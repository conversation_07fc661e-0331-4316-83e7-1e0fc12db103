{"name": "codarashi-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/codarashi-e2e/src", "targets": {"e2e": {"executor": "@nx/playwright:playwright", "outputs": ["{workspaceRoot}/dist/.playwright/apps/codarashi-e2e"], "options": {"config": "apps/codarashi-e2e/playwright.config.ts"}}, "e2e-watch": {"executor": "nx:run-commands", "options": {"command": "npx playwright test --ui", "cwd": "apps/codarashi-e2e"}}, "lint": {"executor": "@nx/eslint:lint"}}, "tags": [], "implicitDependencies": ["co<PERSON><PERSON>"]}