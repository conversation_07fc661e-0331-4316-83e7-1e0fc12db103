import { expect, test } from '@playwright/test';

test('move Summary section down and up again', async ({ page }) => {
  await page.goto('/profile');

  const regions = page.locator('[role="region"]');

  await expect(regions).toHaveCount(2);

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();

  // First, move upwards (since Summary is in 2nd place)
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Move upwards' })
    .click();

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();

  // Then, move downwards
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Move downwards' })
    .click();

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();
});

test('Summary alignment can be changed', async ({ page }) => {
  // Navigate to the profile page
  await page.goto('/profile');

  const summarySection = page.getByRole('region', {
    name: 'Summary section',
  });

  await expect(
    summarySection.locator('[data-items-alignment=center]')
  ).toHaveCount(1);

  await expect(
    summarySection.locator('[data-items-alignment=left]')
  ).toHaveCount(0);

  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Change alignment to left' })
    .click();

  await expect(
    summarySection.locator('[data-items-alignment=left]')
  ).toHaveCount(1);

  await expect(
    summarySection.locator('[data-items-alignment=center]')
  ).toHaveCount(0);

  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Change alignment to center' })
    .click();

  await expect(
    summarySection.locator('[data-items-alignment=center]')
  ).toHaveCount(1);

  await expect(
    summarySection.locator('[data-items-alignment=left]')
  ).toHaveCount(0);
});

test('Summary gap can be changed', async ({ page }) => {
  // Navigate to the profile page
  await page.goto('/profile');

  const summarySection = page.getByRole('region', {
    name: 'Summary section',
  });

  // Initial state - small gap
  await expect(summarySection.locator('[data-items-gap=small]')).toHaveCount(1);

  await expect(summarySection.locator('[data-items-gap=large]')).toHaveCount(0);

  await expect(summarySection.locator('[data-items-gap=medium]')).toHaveCount(
    0
  );

  // Change to medium gap
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(summarySection.locator('[data-items-gap=medium]')).toHaveCount(
    1
  );

  await expect(summarySection.locator('[data-items-gap=small]')).toHaveCount(0);

  await expect(summarySection.locator('[data-items-gap=large]')).toHaveCount(0);

  // Change to large gap
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(summarySection.locator('[data-items-gap=large]')).toHaveCount(1);

  await expect(summarySection.locator('[data-items-gap=medium]')).toHaveCount(
    0
  );

  await expect(summarySection.locator('[data-items-gap=small]')).toHaveCount(0);

  // Change back to small gap
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(summarySection.locator('[data-items-gap=small]')).toHaveCount(1);

  await expect(summarySection.locator('[data-items-gap=large]')).toHaveCount(0);

  await expect(summarySection.locator('[data-items-gap=medium]')).toHaveCount(
    0
  );
});

test('Summary section visibility can be toggled', async ({ page }) => {
  // Navigate to the profile page
  await page.goto('/profile');

  const summarySection = page.getByRole('region', {
    name: 'Summary section',
  });

  // Initial state - visible
  await expect(
    summarySection.locator('[data-section-visible=true]')
  ).toHaveCount(1);

  // Toggle to hide
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Hide section' })
    .click();

  // Verify hidden state
  await expect(
    summarySection.locator('[data-section-visible=false]')
  ).toHaveCount(1);

  // Toggle to show again
  await page.getByRole('button', { name: 'Summary section' }).click();

  await page
    .getByRole('menu', { name: 'Summary section' })
    .getByRole('menuitem', { name: 'Show section' })
    .click();

  // Verify back to visible state
  await expect(
    summarySection.locator('[data-section-visible=true]')
  ).toHaveCount(1);
});
