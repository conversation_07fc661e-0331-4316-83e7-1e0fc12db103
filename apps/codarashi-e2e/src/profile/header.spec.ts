import { expect, test } from '@playwright/test';

test('move Header section down and up again', async ({ page }) => {
  await page.goto('/profile');

  const regions = page.locator('[role="region"]');

  await expect(regions).toHaveCount(2);

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();

  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Move downwards' })
    .click();

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();

  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Move upwards' })
    .click();

  await expect(
    regions.nth(0).getByRole('heading', { name: 'Header', level: 2 })
  ).toBeVisible();
  await expect(
    regions.nth(1).getByRole('heading', { name: 'Summary', level: 2 })
  ).toBeVisible();
});

test('Header alignment can be changed', async ({ page }) => {
  // Navigate to the profile page
  await page.goto('/profile');

  const headerSection = page.getByRole('region', {
    name: 'Header section',
  });

  await expect(
    headerSection.locator('[data-items-alignment=center]')
  ).toHaveCount(2);

  await expect(
    headerSection.locator('[data-items-alignment=left]')
  ).toHaveCount(0);

  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Change alignment to left' })
    .click();

  await expect(
    headerSection.locator('[data-items-alignment=left]')
  ).toHaveCount(2);

  await expect(
    headerSection.locator('[data-items-alignment=center]')
  ).toHaveCount(0);

  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Change alignment to center' })
    .click();

  await expect(
    headerSection.locator('[data-items-alignment=center]')
  ).toHaveCount(2);

  await expect(
    headerSection.locator('[data-items-alignment=left]')
  ).toHaveCount(0);
});

test('Header gap can be changed', async ({ page }) => {
  // Navigate to the profile page
  await page.goto('/profile');

  const headerSection = page.getByRole('region', {
    name: 'Header section',
  });

  // Initial state - small gap
  await expect(headerSection.locator('[data-items-gap=small]')).toHaveCount(2);

  await expect(headerSection.locator('[data-items-gap=large]')).toHaveCount(0);

  await expect(headerSection.locator('[data-items-gap=medium]')).toHaveCount(0);

  // Change to medium gap
  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(headerSection.locator('[data-items-gap=medium]')).toHaveCount(2);

  await expect(headerSection.locator('[data-items-gap=small]')).toHaveCount(0);

  await expect(headerSection.locator('[data-items-gap=large]')).toHaveCount(0);

  // Change to large gap
  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(headerSection.locator('[data-items-gap=large]')).toHaveCount(2);

  await expect(headerSection.locator('[data-items-gap=medium]')).toHaveCount(0);

  await expect(headerSection.locator('[data-items-gap=small]')).toHaveCount(0);

  // Change back to small gap
  await page.getByRole('button', { name: 'Header section' }).click();

  await page
    .getByRole('menu', { name: 'Header section' })
    .getByRole('menuitem', { name: 'Adjust spacing' })
    .click();

  await expect(headerSection.locator('[data-items-gap=small]')).toHaveCount(2);

  await expect(headerSection.locator('[data-items-gap=large]')).toHaveCount(0);

  await expect(headerSection.locator('[data-items-gap=medium]')).toHaveCount(0);
});
