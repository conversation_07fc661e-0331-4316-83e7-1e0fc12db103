import { expect, test } from '@playwright/test';

test.describe('Tag CRUD Operations', () => {
  test('Create a new tag', async ({ page }) => {
    // Navigate to tag creation page
    await page.goto('/profile/settings/tags/create');

    // Fill in tag name
    const tagNameInput = page.getByLabel('Tag name');
    const uniqueTagName = `Test Tag ${Date.now()}`;
    await tagNameInput.fill(uniqueTagName);

    // Submit the form
    const saveButton = page.getByRole('button', { name: 'Save' });
    await saveButton.click();

    // Assert redirection to tags list or success message
    await page.waitForURL('**/profile/settings/tags');

    // Optional: Verify the tag was created
    const createdTag = page.getByText(uniqueTagName);
    await expect(createdTag).toBeVisible();
  });

  // TODO: see why the test fails

  // test('Prevent creating a duplicate tag', async ({ page }) => {
  //   // Navigate to tag creation page and create an initial tag
  //   await page.goto('/profile/settings/tags/create');

  //   // Create a unique tag name
  //   const duplicateTagName = `Duplicate Tag ${Date.now()}`;

  //   // Fill in tag name
  //   const tagNameInput = page.getByLabel('Tag name');
  //   await tagNameInput.fill(duplicateTagName);

  //   // Submit the form to create the first tag
  //   const saveButton = page.getByRole('button', { name: 'Save' });
  //   await saveButton.click();

  //   // Wait for redirection after first tag creation
  //   await page.waitForURL('**/profile/settings/tags');

  //   // Navigate back to tag creation page
  //   await page.goto('/profile/settings/tags/create');

  //   // Try to create the same tag again
  //   await tagNameInput.fill(duplicateTagName);

  //   // Assert error message for duplicate tag
  //   const errorMessage = page.getByRole('status', {
  //     name: 'Tag already exists',
  //   });
  //   await expect(errorMessage).toBeVisible();
  // });

  test('Cancel tag creation', async ({ page }) => {
    // Navigate to tag creation page
    await page.goto('/profile/settings/tags/create');

    // Click cancel button
    const cancelLink = page.getByRole('link', { name: 'Cancel' });
    await cancelLink.click();

    // Assert redirection to tags list
    await page.waitForURL('**/profile/settings/tags');
    await expect(page).toHaveURL('/profile/settings/tags');
  });
});
