import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import { migrate } from 'drizzle-orm/libsql/migrator';
import { join } from 'path';

const testDbUrl = join(__dirname, '../codarashi/db/test-e2e.db');

const createDb = () => {
  const client = createClient({
    url: `file:${testDbUrl}`,
    authToken: '',
  });

  const db = drizzle(client);

  return db;
};

export const runMigrations = async () => {
  console.log('Running migrations...');

  const db = createDb();

  await migrate(db, {
    migrationsFolder: join(__dirname, '../codarashi/db/migrations'),
  });

  console.log('Migrations completed');
};

export const cleanDatabase = async () => {
  const db = createDb();

  const tables = await db.$client.execute(`
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE '__drizzle_%';
  `);

  // Disable foreign key checks temporarily
  await db.$client.execute('PRAGMA foreign_keys=OFF;');

  try {
    // Delete data from each table
    for (const { name } of tables.rows) {
      await db.$client.execute(`DELETE FROM "${name}";`);
    }
  } finally {
    // Re-enable foreign key checks
    await db.$client.execute('PRAGMA foreign_keys=ON;');
  }
};
