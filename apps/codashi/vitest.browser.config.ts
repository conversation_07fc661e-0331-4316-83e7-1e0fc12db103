/// <reference types='vitest' />
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/codashi',

  plugins: [react(), nxViteTsPaths()],

  test: {
    setupFiles: ['./test-setup.ts'],
    globals: true,
    include: ['app/**/*.browser.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],

    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../coverage/apps/codashi',
      provider: 'v8',
    },

    // Browser mode configuration (compatible with Vitest 3.x)
    browser: {
      enabled: true,
      provider: 'playwright',
      headless: true,
      api: {
        port: 63315, // Use a different port to avoid conflicts
      },
      instances: [
        {
          browser: 'chromium',
        },
      ],
    },

    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
  },
});
