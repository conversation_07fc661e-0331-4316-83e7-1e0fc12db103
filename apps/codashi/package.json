{"name": "codashi", "private": true, "type": "module", "scripts": {"build": "react-router build", "cf-typegen": "wrangler types", "deploy": "npm run build && wrangler deploy", "dev": "react-router dev", "postinstall": "npm run cf-typegen", "preview": "npm run build && vite preview", "typecheck": "npm run cf-typegen && react-router typegen && tsc -b"}, "dependencies": {"isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.0.12", "@react-router/dev": "^7.5.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.5.2", "@vitest/browser": "^3.2.3", "playwright": "^1.53.0", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.19.1"}}