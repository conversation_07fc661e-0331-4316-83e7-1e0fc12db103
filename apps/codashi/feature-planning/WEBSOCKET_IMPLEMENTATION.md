# WebSocket Implementation Plan for Profile Extraction

This document outlines the step-by-step plan to implement WebSocket-based streaming for the profile extraction feature, including human-in-the-loop confirmations.

## Table of Contents

1. [Phase 1: Basic WebSocket Setup](#phase-1-basic-websocket-setup)
2. [Phase 2: Streaming Response Implementation](#phase-2-streaming-response-implementation)
3. [Phase 3: Human-in-the-Loop Integration](#phase-3-human-in-the-loop-integration)
4. [Phase 4: Error Handling & Edge Cases](#phase-4-error-handling--edge-cases)
5. [Phase 5: Testing & Optimization](#phase-5-testing--optimization)

## Phase 1: Basic WebSocket Setup

### 1.1 Create WebSocket Route

- [ ] Create new route file: `app/routes/api.profile.ws.tsx`
- [ ] Implement basic WebSocket handshake
- [ ] Test basic connection establishment

```typescript
// app/routes/api.profile.ws.tsx
export async function action({ request }: { request: Request }) {
  const [client, server] = Object.values(new WebSocketPair());

  server.accept();

  // Basic echo test
  server.addEventListener('message', (event) => {
    server.send(`Echo: ${event.data}`);
  });

  return new Response(null, {
    status: 101,
    webSocket: client,
  });
}
```

### 1.2 Update Router Configuration

- [ ] Add WebSocket route to `app/routes.ts`
- [ ] Configure CORS if needed

### 1.3 Create WebSocket Client Hook

- [ ] Create `useProfileWebSocket` hook
- [ ] Implement basic connection management
- [ ] Add connection status tracking

```typescript
// app/hooks/useProfileWebSocket.ts
export function useProfileWebSocket() {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [status, setStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  const connect = useCallback(() => {
    const ws = new WebSocket('wss://your-worker-url/api/profile.ws');
    setStatus('connecting');

    ws.onopen = () => setStatus('connected');
    ws.onclose = () => setStatus('disconnected');

    setSocket(ws);
    return () => ws.close();
  }, []);

  return { socket, status, connect };
}
```

## Phase 2: Streaming Response Implementation

### 2.1 Implement Basic Streaming

- [ ] Update WebSocket handler to process profile extraction
- [ ] Stream chunks of data as they become available

```typescript
// In api.profile.ws.ts
server.addEventListener('message', async (event) => {
  try {
    const { content } = JSON.parse(event.data);

    for await (const chunk of extractProfileStream(content)) {
      server.send(
        JSON.stringify({
          type: 'chunk',
          data: chunk,
        })
      );
    }

    server.send(JSON.stringify({ type: 'complete' }));
    server.close(1000, 'Processing complete');
  } catch (error) {
    // Error handling
  }
});
```

### 2.2 Update Frontend to Handle Stream

- [ ] Modify `ProfileEditor` to use WebSocket
- [ ] Implement chunk processing
- [ ] Add loading states

```typescript
// In ProfileEditor component
useEffect(() => {
  if (!socket || status !== 'connected') return;

  socket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'chunk') {
      setPartialProfile((prev) => ({
        ...prev,
        ...data.data,
      }));
    } else if (data.type === 'complete') {
      setIsComplete(true);
    }
  };

  // Send initial content
  socket.send(JSON.stringify({ content: initialValue }));

  return () => {
    socket.onmessage = null;
  };
}, [socket, status]);
```

## Phase 3: Human-in-the-Loop Integration

### 3.1 Implement Confirmation Requests

- [ ] Add confirmation points in the extraction flow
- [ ] Pause processing until confirmation

```typescript
// In api.profile.ws.ts
for await (const chunk of extractProfileStream(content)) {
  if (chunk.needsConfirmation) {
    // Send confirmation request
    const confirmationId = generateId();
    server.send(
      JSON.stringify({
        type: 'confirmation',
        id: confirmationId,
        message: 'Is this information correct?',
        data: chunk.data,
      })
    );

    // Wait for response
    const response = await new Promise((resolve) => {
      const listener = (msg: MessageEvent) => {
        const data = JSON.parse(msg.data);
        if (data.type === 'confirmation_response' && data.id === confirmationId) {
          resolve(data.confirmed);
        }
      };
      server.addEventListener('message', listener);
    });

    // Process response
    if (!response) {
      // Handle rejection
      continue;
    }
  }

  // Process normal chunk
  server.send(
    JSON.stringify({
      type: 'chunk',
      data: chunk.data,
    })
  );
}
```

### 3.2 Add Confirmation UI

- [ ] Create `ConfirmationDialog` component
- [ ] Implement confirmation state management
- [ ] Add styles and animations

## Phase 4: Error Handling & Edge Cases

### 4.1 Implement Error Handling

- [ ] Add timeout for WebSocket connections
- [ ] Handle reconnection logic
- [ ] Add error boundaries

### 4.2 Handle Disconnections

- [ ] Implement reconnection strategy
- [ ] Add offline indicators
- [ ] Handle partial data on disconnect

## Phase 5: Testing & Optimization

### 5.1 Unit Testing

- [ ] Test WebSocket connection handling
- [ ] Test chunk processing
- [ ] Test confirmation flow

### 5.2 Performance Optimization

- [ ] Implement chunk size optimization
- [ ] Add rate limiting
- [ ] Monitor WebSocket performance

## Next Steps

1. Start with Phase 1 and verify basic WebSocket connectivity
2. Implement Phase 2 for basic streaming
3. Add human-in-the-loop confirmations in Phase 3
4. Implement robust error handling in Phase 4
5. Complete testing and optimization in Phase 5

## LangGraph Cloud Integration Analysis

We evaluated using `@langchain/langgraph-sdk` for our WebSocket implementation but decided against it for the following reasons:

1. **Commercial Product**:

   - LangGraph Cloud is a commercial product with a free tier limited to 1 million nodes/year
   - Requires a LangSmith Developer account (free tier: 5k trace executions/month)
   - Pricing scales with usage: [LangGraph Pricing](https://www.langchain.com/pricing-langgraph-platform)

2. **Architectural Coupling**:

   - The SDK is tightly coupled with LangGraph Cloud's API and message formats
   - Requires specific server-side infrastructure (PostgreSQL, Redis)
   - Uses a web-queue-worker architecture that adds complexity

3. **Limited Flexibility**:

   - Designed for chat-like interactions with specific message formats
   - Less control over the streaming protocol (uses HTTP streaming, not WebSockets)
   - More challenging to implement custom features like our human-in-the-loop confirmations

4. **Self-Hosting Limitations**:
   - Self-hosted deployment requires additional infrastructure
   - Still requires LangSmith for monitoring and tracing
   - More complex deployment than our WebSocket solution

## Dependencies

- `@awe/codashi-core` - For profile extraction logic
- React 18+ - For hooks and state management
- TypeScript - For type safety
- WebSocket API - For real-time communication

## Notes

- Ensure proper cleanup of WebSocket connections
- Implement proper error boundaries
- Consider adding analytics for monitoring
- Add logging for debugging
