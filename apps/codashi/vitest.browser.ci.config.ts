/// <reference types='vitest' />
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/codashi',

  plugins: [react(), nxViteTsPaths()],

  test: {
    setupFiles: ['./test-setup.ts'],
    globals: true,
    include: ['app/**/*.browser.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],

    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../coverage/apps/codashi',
      provider: 'v8',
    },

    // CI-optimized browser configuration
    browser: {
      enabled: true,
      provider: 'playwright',
      headless: true,
      api: {
        port: 63315,
      },
      instances: [
        {
          browser: 'chromium',
        },
      ],
    },

    // CI-optimized test settings
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
        isolate: false,
      },
    },

    // Longer timeouts for CI
    testTimeout: 30000,
    hookTimeout: 30000,

    // Retry failed tests in CI
    retry: process.env.CI ? 2 : 0,
  },
});
