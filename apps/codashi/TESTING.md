# Testing Setup for Codashi

This document describes the testing setup for the Codashi project.

## Overview

The project uses Vitest for testing with the following configuration:

- **Test Runner**: Vitest 1.6.1
- **Test Environment**: jsdom
- **Testing Library**: @testing-library/react
- **Assertion Library**: Vitest with @testing-library/jest-dom matchers

## Available Commands

### Run Tests

```bash
# Run unit/integration tests (jsdom) - excludes browser tests
npx nx test codashi

# Run unit tests in watch mode
npx nx test:watch codashi

# Run browser tests (real Chromium browser)
npx nx test:browser codashi

# Run browser tests in watch mode
npx nx test:browser:watch codashi

# Run browser tests (CI-optimized)
npx nx test:browser:ci codashi

# Run tests from the root workspace
npm run test  # runs all projects
```

### Test Coverage

```bash
# Run unit tests with coverage
npx nx test codashi --coverage

# Run browser tests with coverage
npx nx test:browser codashi --coverage
```

## Configuration Files

- `vitest.config.ts` - Main Vitest configuration
- `test-setup.ts` - Test setup file with jest-dom matchers
- `tsconfig.spec.json` - TypeScript configuration for tests

## Browser Mode (Optional)

The configuration includes commented browser mode setup. To enable browser testing:

1. Uncomment the browser configuration in `vitest.config.ts`
2. Install playwright: `npm install --save-dev @vitest/browser playwright`
3. Run tests with: `npx nx test codashi --browser`

## Test File Naming Convention

Tests should be placed alongside the code they test with the following naming convention:

### Unit/Integration Tests (jsdom)

- `*.test.ts` or `*.test.tsx` - Unit and integration tests that run in jsdom
- `*.spec.ts` or `*.spec.tsx` - Specification files that run in jsdom

### Browser Tests (real browser)

- `*.browser.test.ts` or `*.browser.test.tsx` - Tests that run in real Chromium browser
- `*.browser.spec.ts` or `*.browser.spec.tsx` - Browser specification files

## Writing Tests

### Unit Test Example (jsdom)

```typescript
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Hello World')).toBeTruthy();
  });
});
```

### Browser Test Example (real browser)

```typescript
// MyComponent.browser.test.tsx
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { MyComponent } from './MyComponent';

describe('MyComponent Browser Tests', () => {
  it('works in real browser environment', () => {
    render(<MyComponent />);

    // Test browser-specific functionality
    expect(typeof window).toBe('object');
    expect('localStorage' in window).toBe(true);
    expect(screen.getByText('Hello World')).toBeTruthy();
  });
});
```

## React Router Testing

Since this project uses React Router v7, components that use router hooks need to be wrapped in router context or mocked. See `app/root.test.tsx` for examples of mocking React Router components.

## CI/CD Integration

For running tests in CI environments, see [CI-TESTING.md](./CI-TESTING.md) for detailed setup instructions.

Quick CI commands:

```bash
# Unit tests (fast, no browser dependencies)
npx nx test codashi

# Browser tests (CI-optimized with retries and longer timeouts)
npx nx test:browser:ci codashi
```

## Notes

- Tests run in single-threaded mode for consistency
- Coverage reports are generated in `../../coverage/apps/codashi`
- The setup includes TypeScript support for test files
- Jest-DOM matchers are available for enhanced assertions
- Browser tests require Playwright and Chromium installation
- CI environments need additional system dependencies (see CI-TESTING.md)
