{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["vitest/globals", "vitest/importMeta", "vite/client", "node", "vitest", "@testing-library/jest-dom"]}, "include": ["vite.config.ts", "vitest.config.ts", "app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "app/**/*.test.ts", "app/**/*.spec.ts", "app/**/*.test.tsx", "app/**/*.spec.tsx", "app/**/*.test.js", "app/**/*.spec.js", "app/**/*.test.jsx", "app/**/*.spec.jsx", "test-setup.ts"]}