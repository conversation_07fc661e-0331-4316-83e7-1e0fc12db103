import { createRequestHand<PERSON> } from 'react-router';

import { type Ai } from '@cloudflare/ai';

declare module 'react-router' {
  interface Env {
    AI: Ai;
  }
  export interface AppLoadContext {
    cloudflare: {
      env: Env;
      ctx: ExecutionContext;
    };
  }
}

const requestHandler = createRequestHandler(
  () => import('virtual:react-router/server-build'),
  import.meta.env.MODE
);

export default {
  async fetch(request, env, ctx) {
    return requestHandler(request, {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      cloudflare: { env: env as any, ctx },
    });
  },
} satisfies ExportedHandler<Env>;
