/// <reference types='vitest' />
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/codashi',

  plugins: [react(), nxViteTsPaths()],

  test: {
    setupFiles: ['./test-setup.ts'],
    globals: true,
    environment: 'jsdom',
    include: ['app/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['app/**/*.browser.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    passWithNoTests: true,

    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../coverage/apps/codashi',
      provider: 'v8',
    },

    // Browser mode configuration (disabled for now)
    // browser: {
    //   enabled: true,
    //   name: 'chromium',
    //   provider: 'playwright',
    //   headless: true,
    //   api: {
    //     port: 5173,
    //     host: 'localhost',
    //   },
    // },

    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
  },
});
