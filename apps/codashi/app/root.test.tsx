import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { ErrorBoundary } from './root';

// Mock React Router components since they require router context
vi.mock('react-router', () => ({
  Links: () => <div data-testid="links" />,
  Meta: () => <div data-testid="meta" />,
  Scripts: () => <div data-testid="scripts" />,
  ScrollRestoration: () => <div data-testid="scroll-restoration" />,
  isRouteErrorResponse: (error: any) =>
    error && typeof error.status === 'number',
}));

describe('ErrorBoundary', () => {
  it('renders 404 error correctly', () => {
    const error = {
      status: 404,
      statusText: 'Not Found',
      data: null,
    };

    const props = { error, params: {} } as any;
    render(<ErrorBoundary {...props} />);

    expect(screen.getByText('404')).toBeTruthy();
    expect(
      screen.getByText('The requested page could not be found.')
    ).toBeTruthy();
  });

  it('renders generic error correctly', () => {
    const error = {
      status: 500,
      statusText: 'Internal Server Error',
      data: null,
    };

    const props = { error, params: {} } as any;
    render(<ErrorBoundary {...props} />);

    expect(screen.getByText('Error')).toBeTruthy();
    expect(screen.getByText('Internal Server Error')).toBeTruthy();
  });

  it('renders unexpected error correctly', () => {
    const error = new Error('Something went wrong');

    // Mock import.meta.env.DEV to be true
    Object.defineProperty(import.meta, 'env', {
      value: { DEV: true },
      writable: true,
    });

    const props = { error, params: {} } as any;
    render(<ErrorBoundary {...props} />);

    expect(screen.getByText('Oops!')).toBeTruthy();
    expect(screen.getByText('Something went wrong')).toBeTruthy();
  });
});
