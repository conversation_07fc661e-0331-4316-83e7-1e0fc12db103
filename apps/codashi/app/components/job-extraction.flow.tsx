import { type FC, useEffect, useState } from 'react';

import {
  type JobDraft,
  type JobExtractionStreamEvent,
} from '@awe/codashi-core';

// Helper to give a friendly name to section keys
const sectionDisplayNames: Record<string, string> = {
  basic_info: 'Basic Info',
  company_info: 'Company Info',
  job_details: 'Job Details',
  compensation: 'Compensation',
  location: 'Location',
  skills: 'Skills',
  experience: 'Experience',
  responsibilities: 'Responsibilities',
  qualifications: 'Qualifications',
  benefits: 'Benefits',
};

type SectionStatus = 'pending' | 'extracting' | 'done' | 'error';

type SectionState = {
  name: string;
  status: SectionStatus;
};

export const JobExtractionFlow: FC<{
  jobText: string;
  onJobReady: (job: JobDraft) => void;
}> = ({ jobText, onJobReady }) => {
  const [sections, setSections] = useState<SectionState[]>([]);
  const [finalJob, setFinalJob] = useState<JobDraft | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    const abortController = new AbortController();

    const processStream = async () => {
      setIsLoading(true);
      setError(null);
      setSections([]);
      setFinalJob(null);

      try {
        const formData = new FormData();
        formData.append('jobText', jobText);

        const response = await fetch('/api/job/extract', {
          signal: abortController.signal,
          method: 'POST',
          body: formData,
        });

        if (!response.ok || !response.body) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (isMounted) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '' || !isMounted) continue;

            try {
              const event = JSON.parse(line) as
                | JobExtractionStreamEvent
                | { type: 'error'; message: string };

              if (event.type === 'sections_identified') {
                setSections(
                  event.sections.map((s) => ({
                    name: s.name,
                    status: 'extracting',
                  }))
                );
              } else if (event.type === 'extraction_result') {
                setSections((prev) =>
                  prev.map((s) =>
                    s.name === event.section ? { ...s, status: 'done' } : s
                  )
                );
              } else if (event.type === 'final_result') {
                setFinalJob(event.job);
                onJobReady(event.job);

                setSections((prev) =>
                  prev.map((s) => ({ ...s, status: 'done' }))
                );
              } else if (event.type === 'error') {
                setError(event.message);
              }
            } catch (e) {
              console.error('Error parsing chunk:', e, 'Line:', line);
            }
          }
        }
      } catch (err) {
        if (err instanceof Error && err.name !== 'AbortError' && isMounted) {
          console.error('Error fetching stream:', err);
          setError(err.message);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    processStream();

    return () => {
      isMounted = false;
      abortController.abort();
    };
  }, [jobText, onJobReady]);

  const renderStatusIcon = (status: SectionStatus) => {
    switch (status) {
      case 'extracting':
        return <span className="animate-spin">⚙️</span>;
      case 'done':
        return <span className="text-green-500">✅</span>;
      case 'error':
        return <span className="text-red-500">❌</span>;
      default:
        return <span></span>;
    }
  };

  return (
    <div className="p-4 max-w-2xl mx-auto font-sans">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">
        Extracting Job Information...
      </h1>

      {isLoading && sections.length === 0 && (
        <div className="text-center py-4 text-gray-500">
          Identifying sections in your job description...
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      {sections.length > 0 && (
        <div className="space-y-2">
          {sections.map((section) => (
            <div
              key={section.name}
              className="p-3 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-between gap-2"
            >
              <span className="text-gray-700">
                {sectionDisplayNames[section.name] || section.name}
              </span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 capitalize">
                  {section.status}
                </span>
                {renderStatusIcon(section.status)}
              </div>
            </div>
          ))}
        </div>
      )}

      {!isLoading && finalJob && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h2 className="text-lg font-semibold text-green-800">
            Extraction Complete!
          </h2>
        </div>
      )}
    </div>
  );
};
