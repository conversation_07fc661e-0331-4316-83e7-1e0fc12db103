import { useState } from 'react';

import { extractTextWithFallback, type Resume } from '@awe/codashi-core';
import { ProfileExtractionFlow } from '@self/components/profile.flow';

interface ResumeSectionProps {
  onProfileReady: (profile: Resume) => void;
}

interface ResumeState {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
}

export function ResumeSection({ onProfileReady }: ResumeSectionProps) {
  const [state, setState] = useState<ResumeState>({
    text: null,
    error: null,
    isProcessing: false,
  });

  const resetState = () => {
    setState({
      text: null,
      error: null,
      isProcessing: false,
    });
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      resetState();
      return;
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      setState({
        text: null,
        error: 'Please upload a PDF file',
        isProcessing: false,
      });
      return;
    }

    // Validate file size
    if (file.size === 0) {
      setState({
        text: null,
        error: 'Please upload a PDF file',
        isProcessing: false,
      });
      return;
    }

    // Start processing
    setState({
      text: null,
      error: null,
      isProcessing: true,
    });

    try {
      const { content: text } = await extractTextWithFallback(file);

      if (!text || text.trim().length === 0) {
        setState({
          text: null,
          error:
            'No text could be extracted from the PDF. Please try another file.',
          isProcessing: false,
        });
        return;
      }

      setState({
        text,
        error: null,
        isProcessing: false,
      });
    } catch (error) {
      setState({
        text: null,
        error: 'Failed to process PDF. Please try another file.',
        isProcessing: false,
      });
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Resume Upload</h2>

      <div className="flex flex-col gap-2">
        <label htmlFor="resume" className="text-lg font-medium">
          Upload your resume (PDF)
        </label>
        <input
          id="resume"
          name="resume"
          type="file"
          accept="application/pdf"
          onChange={handleFileChange}
          disabled={state.isProcessing}
          className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          required
        />
      </div>

      {state.isProcessing && (
        <div className="p-4 bg-blue-50 text-blue-700 rounded-md">
          Processing PDF... This may take a moment.
        </div>
      )}

      {state.error && (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          {state.error}
        </div>
      )}

      {state.text && (
        <ProfileExtractionFlow
          resumeText={state.text}
          onProfileReady={onProfileReady}
        />
      )}
    </div>
  );
}
