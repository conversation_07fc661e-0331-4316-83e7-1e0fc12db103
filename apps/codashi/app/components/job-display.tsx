import { type FC } from 'react';

import { draftToJob, type JobDraft } from '@awe/codashi-core';

interface JobDisplayProps {
  job: JobDraft;
}

export const JobDisplay: FC<JobDisplayProps> = ({ job }) => {
  // Handle malformed job data
  if (!job || typeof job !== 'object') {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <h2 className="text-lg font-semibold text-red-800 mb-2">
          Error: Invalid Job Data
        </h2>
        <p className="text-red-700">
          The job data appears to be malformed or missing. Please try extracting
          the job description again.
        </p>
      </div>
    );
  }

  try {
    // Format Job data as readable JSON with proper indentation
    const formattedJobData = JSON.stringify(draftToJob(job), null, 2);

    return (
      <div className="flex flex-col gap-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Extracted Job Information
        </h2>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-auto max-h-96">
            {formattedJobData}
          </pre>
        </div>

        <div className="text-sm text-gray-500">
          <p>
            This is the structured data extracted from the job description. Each
            field includes confidence scores and can be reviewed for accuracy.
          </p>
        </div>
      </div>
    );
  } catch (error) {
    // Handle JSON serialization errors
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <h2 className="text-lg font-semibold text-red-800 mb-2">
          Error: Unable to Display Job Data
        </h2>
        <p className="text-red-700">
          There was an error formatting the job data for display. The data may
          contain circular references or other serialization issues.
        </p>
        <details className="mt-2">
          <summary className="cursor-pointer text-red-600 hover:text-red-800">
            Technical Details
          </summary>
          <pre className="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded">
            {error instanceof Error ? error.message : 'Unknown error'}
          </pre>
        </details>
      </div>
    );
  }
};
