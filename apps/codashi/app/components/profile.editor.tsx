import { type FC, useEffect, useState } from 'react';
import { useFetcher } from 'react-router';

import { yamlToResume, type YamlValidationError } from '@awe/codashi-core';
import { CodeEditor } from '@awe/codashi-ui';
import type { ActionResponse } from '@self/routes/api.profile';

// The response type is now inferred from the action's return type

export const ProfileEditor: FC<{
  initialValue: string;
}> = ({ initialValue }) => {
  const [yamlContent, setYamlContent] = useState('');
  const [diagnostics, setDiagnostics] = useState<YamlValidationError[]>([]);
  const fetcher = useFetcher<ActionResponse>();
  const isLoading =
    fetcher.state === 'submitting' || fetcher.state === 'loading';
  const error = fetcher.data?.error || null;

  useEffect(() => {
    if (initialValue && !yamlContent && !isLoading && !fetcher.data) {
      const formData = new FormData();
      formData.append('content', initialValue);

      fetcher.submit(formData, {
        method: 'POST',
        action: '/api/profile',
        encType: 'multipart/form-data',
      });
    }
  }, [initialValue, yamlContent, isLoading, fetcher]);

  // Update yamlContent when data is loaded
  useEffect(() => {
    if (fetcher.data?.profile && !yamlContent) {
      setYamlContent(JSON.stringify(fetcher.data.profile, null, 2));
    }
  }, [fetcher.data, yamlContent]);

  // Handle loading and error states
  if (isLoading) {
    return <div>Loading profile data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <CodeEditor
      value={yamlContent}
      onChange={(value) => {
        try {
          const parsed = yamlToResume(value);
          setDiagnostics(parsed.success ? [] : parsed.errors);
          setYamlContent(value);
        } catch (err) {
          console.error('Error parsing YAML:', err);
        }
      }}
      diagnostics={diagnostics}
    />
  );
};
