import { useState } from 'react';

import { extractTextWithFallback, type JobDraft } from '@awe/codashi-core';
import { JobExtractionFlow } from '@self/components/job-extraction.flow';

interface JobSectionProps {
  onJobReady: (job: JobDraft) => void;
}

type JobInputMethod = 'pdf' | 'text';

interface JobState {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
  inputMethod: JobInputMethod;
  textInput: string;
}

export function JobSection({ onJobReady }: JobSectionProps) {
  const [state, setState] = useState<JobState>({
    text: null,
    error: null,
    isProcessing: false,
    inputMethod: 'pdf',
    textInput: '',
  });

  const resetState = () => {
    setState((prev) => ({
      ...prev,
      text: null,
      error: null,
      isProcessing: false,
    }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      resetState();
      return;
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      setState((prev) => ({
        ...prev,
        text: null,
        error: 'Please upload a valid PDF file',
        isProcessing: false,
      }));
      return;
    }

    // Validate file size
    if (file.size === 0) {
      setState((prev) => ({
        ...prev,
        text: null,
        error: 'Please upload a PDF file',
        isProcessing: false,
      }));
      return;
    }

    // Start processing
    setState((prev) => ({
      ...prev,
      text: null,
      error: null,
      isProcessing: true,
    }));

    try {
      const { content: text } = await extractTextWithFallback(file);

      if (!text || text.trim().length === 0) {
        setState((prev) => ({
          ...prev,
          text: null,
          error:
            'Could not extract text from PDF. Please try another file or use text input.',
          isProcessing: false,
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        text,
        error: null,
        isProcessing: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        text: null,
        error:
          'Failed to process PDF. Please try another file or use text input.',
        isProcessing: false,
      }));
    }
  };

  const handleTextSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!state.textInput.trim()) {
      return;
    }

    setState((prev) => ({
      ...prev,
      text: state.textInput.trim(),
      error: null,
      isProcessing: false,
    }));
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setState((prev) => ({
      ...prev,
      textInput: e.target.value,
    }));
  };

  const handleInputMethodChange = (method: JobInputMethod) => {
    setState((prev) => ({
      ...prev,
      inputMethod: method,
      text: null,
      error: null,
      isProcessing: false,
    }));
  };

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Job Description Input</h2>

      {/* Input Method Selection */}
      <div className="flex gap-4 mb-4">
        <label className="flex items-center gap-2">
          <input
            type="radio"
            name="inputMethod"
            value="pdf"
            checked={state.inputMethod === 'pdf'}
            onChange={(e) =>
              handleInputMethodChange(e.target.value as JobInputMethod)
            }
            className="text-blue-600"
          />
          <span>Upload PDF</span>
        </label>
        <label className="flex items-center gap-2">
          <input
            type="radio"
            name="inputMethod"
            value="text"
            checked={state.inputMethod === 'text'}
            onChange={(e) =>
              handleInputMethodChange(e.target.value as JobInputMethod)
            }
            className="text-blue-600"
          />
          <span>Paste Text</span>
        </label>
      </div>

      {/* PDF Upload Section */}
      {state.inputMethod === 'pdf' && (
        <div className="flex flex-col gap-2">
          <label htmlFor="jobPdf" className="text-lg font-medium">
            Upload job description (PDF)
          </label>
          <input
            id="jobPdf"
            name="jobPdf"
            type="file"
            accept="application/pdf"
            onChange={handleFileChange}
            disabled={state.isProcessing}
            className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
            required
          />
        </div>
      )}

      {/* Text Input Section */}
      {state.inputMethod === 'text' && (
        <form onSubmit={handleTextSubmit}>
          <div className="flex flex-col gap-2">
            <label htmlFor="jobText" className="text-lg font-medium">
              Paste job description text
            </label>
            <textarea
              id="jobText"
              name="jobText"
              value={state.textInput}
              autoFocus
              onChange={handleTextChange}
              placeholder="Paste the job description here..."
              className="w-full h-40 p-3 border border-gray-300 rounded-md resize-vertical focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              minLength={50}
              maxLength={50000}
            />
            <div className="flex justify-between items-center text-sm text-gray-500">
              <span>
                {state.textInput.length} / 50,000 characters
                {state.textInput.length > 0 && state.textInput.length < 50 && (
                  <span className="text-red-500 ml-2">
                    (minimum 50 characters)
                  </span>
                )}
              </span>
              <button
                type="submit"
                disabled={
                  !state.textInput.trim() || state.textInput.length < 50
                }
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Process Text
              </button>
            </div>
          </div>
        </form>
      )}

      {/* Loading State for PDF Processing */}
      {state.isProcessing && (
        <div className="p-4 bg-blue-50 text-blue-700 rounded-md">
          Processing PDF... This may take a moment.
        </div>
      )}

      {/* Error Display - PDF Processing Errors */}
      {state.error && (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          {state.error}
        </div>
      )}

      {/* Job Extraction Flow - PDF Processing Results */}
      {state.text && (
        <JobExtractionFlow jobText={state.text} onJobReady={onJobReady} />
      )}
    </div>
  );
}
