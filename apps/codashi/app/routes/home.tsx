import { useState } from 'react';
import type { Route } from './+types/home';

import { type JobDraft, type Resume } from '@awe/codashi-core';
import { ResumePdfPreview } from '@awe/codashi-ui';
import { JobDisplay } from '@self/components/job-display';
import { JobSection } from '@self/components/job.section';
import { ResumeSection } from '@self/components/resume.section';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function meta(_: Route.MetaArgs) {
  return [
    { title: 'Resume & Job Parser' },
    {
      name: 'description',
      content: 'Upload and parse your resume and job descriptions',
    },
  ];
}

export function loader({ context }: Route.LoaderArgs) {
  return { message: context.cloudflare.env.AI };
}

export default function Home() {
  const [profile, setProfile] = useState<Resume | null>(null);
  const [job, setJob] = useState<JobDraft | null>(null);

  return (
    <main className="grid-container p-4 full-height">
      <section className="grid-span-7 flex flex-col gap-8">
        <h1 className="text-2xl font-bold">Resume & Job Parser</h1>
        <ResumeSection onProfileReady={setProfile} />
        <JobSection onJobReady={setJob} />
      </section>

      <section className="grid-span-5">
        {profile && <ResumePdfPreview profile={profile} />}
        {job && <JobDisplay job={job} />}
      </section>
    </main>
  );
}
