import { ChatMistralAI } from '@langchain/mistralai';

import { extractJobStream } from '@awe/codashi-core';
import type { Route } from '../../+types/root';

export const action = async ({ request }: Route.ActionArgs) => {
  const body = await request.formData();
  const jobText = body.get('jobText');

  if (!jobText || typeof jobText !== 'string') {
    return new Response('Invalid job text', { status: 400 });
  }

  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const model = new ChatMistralAI({
          modelName: 'mistral-small-latest',
          apiKey: import.meta.env.VITE_MISTRAL_API_KEY,
          maxTokens: 8096,
        });

        const stream = extractJobStream(jobText, model);

        for await (const event of stream) {
          controller.enqueue(encoder.encode(JSON.stringify(event) + '\n'));
        }
      } catch (error) {
        console.error('Error in stream:', error);
        // Optionally, send an error event to the client
        controller.enqueue(
          encoder.encode(
            JSON.stringify({
              type: 'error',
              message: 'An error occurred during extraction.',
            }) + '\n'
          )
        );
      } finally {
        controller.close();
      }
    },
    cancel(ctrl) {
      ctrl.close();
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'application/x-ndjson',
      'Transfer-Encoding': 'chunked',
      'Cache-Control': 'no-cache',
    },
  });
};
