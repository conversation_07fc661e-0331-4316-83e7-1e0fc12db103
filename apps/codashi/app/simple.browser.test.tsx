import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

// Simple component for testing browser mode
function SimpleComponent({ message }: { message: string }) {
  return <div data-testid="message">{message}</div>;
}

describe('Simple Browser Test', () => {
  it('renders a simple component', () => {
    render(<SimpleComponent message="Hello Browser Mode!" />);

    expect(screen.getByTestId('message')).toBeTruthy();
    expect(screen.getByText('Hello Browser Mode!')).toBeTruthy();
  });

  it('can interact with DOM elements', () => {
    render(
      <div>
        <button data-testid="click-me">Click me</button>
        <span data-testid="counter">0</span>
      </div>
    );

    const button = screen.getByTestId('click-me');
    const counter = screen.getByTestId('counter');

    expect(button).toBeTruthy();
    expect(counter).toBeTruthy();
    expect(counter.textContent).toBe('0');
  });

  it('verifies browser environment', () => {
    // This test verifies we're running in a real browser environment
    expect(typeof window).toBe('object');
    expect(typeof document).toBe('object');
    expect(typeof navigator).toBe('object');

    // Check for browser-specific APIs
    expect('localStorage' in window).toBe(true);
    expect('sessionStorage' in window).toBe(true);
  });
});
