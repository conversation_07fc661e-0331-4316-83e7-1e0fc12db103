/* Reset and base styles */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', ui-sans-serif, system-ui, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  background-color: white;
  color: #1f2937;
  line-height: 1.5;
}

@media (prefers-color-scheme: dark) {
  html,
  body {
    background-color: #030712;
    color: #f9fafb;
    color-scheme: dark;
    height: 100%;
  }
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Gap utilities - 4px increments */
.gap-0 {
  gap: 0;
} /* 0px */
.gap-1 {
  gap: 0.25rem;
} /* 4px */
.gap-2 {
  gap: 0.5rem;
} /* 8px */
.gap-3 {
  gap: 0.75rem;
} /* 12px */
.gap-4 {
  gap: 1rem;
} /* 16px */
.gap-5 {
  gap: 1.25rem;
} /* 20px */
.gap-6 {
  gap: 1.5rem;
} /* 24px */
.gap-8 {
  gap: 2rem;
} /* 32px */
.gap-10 {
  gap: 2.5rem;
} /* 40px */
.gap-12 {
  gap: 3rem;
} /* 48px */
.gap-16 {
  gap: 4rem;
} /* 64px */
.gap-20 {
  gap: 5rem;
} /* 80px */
.gap-24 {
  gap: 6rem;
} /* 96px */
.gap-32 {
  gap: 8rem;
} /* 128px */
.gap-40 {
  gap: 10rem;
} /* 160px */
.gap-48 {
  gap: 12rem;
} /* 192px */
.gap-56 {
  gap: 14rem;
} /* 224px */
.gap-64 {
  gap: 16rem;
} /* 256px */

.full-height {
  height: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
  padding: 1rem;
}

.grid-span-1 {
  grid-column: span 1;
}

.grid-span-2 {
  grid-column: span 2;
}
.grid-span-3 {
  grid-column: span 3;
}
.grid-span-4 {
  grid-column: span 4;
}
.grid-span-5 {
  grid-column: span 5;
}
.grid-span-6 {
  grid-column: span 6;
}
.grid-span-7 {
  grid-column: span 7;
}
.grid-span-8 {
  grid-column: span 8;
}
.grid-span-9 {
  grid-column: span 9;
}
.grid-span-10 {
  grid-column: span 10;
}
.grid-span-11 {
  grid-column: span 11;
}
.grid-span-12 {
  grid-column: span 12;
}
