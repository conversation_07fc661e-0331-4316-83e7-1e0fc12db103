{"name": "codashi", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/codashi/app", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "npm run build", "cwd": "apps/codashi"}}, "serve": {"executor": "nx:run-script", "options": {"script": "dev"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/codashi/**/*.{ts,tsx,js,jsx}"]}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "tsc --project tsconfig.json --noEmit", "cwd": "apps/codashi"}}, "test": {"executor": "nx:run-commands", "outputs": ["{workspaceRoot}/coverage/apps/codashi"], "options": {"command": "npx vitest run", "cwd": "apps/codashi"}}, "test:watch": {"executor": "nx:run-commands", "options": {"command": "npx vitest", "cwd": "apps/codashi"}}, "test:browser": {"executor": "nx:run-commands", "outputs": ["{workspaceRoot}/coverage/apps/codashi"], "options": {"command": "npx vitest run --config=vitest.browser.config.ts", "cwd": "apps/codashi"}}, "test:browser:watch": {"executor": "nx:run-commands", "options": {"command": "npx vitest --config=vitest.browser.config.ts", "cwd": "apps/codashi"}}, "test:browser:ci": {"executor": "nx:run-commands", "outputs": ["{workspaceRoot}/coverage/apps/codashi"], "options": {"command": "npx vitest run --config=vitest.browser.ci.config.ts", "cwd": "apps/codashi"}}}, "tags": []}