{"extends": "./tsconfig.json", "include": [".react-router/types/**/*", "app/**/*", "app/**/.server/**/*", "app/**/.client/**/*", "workers/**/*", "worker-configuration.d.ts"], "compilerOptions": {"strict": true, "lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "baseUrl": ".", "rootDirs": [".", "./.react-router/types"], "paths": {"@self/*": ["./app/*"], "@awe/auth": ["../../libs/auth/src/index.ts"], "@awe/codashi-core": ["../../libs/codashi-core/src/index.ts"], "@awe/codashi-ui": ["../../libs/codashi-ui/src/index.ts"], "@awe/core": ["../../libs/core/src/index.ts"], "@awe/node-auth": ["../../libs/node-auth/src/index.ts"]}, "esModuleInterop": true, "resolveJsonModule": true}}