{"name": "codarashi-hq", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@astrojs/node": "^9.1.0", "@google/generative-ai": "^0.21.0", "@nanostores/react": "^0.8.4", "@tanstack/react-query": "^5.66.8", "astro": "^5.3.0", "class-variance-authority": "^0.7.1", "mongodb": "^6.13.1", "nanostores": "^0.11.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.0.7", "zod": "^3.24.2"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/react": "^4.2.0", "@tailwindcss/vite": "^4.0.7", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/parser": "^8.25.0", "eslint": "^9.21.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "jsdom": "^26.0.0", "vitest": "^3.0.9"}}