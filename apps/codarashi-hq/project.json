{"name": "codarashi-hq", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/codarashi-hq/src", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "astro build", "cwd": "apps/codarashi-hq"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "astro dev", "cwd": "apps/codarashi-hq"}}, "preview": {"executor": "nx:run-commands", "options": {"command": "astro preview", "cwd": "apps/codarashi-hq"}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "astro check", "cwd": "apps/codarashi-hq"}}, "lint": {"executor": "@nx/eslint:lint"}}, "tags": []}