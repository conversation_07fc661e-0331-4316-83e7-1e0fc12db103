// @ts-check
import node from '@astrojs/node';
import { defineConfig } from 'astro/config';

import react from '@astrojs/react';

import tailwindcss from '@tailwindcss/vite';

// https://astro.build/config
export default defineConfig({
  output: 'server',

  adapter: node({
    mode: 'standalone',
  }),

  vite: {
    resolve: {
      preserveSymlinks: true,
    },

    optimizeDeps: {
      include: ['@awe/core'],
    },

    plugins: [tailwindcss()],
  },

  integrations: [react()],
});
