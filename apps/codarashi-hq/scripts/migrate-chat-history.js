/**
 * Migration script to update ChatSession history items
 * Adds "model" field set to "gemini-2.0-flash" and "type" field set to "message"
 * for all chat history items that don't have these fields
 */

import * as dotenv from 'dotenv';
import { MongoClient } from 'mongodb';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file
const __dirname = path.dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const MONGO_URI = process.env.MONGO_URI;
const DB_NAME = 'codarashi';
const COLLECTION_NAME = 'chat-sessions';

if (!MONGO_URI) {
  console.error('MONGO_URI environment variable is not set');
  process.exit(1);
}

async function main() {
  console.log('Starting migration of ChatSession history items...');

  // Connect to MongoDB
  const client = new MongoClient(MONGO_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const collection = db.collection(COLLECTION_NAME);

    // Find all chat sessions
    const chatSessions = await collection.find({}).toArray();
    console.log(`Found ${chatSessions.length} chat sessions to process`);

    let totalUpdated = 0;
    let totalHistoryItems = 0;
    let totalItemsUpdated = 0;

    // Process each chat session
    for (const session of chatSessions) {
      let sessionUpdated = false;

      if (session.history && Array.isArray(session.history)) {
        totalHistoryItems += session.history.length;
        const updatedHistory = session.history.map((item) => {
          // If the item already has a model and type, leave it as is
          if (item.model && item.type) {
            return item;
          }

          // Otherwise, add the missing fields
          const updatedItem = { ...item };

          if (!updatedItem.model) {
            updatedItem.model = 'gemini-2.0-flash';
            sessionUpdated = true;
            totalItemsUpdated++;
          }

          if (!updatedItem.type) {
            updatedItem.type = 'message';
            sessionUpdated = true;
            totalItemsUpdated++;
          }

          return updatedItem;
        });

        if (sessionUpdated) {
          // Update the session in the database
          await collection.updateOne(
            { _id: session._id },
            { $set: { history: updatedHistory } }
          );
          totalUpdated++;
        }
      }
    }

    console.log(`Migration completed successfully!`);
    console.log(`Total chat sessions processed: ${chatSessions.length}`);
    console.log(`Total chat sessions updated: ${totalUpdated}`);
    console.log(`Total history items processed: ${totalHistoryItems}`);
    console.log(
      `Total history items that needed updating: ${totalItemsUpdated}`
    );
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

main().catch(console.error);
