/**
 * Migration script to update ChatSession documents
 * 1. Adds "model" field set to "gemini-2.0-flash" at the ChatSession top level
 * 2. Adds "comment" field set to "backfilled comment" for all history items of type "function"
 */

import * as dotenv from 'dotenv';
import { MongoClient } from 'mongodb';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file
const __dirname = path.dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const MONGO_URI = process.env.MONGO_URI;
const DB_NAME = 'codarashi';
const COLLECTION_NAME = 'chat-sessions';

if (!MONGO_URI) {
  console.error('MONGO_URI environment variable is not set');
  process.exit(1);
}

async function main() {
  console.log('Starting migration of ChatSession documents...');

  // Connect to MongoDB
  const client = new MongoClient(MONGO_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const collection = db.collection(COLLECTION_NAME);

    // Find all chat sessions
    const chatSessions = await collection.find({}).toArray();
    console.log(`Found ${chatSessions.length} chat sessions to process`);

    let totalSessionsUpdated = 0;
    let totalHistoryItems = 0;
    let totalFunctionItemsUpdated = 0;
    let totalModelFieldAdded = 0;

    // Process each chat session
    for (const session of chatSessions) {
      let sessionUpdated = false;

      // Add model field to the top level if it doesn't exist
      if (!session.model) {
        await collection.updateOne(
          { _id: session._id },
          { $set: { model: 'gemini-2.0-flash' } }
        );
        sessionUpdated = true;
        totalModelFieldAdded++;
      }

      // Process history items to add comment field to function items
      if (session.history && Array.isArray(session.history)) {
        totalHistoryItems += session.history.length;
        let historyUpdated = false;

        const updatedHistory = session.history.map((item) => {
          // If the item is of type "function" and doesn't have a comment field
          if (item.type === 'function' && !item.comment) {
            historyUpdated = true;
            totalFunctionItemsUpdated++;
            return { ...item, comment: 'backfilled comment' };
          }
          return item;
        });

        if (historyUpdated) {
          // Update the session history in the database
          await collection.updateOne(
            { _id: session._id },
            { $set: { history: updatedHistory } }
          );
          sessionUpdated = true;
        }
      }

      if (sessionUpdated) {
        totalSessionsUpdated++;
      }
    }

    console.log(`Migration completed successfully!`);
    console.log(`Total chat sessions processed: ${chatSessions.length}`);
    console.log(`Total chat sessions updated: ${totalSessionsUpdated}`);
    console.log(
      `Total chat sessions with model field added: ${totalModelFieldAdded}`
    );
    console.log(`Total history items processed: ${totalHistoryItems}`);
    console.log(
      `Total function history items updated with comment field: ${totalFunctionItemsUpdated}`
    );
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

main().catch(console.error);
