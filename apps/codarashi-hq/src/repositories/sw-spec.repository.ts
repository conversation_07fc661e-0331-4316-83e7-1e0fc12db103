import { getDb } from '@app/database';
import { UUID, type Pretify, type Primitive } from '@awe/core';
import { z } from 'astro:content';
import {
  softwareSpecialization,
  type SoftwareSpecialization,
} from '../entities/sw-spec.entity';

type DocumentType = Pretify<Primitive<SoftwareSpecialization>>;

const db = await getDb();
const collection = db.collection<DocumentType>('specializations');

export const swSpecRepository = {
  listAll: async (): Promise<SoftwareSpecialization[]> => {
    return collection
      .find({})
      .toArray()
      .then((docs) => z.array(softwareSpecialization).parse(docs));
  },

  listByNames: async (names: string[]): Promise<SoftwareSpecialization[]> => {
    return collection
      .find({ name: { $in: names } })
      .toArray()
      .then((docs) => z.array(softwareSpecialization).parse(docs));
  },

  create: async (
    payload: Omit<SoftwareSpecialization, 'id'>,
    id = UUID.generate()
  ): Promise<SoftwareSpecialization> => {
    const document = {
      ...payload,
      id,
      created_at: new Date(),
    };
    await collection.insertOne(document);

    return softwareSpecialization.parse(document);
  },

  update: async (payload: SoftwareSpecialization): Promise<void> => {
    await collection.updateOne(
      { id: payload.id },
      { $set: { ...payload, updated_at: new Date() } }
    );
  },

  delete: async (id: string): Promise<void> => {
    await collection.deleteOne({ id });
  },

  getById: async (id: string): Promise<SoftwareSpecialization | null> => {
    const doc = await collection.findOne({ id });

    return doc ? softwareSpecialization.parse(doc) : null;
  },
};
