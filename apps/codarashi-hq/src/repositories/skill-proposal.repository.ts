import { z } from 'astro:schema';
import { ClientSession, type Match<PERSON>eysAndValues } from 'mongodb';

import { getDb } from '@app/database';
import type { ChatSession } from '@app/entities/shared';
import {
  skillProposal,
  type SkillProposal,
  type UpdateDto,
} from '@app/entities/skill-proposal.entity';
import { type Skill } from '@app/entities/skill.entity';
import {
  DomainError,
  ErrorMessages,
  UUID,
  type DeepMutable,
  type Pretify,
  type Primitive,
} from '@awe/core';

// Extend the SkillProposal type with the database-specific fields
type SkillProposalDocument = Pretify<Primitive<SkillProposal>> & {
  created_at: Date;
  updated_at?: Date;
};

const db = await getDb();
const collection = db.collection<SkillProposalDocument>('skill-proposals');

export const skillProposalRepository = {
  create: async (
    payload: Pick<SkillProposal, 'skill' | 'session_id' | 'history_item_id'>,
    id = UUID.generate()
  ): Promise<SkillProposal> => {
    const document = {
      ...payload,
      id,
      status: 'pending',
      created_at: new Date(),
    };

    await collection.insertOne(document);

    return skillProposal.parse(document);
  },

  createMultiple: async (
    payload: Pick<SkillProposal, 'skill' | 'session_id' | 'history_item_id'>[],
    session?: ClientSession,
    generateId = UUID.generate
  ): Promise<SkillProposal[]> => {
    const documents = payload.map((item) => ({
      ...item,
      id: generateId(),
      status: 'pending',
      created_at: new Date(),
    }));

    await collection.insertMany(documents, { session });

    return z.array(skillProposal).parse(documents);
  },

  update: async (
    id: SkillProposal['id'],
    updates: UpdateDto,
    session?: ClientSession
  ): Promise<SkillProposal> => {
    const updateFields: DeepMutable<MatchKeysAndValues<SkillProposalDocument>> =
      {
        updated_at: new Date(),
      };

    if (updates.status) {
      updateFields.status = updates.status;
    }

    if (updates.user_notes) {
      updateFields.user_notes = updates.user_notes;
    }

    if (updates.user_feedback) {
      updateFields.user_feedback = updates.user_feedback;
    }

    if (updates.skill.name) {
      updateFields['skill.name'] = updates.skill.name;
    }

    if (updates.skill.description) {
      updateFields['skill.description'] = updates.skill.description;
    }

    if (updates.skill.category) {
      updateFields['skill.category'] = updates.skill.category;
    }

    if (updates.skill.alternative_names) {
      updateFields['skill.alternative_names'] = updates.skill.alternative_names;
    }

    if (updates.skill.links) {
      updateFields['skill.links'] = updates.skill.links;
    }

    if (updates.skill.specializations) {
      updateFields['skill.specializations'] = updates.skill.specializations;
    }

    const result = await collection.findOneAndUpdate(
      { id },
      { $set: updateFields },
      { returnDocument: 'after', session }
    );

    if (!result) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        id,
        entity: 'SkillProposal',
        message: 'Failed to update skill proposal',
        payload: updates,
      });
    }

    return skillProposal.parse(result);
  },

  listBySessionId: async (
    sessionId: ChatSession['id']
  ): Promise<SkillProposal[]> => {
    // Use aggregation pipeline to sort with custom logic
    const results = await collection
      .aggregate([
        // Match documents for the session
        { $match: { session_id: sessionId } },
        // Add a sortOrder field for custom sorting
        {
          $addFields: {
            sortOrder: {
              $cond: {
                if: { $eq: ['$status', 'pending'] },
                then: 0,
                else: 1,
              },
            },
          },
        },
        // Sort by the computed field first, then by created_at
        {
          $sort: {
            sortOrder: 1,
            created_at: 1,
          },
        },
        // Remove the temporary sortOrder field
        {
          $project: {
            sortOrder: 0,
          },
        },
      ])
      .toArray();

    // Parse the sorted documents
    return z.array(skillProposal).parse(results);
  },

  getByIdOrThrow: async (id: SkillProposal['id']): Promise<SkillProposal> => {
    return collection.findOne({ id }).then((doc) => {
      if (!doc) {
        throw new DomainError(ErrorMessages.EntityNotFound, {
          id,
          entity: 'SkillProposal',
        });
      }
      return skillProposal.parse(doc);
    });
  },

  findSimilarProposals: async (
    skillNames: Skill['name'][]
  ): Promise<SkillProposal[]> => {
    // Escape special regex characters in each skill name
    const escapedNames = skillNames.map((name) =>
      name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
    );

    const similarProposals = await collection
      .aggregate([
        {
          $match: {
            status: { $ne: 'accepted' },
            $or: escapedNames.flatMap((escapedName) => [
              // Exact match (case insensitive)
              { 'skill.name': { $regex: `^${escapedName}$`, $options: 'i' } },

              // Skill name contains existing skill name or vice versa
              { 'skill.name': { $regex: escapedName, $options: 'i' } },

              // Check if any alternative names match
              {
                'skill.alternative_names': {
                  $elemMatch: { $regex: escapedName, $options: 'i' },
                },
              },
            ]),
          },
        },
        {
          $addFields: {
            statusOrder: {
              $switch: {
                branches: [
                  { case: { $eq: ['$status', 'pending'] }, then: 0 },
                  {
                    case: { $in: ['$status', ['rejected', 'archived']] },
                    then: 2,
                  },
                ],
                default: 1,
              },
            },
          },
        },
        { $sort: { statusOrder: 1, created_at: -1 } },
        { $project: { statusOrder: 0 } }, // Remove the sort key from results
      ])
      .toArray();

    return z.array(skillProposal).parse(similarProposals);
  },

  delete: async (id: SkillProposal['id']): Promise<void> => {
    await collection.deleteOne({ id });
  },
};
