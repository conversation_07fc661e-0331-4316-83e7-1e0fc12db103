import { z } from 'astro:schema';
import { ClientSession } from 'mongodb';

import { getDb } from '@app/database';
import {
  sessionWithProposals,
  type SessionWithProposals,
} from '@app/entities/rel-proposal.entity';
import {
  chatSession,
  toHistoryItemId,
  type AiModel,
  type ChatHistoryItem,
  type ChatSession,
} from '@app/entities/shared';
import {
  DomainError,
  ErrorMessages,
  UnexpectedError,
  UUID,
  type DistributedOmit,
  type Pretify,
  type Primitive,
} from '@awe/core';

type DocumentType = Pretify<Primitive<ChatSession>>;

const db = await getDb();
const collection = db.collection<DocumentType>('chat-sessions');

// Enhanced chat session with skill proposal counts
const chatSessionWithProposalCounts = chatSession.extend({
  skillProposalCounts: z
    .object({
      pending: z.number().default(0),
      accepted: z.number().default(0),
      rejected: z.number().default(0),
      archived: z.number().default(0),
      total: z.number().default(0),
    })
    .optional(),
});

export type ChatSessionWithProposalCounts = z.infer<
  typeof chatSessionWithProposalCounts
>;

export const chatSessionRepository = {
  list: async (): Promise<ChatSessionWithProposalCounts[]> => {
    // Get all sessions with aggregated proposal counts using MongoDB aggregation
    const sessionsWithCounts = await collection
      .aggregate([
        // Match all chat sessions
        { $match: {} },

        // Lookup skill proposals for each session
        {
          $lookup: {
            from: 'skill-proposals',
            localField: 'id',
            foreignField: 'session_id',
            as: 'proposals',
          },
        },

        // Add proposal counts by status
        {
          $addFields: {
            skillProposalCounts: {
              pending: {
                $size: {
                  $filter: {
                    input: '$proposals',
                    as: 'proposal',
                    cond: { $eq: ['$$proposal.status', 'pending'] },
                  },
                },
              },
              accepted: {
                $size: {
                  $filter: {
                    input: '$proposals',
                    as: 'proposal',
                    cond: { $eq: ['$$proposal.status', 'accepted'] },
                  },
                },
              },
              rejected: {
                $size: {
                  $filter: {
                    input: '$proposals',
                    as: 'proposal',
                    cond: { $eq: ['$$proposal.status', 'rejected'] },
                  },
                },
              },
              archived: {
                $size: {
                  $filter: {
                    input: '$proposals',
                    as: 'proposal',
                    cond: { $eq: ['$$proposal.status', 'archived'] },
                  },
                },
              },
              total: { $size: '$proposals' },
            },
          },
        },

        // Remove the proposals array from the results to keep the response clean
        {
          $project: {
            proposals: 0,
          },
        },

        // Sort by created_at in descending order (newest first)
        {
          $sort: {
            created_at: -1,
          },
        },
      ])
      .toArray();

    return z.array(chatSessionWithProposalCounts).parse(sessionsWithCounts);
  },

  listWithProposals: async (): Promise<SessionWithProposals[]> => {
    // Get all sessions with their relation proposals using MongoDB aggregation
    const sessionsWithProposals = await collection
      .aggregate([
        // Match only non-deleted chat sessions
        {
          $match: {
            deleted_at: { $exists: false },
          },
        },

        // Lookup relation proposals for each session
        {
          $lookup: {
            from: 'skill-relation-proposals',
            localField: 'id',
            foreignField: 'session_id',
            as: 'proposals',
          },
        },

        // Only include sessions with at least one proposal
        {
          $match: {
            'proposals.0': { $exists: true },
          },
        },

        // Sort by created_at in descending order (newest first)
        {
          $sort: {
            created_at: -1,
          },
        },
      ])
      .toArray();

    // Parse the results using the sessionWithProposals schema
    return z.array(sessionWithProposals).parse(sessionsWithProposals);
  },

  updateTitle: async (
    id: ChatSession['id'],
    dto: Pretify<Required<Pick<ChatSession, 'display_title'>>>
  ): Promise<void> => {
    await collection.updateOne(
      { id },
      { $set: { display_title: dto.display_title } }
    );
  },

  getOrCreate: async (
    model: AiModel = 'gemini-2.0-flash',
    id: ChatSession['id'] = UUID.generate()
  ) => {
    const maybeSession = await collection.findOne({ id });

    if (maybeSession) {
      return chatSession.parse(maybeSession);
    }

    await collection.insertOne({
      id,
      model,
      history: [],
      created_at: new Date(),
    });

    return chatSession.parse(await collection.findOne({ id }));
  },

  addHistoryItem: async (
    session: ChatSession,
    item: DistributedOmit<ChatHistoryItem, 'id' | 'created_at'>,
    dbSession?: ClientSession,
    itemId = toHistoryItemId(),
    createdAt = new Date()
  ): Promise<ChatSession> => {
    const newItem: ChatHistoryItem =
      item.type === 'message'
        ? ({
            type: 'message',
            id: itemId,
            created_at: createdAt,
            user_prompt: item.user_prompt,
            model_response: item.model_response,
          } as const)
        : ({
            ...item,
            id: itemId,
            created_at: createdAt,
            type: 'function',
            function_call: item.function_call,
            function_response: item.function_response,
          } as const);

    const updatedSession = await collection.findOneAndUpdate(
      { id: session.id },
      {
        $push: { history: newItem },
        $set: { updated_at: new Date() },
      },
      { returnDocument: 'after', session: dbSession }
    );

    if (!updatedSession) {
      throw new UnexpectedError(ErrorMessages.EntityNotFound);
    }

    return chatSession.parse(updatedSession);
  },

  delete: async (id: string, now = new Date()): Promise<void> => {
    await collection.updateOne({ id }, { $set: { deleted_at: now } });
  },

  getByIdOrFail: async (
    id: string,
    dbSession?: ClientSession
  ): Promise<ChatSession> => {
    const doc = await collection.findOne({ id }, { session: dbSession });

    if (!doc) {
      throw new DomainError(ErrorMessages.EntityNotFound);
    }

    return chatSession.parse(doc);
  },

  replace: async (
    session: ChatSession,
    dbSession?: ClientSession
  ): Promise<ChatSession> => {
    const updatedSession = await collection.findOneAndReplace(
      { id: session.id },
      session,
      { returnDocument: 'after', session: dbSession }
    );

    if (!updatedSession) {
      throw new DomainError(ErrorMessages.EntityNotFound);
    }

    return chatSession.parse(updatedSession);
  },
};
