import { z } from 'astro:schema';
import type { ClientSession } from 'mongodb';

import { getDb } from '@app/database';
import type { SkillCategory } from '@app/entities/shared';
import { leanSkill, skill, type Skill } from '@app/entities/skill.entity';
import { type SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { createDefinedFieldsObject } from '@app/utils/mongodb';
import {
  DomainError,
  ErrorMessages,
  UnexpectedError,
  UUID,
  type Pretify,
  type Primitive,
} from '@awe/core';

type DocumentType = Pretify<Primitive<Skill>>;

const db = await getDb();
const collection = db.collection<DocumentType>('skills');

export const skillsRepository = {
  listBy: async (payload: {
    categories: SkillCategory[];
    specializations: SoftwareSpecialization['id'][];
  }): Promise<Skill[]> => {
    return collection
      .find({
        ...(payload.categories.length > 0
          ? { category: { $in: payload.categories } }
          : {}),
        ...(payload.specializations.length > 0
          ? { specializations: { $in: payload.specializations } }
          : {}),
      })
      .toArray()
      .then((docs) => z.array(skill).parse(docs));
  },

  list: async (): Promise<Skill[]> => {
    return collection
      .find({})
      .sort({ _id: -1 })
      .toArray()
      .then((docs) => z.array(skill).parse(docs));
  },

  getById: async (id: Skill['id']): Promise<Skill | null> => {
    const doc = await collection.findOne({ id });
    if (!doc) return null;
    return skill.parse(doc);
  },

  getByIdOrThrow: async (id: Skill['id']): Promise<Skill> => {
    const doc = await collection.findOne({ id });
    if (!doc) {
      throw new UnexpectedError('Skill not found', {
        id,
        service: 'skillsRepository',
      });
    }
    return skill.parse(doc);
  },

  create: async (
    payload: Omit<Skill, 'id'>,
    session?: ClientSession,
    id = UUID.generate()
  ): Promise<Skill> => {
    const document = {
      ...payload,
      id,
      created_at: new Date(),
    };

    const similarSkill = await skillsRepository.findSimilarSkill(payload.name);

    if (similarSkill) {
      throw new DomainError(ErrorMessages.EntityAlreadyExists, {
        message: 'Skill with similar name already exists',
        name: payload.name,
      });
    }

    await collection.insertOne(document, { session });

    return skill.parse(document);
  },

  update: async (
    id: Skill['id'],
    updates: Partial<Omit<Skill, 'id'>>,
    session?: ClientSession
  ): Promise<Skill> => {
    // Create an object with only the defined fields
    const definedFields = createDefinedFieldsObject(updates);

    // Always set the updated_at field
    const updateFields = {
      ...definedFields,
      updated_at: new Date(),
    };

    const result = await collection.findOneAndUpdate(
      { id },
      { $set: updateFields },
      {
        returnDocument: 'after',
        session,
      }
    );

    if (!result) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        id,
        entity: 'Skill',
      });
    }

    return skill.parse(result);
  },

  delete: async (id: Skill['id'], session?: ClientSession): Promise<void> => {
    const result = await collection.deleteOne({ id }, { session });

    if (result.deletedCount === 0) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        id,
        entity: 'Skill',
      });
    }
  },

  findSimilarSkill: async (skillName: Skill['name']): Promise<Skill | null> => {
    // Escape special regex characters in the skill name
    const escapedName = skillName.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

    const similarSkill = await collection.findOne({
      $or: [
        // Exact match (case insensitive)
        { name: { $regex: `^${escapedName}$`, $options: 'i' } },

        // Skill name contains existing skill name or vice versa
        { name: { $regex: escapedName, $options: 'i' } },

        // Check if any alternative names match
        {
          alternative_names: {
            $elemMatch: { $regex: escapedName, $options: 'i' },
          },
        },
      ],
    });

    return similarSkill ? skill.parse(similarSkill) : null;
  },

  findSimilarSkills: async (skillNames: Skill['name'][]): Promise<Skill[]> => {
    // Escape special regex characters in each skill name
    const escapedNames = skillNames.map((name) =>
      name.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
    );

    const similarSkills = await collection
      .find({
        $or: escapedNames.flatMap((escapedName) => [
          // Exact match (case insensitive)
          { name: { $regex: `^${escapedName}$`, $options: 'i' } },

          // Skill name contains existing skill name or vice versa
          { name: { $regex: escapedName, $options: 'i' } },

          // Check if any alternative names match
          {
            alternative_names: {
              $elemMatch: { $regex: escapedName, $options: 'i' },
            },
          },
        ]),
      })
      .toArray();

    return z.array(skill).parse(similarSkills);
  },

  /**
   * Search for skills by name (or alternative name) and return name, id, specializations (as names), and category.
   * Uses aggregation to join specialization names.
   */
  searchByName: async (query: string) => {
    if (!query.trim()) return [];
    // Escape regex special chars
    const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    const pipeline = [
      {
        $match: {
          $or: [
            { name: { $regex: escapedQuery, $options: 'i' } },
            {
              alternative_names: {
                $elemMatch: { $regex: escapedQuery, $options: 'i' },
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'specializations',
          localField: 'specializations',
          foreignField: 'id',
          as: 'specializationObjs',
        },
      },
      {
        $project: {
          _id: 0, // Exclude the default MongoDB _id
          id: 1,
          name: 1,
          category: 1,
          specializations: {
            $map: {
              input: '$specializationObjs',
              as: 'spec',
              in: '$$spec.name',
            },
          },
        },
      },
    ];

    const results = await collection.aggregate(pipeline).toArray();

    return z.array(leanSkill).parse(results);
  },

  getLeanSkillsByIds: async (ids: [Skill['id'], ...Skill['id'][]]) => {
    const pipeline = [
      {
        $match: {
          id: { $in: ids },
        },
      },
      {
        $lookup: {
          from: 'specializations',
          localField: 'specializations',
          foreignField: 'id',
          as: 'specializationObjs',
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          name: 1,
          category: 1,
          specializations: {
            $map: {
              input: '$specializationObjs',
              as: 'spec',
              in: '$$spec.name',
            },
          },
        },
      },

      {
        $addFields: {
          __order: { $indexOfArray: [ids, '$id'] },
        },
      },
      {
        $sort: { __order: 1 },
      },
      {
        $project: { __order: 0 },
      },
    ];

    const results = await collection.aggregate(pipeline).toArray();

    return z.array(leanSkill).parse(results);
  },
};
