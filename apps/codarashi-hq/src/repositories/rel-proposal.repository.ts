import { z } from 'astro:schema';
import type { ClientSession, MatchKeysAndValues } from 'mongodb';

import { getDb } from '@app/database';
import {
  relationProposal,
  type RelationProposal,
  type UpdateDto,
} from '@app/entities/rel-proposal.entity';
import type { ChatSession } from '@app/entities/shared';
import {
  DomainError,
  ErrorMessages,
  UUID,
  type DeepMutable,
  type Pretify,
  type Primitive,
} from '@awe/core';

type DocumentType = Pretify<Primitive<RelationProposal>> & {
  created_at: Date;
  updated_at: Date;
};

const db = await getDb();
const collection = db.collection<DocumentType>('skill-relation-proposals');

export const relationProposalRepository = {
  list: async (): Promise<RelationProposal[]> => {
    return collection
      .find({})
      .sort({ created_at: -1 })
      .toArray()
      .then((docs) => z.array(relationProposal).parse(docs));
  },

  getById: async (
    id: RelationProposal['id']
  ): Promise<RelationProposal | null> => {
    const doc = await collection.findOne({ id });

    if (!doc) return null;

    return relationProposal.parse(doc);
  },

  getByIdOrThrow: async (
    id: RelationProposal['id']
  ): Promise<RelationProposal> => {
    return collection.findOne({ id }).then((doc) => {
      if (!doc) {
        throw new DomainError(ErrorMessages.EntityNotFound, {
          id,
          entity: 'RelationProposal',
        });
      }
      return relationProposal.parse(doc);
    });
  },

  listBySessionId: async (
    sessionId: ChatSession['id']
  ): Promise<RelationProposal[]> => {
    return collection
      .find({ session_id: sessionId })
      .sort({ created_at: -1 })
      .toArray()
      .then((docs) => z.array(relationProposal).parse(docs));
  },

  create: async (
    payload: Omit<RelationProposal, 'id'>,
    session?: ClientSession,
    id = UUID.generate()
  ): Promise<RelationProposal> => {
    const document = {
      ...payload,
      id,
      created_at: new Date(),
      updated_at: new Date(),
    } as DocumentType;

    await collection.insertOne(document, { session });

    return relationProposal.parse(document);
  },

  update: async (
    id: RelationProposal['id'],
    updates: UpdateDto,
    session?: ClientSession
  ): Promise<RelationProposal> => {
    const updateFields: DeepMutable<MatchKeysAndValues<DocumentType>> = {
      updated_at: new Date(),
    };

    if (updates.status) {
      updateFields.status = updates.status;
    }
    if (updates.user_feedback) {
      updateFields.user_feedback = updates.user_feedback;
    }

    if (updates.user_notes) {
      updateFields.user_notes = updates.user_notes;
    }

    if (updates.relation) {
      if (updates.relation.description) {
        updateFields['relation.description'] = updates.relation.description;
      }

      switch (updates.relation.type) {
        case 'alternative': {
          if (updates.relation.skill_names) {
            updateFields['relation.skill_names'] = updates.relation.skill_names;
          }

          if (updates.relation.strength) {
            updateFields['relation.strength'] = updates.relation.strength;
          }

          break;
        }
        case 'version': {
          if (updates.relation.skill_names) {
            updateFields['relation.skill_names'] = updates.relation.skill_names;
          }

          break;
        }
        case 'hierarchy': {
          if (updates.relation.source_skill_name) {
            updateFields['relation.source_skill_name'] =
              updates.relation.source_skill_name;
          }
          if (updates.relation.target_skill_names) {
            updateFields['relation.target_skill_names'] =
              updates.relation.target_skill_names;
          }
          if (updates.relation.strength) {
            updateFields['relation.strength'] = updates.relation.strength;
          }

          break;
        }
        case 'ecosystem':
        case 'topical': {
          if (updates.relation.name) {
            updateFields['relation.name'] = updates.relation.name;
          }
          if (updates.relation.skill_names) {
            updateFields['relation.skill_names'] = updates.relation.skill_names;
          }
          break;
        }
      }
    }

    const result = await collection.findOneAndUpdate(
      { id },
      { $set: updateFields },
      {
        returnDocument: 'after',
        session,
      }
    );

    if (!result) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        id,
        entity: 'RelationProposal',
        message: 'Failed to update relation proposal',
        payload: updates,
      });
    }

    return relationProposal.parse(result);
  },

  delete: async (
    id: RelationProposal['id'],
    session?: ClientSession
  ): Promise<void> => {
    const result = await collection.deleteOne({ id }, { session });

    if (result.deletedCount === 0) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        id,
        entity: 'RelationProposal',
      });
    }
  },
};
