import { z } from 'astro:schema';
import type { ClientSession } from 'mongodb';

import { getDb } from '@app/database';
import {
  skillRelation,
  toId,
  type CreateDto,
  type SkillRelation,
  type UpdateDto,
} from '@app/entities/skill-relation.entity';

import { UnexpectedError, UUID, type Pretify, type Primitive } from '@awe/core';

type DocumentType = Pretify<Primitive<SkillRelation>> & {
  created_at: Date;
  updated_at: Date;
};

const db = await getDb();
const collection = db.collection<DocumentType>('skill-relations');

export const relationRepository = {
  list: async (): Promise<SkillRelation[]> => {
    return collection
      .find({})
      .sort({ _id: -1 })
      .toArray()
      .then((docs) => z.array(skillRelation).parse(docs));
  },

  create: async (
    payload: CreateDto,
    session?: ClientSession,
    id = toId(UUID.generate())
  ): Promise<SkillRelation> => {
    await collection.insertOne(
      {
        ...payload,
        id,
        created_at: new Date(),
        updated_at: new Date(),
      },
      { session }
    );

    return skillRelation.parse(await collection.findOne({ id }));
  },

  update: async (
    payload: UpdateDto,
    session?: ClientSession
  ): Promise<SkillRelation> => {
    const document = await collection.findOneAndUpdate(
      { id: payload.id },
      { $set: payload },
      { session, returnDocument: 'after' }
    );

    if (!document) {
      throw new UnexpectedError('Relation not found', {
        id: payload.id,
        service: 'relationRepository',
      });
    }

    return skillRelation.parse(document);
  },

  getByIdOrThrow: async (id: SkillRelation['id']): Promise<SkillRelation> => {
    const document = await collection.findOne({ id });
    if (!document) {
      throw new UnexpectedError('Relation not found', {
        id,
        service: 'relationRepository',
      });
    }
    return skillRelation.parse(document);
  },

  delete: async (
    id: SkillRelation['id'],
    session?: ClientSession
  ): Promise<void> => {
    const result = await collection.deleteOne({ id }, { session });
    if (result.deletedCount === 0) {
      throw new UnexpectedError('Relation not found', {
        id,
        service: 'relationRepository',
      });
    }
  },
};
