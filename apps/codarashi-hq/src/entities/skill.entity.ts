import { z } from 'astro:schema';

import { createFormDataPreprocessor } from '@app/utils/form-data.astro';
import { arrayFromString, commaSeparatedToArray } from '@app/utils/utils';
import type { UUID } from '@awe/core';
import { SkillCategory, SkillLinkType } from './shared';
import { softwareSpecialization } from './sw-spec.entity';

export const id = z.string().uuid().brand('skill_id');

export const skill = z.object({
  id,

  name: z.string().nonempty(),
  description: z.string().nonempty().nullish(),

  category: SkillCategory,
  specializations: z.array(softwareSpecialization.shape.id),
  alternative_names: z.array(z.string()).optional(),
  links: z.array(
    z.object({
      type: SkillLinkType,
      url: z.string().url(),
    })
  ),
});

const filterEmptyItems = (items: unknown) => {
  if (Array.isArray(items)) {
    return items.filter(Boolean);
  }
  return [];
};

const commonSchemaFields = {
  specializations: z.preprocess(arrayFromString, skill.shape.specializations),
  alternative_names: z.preprocess(
    commaSeparatedToArray,
    skill.shape.alternative_names
  ),
  links: z.preprocess(filterEmptyItems, skill.shape.links),
};

export const updateDto = createFormDataPreprocessor(
  z
    .object({
      id,
      ...commonSchemaFields,
    })
    .merge(
      skill
        .omit({
          id: true,
          specializations: true,
          links: true,
          alternative_names: true,
        })
        .partial()
    )
);

export const createDto = createFormDataPreprocessor(
  skill.omit({ id: true }).merge(
    z.object({
      ...commonSchemaFields,
    })
  )
);

export type Skill = z.infer<typeof skill>;

export const toId = (source: UUID) => id.parse(source);

export const leanSkill = z.object({
  id,
  name: skill.shape.name,
  category: skill.shape.category,
  specializations: z.array(softwareSpecialization.shape.name),
});

export type LeanSkill = z.infer<typeof leanSkill>;
