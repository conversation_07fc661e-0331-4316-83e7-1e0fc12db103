import { z } from 'astro:schema';

import { createFormDataPreprocessor } from '@app/utils/form-data.astro';
import {
  chatSession,
  historyItemBase,
  SkillCategory,
  SkillLinkType,
} from './shared';
import { softwareSpecialization } from './sw-spec.entity';
import { arrayFromString, commaSeparatedToArray } from '@app/utils/utils';

export const id = z.string().uuid().brand('skill_proposal_id');

const session_id = chatSession.shape.id;
const history_item_id = historyItemBase.shape.id;

export const skillProposalStatus = [
  'pending',
  'accepted',
  'rejected',
  'archived',
] as const;

export type SkillProposalStatus = (typeof skillProposalStatus)[number];

export const skillProposal = z.object({
  id,
  session_id,
  history_item_id,

  // we confirm that in the UI, they start as pending
  status: z.enum(skillProposalStatus),
  // free form remarks about the result - for RL of the AI
  user_notes: z.string().nullish(),
  // higher number means better feedback - for RL of the AI
  user_feedback: z.number().int().min(1).max(5).nullish(),

  skill: z.object({
    category: SkillCategory,
    specializations: z.array(softwareSpecialization.shape.id),
    name: z.string().nonempty(),
    alternative_names: z.array(z.string()).optional(),
    description: z.string().nonempty(),
    links: z.array(
      z.object({
        type: SkillLinkType,
        url: z.string().url(),
      })
    ),
  }),
});

export type SkillProposal = z.infer<typeof skillProposal>;

const filterEmptyItems = (items: unknown) => {
  if (Array.isArray(items)) {
    return items.filter(Boolean);
  }
  return [];
};

const numberFromString = (val: unknown) =>
  typeof val === 'string' ? Number(val) : val;

const commonSchemaFields = {
  status: z.enum(skillProposalStatus),
  user_notes: z.string().nullish(),
  user_feedback: z.preprocess(
    numberFromString,
    z.number().int().min(1).max(5).nullish()
  ),
};

export const updateDto = createFormDataPreprocessor(
  z
    .object({
      ...commonSchemaFields,
    })
    .merge(
      z.object({
        skill: z.object({
          category: skillProposal.shape.skill.shape.category.optional(),
          name: skillProposal.shape.skill.shape.name.optional(),
          description: skillProposal.shape.skill.shape.description.optional(),
          specializations: z.preprocess(
            arrayFromString,
            skillProposal.shape.skill.shape.specializations
          ),
          alternative_names: z.preprocess(
            commaSeparatedToArray,
            skillProposal.shape.skill.shape.alternative_names
          ),
          links: z.preprocess(
            filterEmptyItems,
            skillProposal.shape.skill.shape.links
          ),
        }),
      })
    )
);

export type UpdateDto = z.infer<typeof updateDto>;
