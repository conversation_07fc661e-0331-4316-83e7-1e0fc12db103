import { z } from 'astro:schema';

import { must, type UUID } from '@awe/core';
import { RelationType } from './shared';
import { id as skillId } from './skill.entity';

export const id = z.string().uuid().brand('skill_relation_id');

// Base fields common to all relationship types
const baseRelationFields = {
  id,
  name: z.string().min(1).default('Untitled'),
  description: z.string().optional(),
  created_at: z.coerce.date(),
  updated_at: z.coerce.date().optional(),
};

// Base fields for directional relationships (similarity and hierarchy)
const directionalRelationFields = {
  ...baseRelationFields,
  source_skill_id: skillId,
  target_skill_ids: z.array(skillId).min(1),
};

// Similarity relationships with strength measurement
const alternative = z.object({
  ...baseRelationFields,
  type: z.literal(RelationType.alternative),

  // How strong the relationship is (1-5)
  // 1: barely similar, minimal knowledge transfer
  // 5: completely interchangeable, complete knowledge transfer
  strength: z.coerce.number().int().min(1).max(5),
  // Non-directional approach using an array of skill IDs
  skill_ids: z.array(skillId).min(2),
});

// Version subtype - for different versions of the same skill
const version = z.object({
  ...baseRelationFields,
  type: z.literal(RelationType.version),

  // Non-directional approach using an array of skill IDs
  skill_ids: z.array(skillId).min(2),
});

// Hierarchy relationship - combines both prerequisite and structural concepts
// Examples:
// - Prerequisite: Ruby -> Rails (need to learn Ruby before Rails)
// - Structural: JavaScript -> TypeScript (TypeScript is a superset of JavaScript)
const hierarchy = z.object({
  ...directionalRelationFields,
  type: z.literal(RelationType.hierarchy),

  // Strength indicates the importance and nature of the hierarchical relationship (1-5)
  // 1: Loose connection
  //    - As prerequisite: Helpful but optional knowledge (nice-to-have)
  //    - As structural: Loosely related component/subset
  // 3: Moderate connection
  //    - As prerequisite: Important foundation but not strictly required
  //    - As structural: Significant component/subset
  // 5: Strong connection
  //    - As prerequisite: Essential/required knowledge
  //    - As structural: Core/fundamental component/subset
  strength: z.coerce.number().int().min(1).max(5),
});

// Bundle relationship (group of related skills)
// not used functionaly for evaluation but more about
// autocomplete
const bundle = z.object({
  ...baseRelationFields,

  type: z.enum([RelationType.ecosystem, RelationType.topical]),
  skill_ids: z.array(skillId).min(2),
});

export const skillRelation = z.discriminatedUnion('type', [
  alternative,
  version,
  hierarchy,
  bundle,
]);

export type SkillRelation = z.infer<typeof skillRelation>;

export const toId = (source: UUID) => id.parse(source);

// TODO from claude:
// add examples or reference links or community validation metrics somehow

export const createDto = z.discriminatedUnion('type', [
  alternative.omit({
    id: true,
    created_at: true,
    updated_at: true,
  }),
  version.omit({ id: true, created_at: true, updated_at: true }),
  hierarchy.omit({ id: true, created_at: true, updated_at: true }),
  bundle.omit({ id: true, created_at: true, updated_at: true }),
]);

const idField = z.object({ id });

export const updateDto = z.discriminatedUnion('type', [
  must(createDto.options.at(0)).merge(idField),
  must(createDto.options.at(1)).merge(idField),
  must(createDto.options.at(2)).merge(idField),
  must(createDto.options.at(3)).merge(idField),
]);

export type CreateDto = z.infer<typeof createDto>;
export type UpdateDto = z.infer<typeof updateDto>;
