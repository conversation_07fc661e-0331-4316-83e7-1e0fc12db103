import { UUID, type Pretify } from '@awe/core';
import { z } from 'astro:schema';

export const skillCategories = [
  'language',
  'lib_or_framework',
  'tool',
  'technique',
  'soft',
] as const;

export const aiModel = z.enum(['gemini-2.0-flash']);
export type AiModel = z.infer<typeof aiModel>;

export const SkillCategory = z.enum(skillCategories);

export type SkillCategory = z.infer<typeof SkillCategory>;

export const historyItemBase = z.object({
  id: z.string().uuid().brand('history_item_id'),
  created_at: z.coerce.date(),
});

export const toHistoryItemId = () =>
  UUID.generate() as unknown as z.infer<typeof historyItemBase.shape.id>;

const messageHistoryItem = historyItemBase.extend({
  type: z.literal('message'),
  user_prompt: z.string().nonempty(),
  model_response: z.string().nonempty(), // raw non-parsed
});

const functionHistoryItem = historyItemBase.extend({
  type: z.literal('function'),
  function_call: z.object({
    name: z.string().nonempty(),
    args: z.record(z.string(), z.any()),
  }),
  function_response: z.string().nonempty(), // raw non-parsed
  comment: z.string(),
});

export const chatHistoryItem = z.discriminatedUnion('type', [
  messageHistoryItem,
  functionHistoryItem,
]);

export type ChatHistoryItem = Pretify<z.infer<typeof chatHistoryItem>>;

export const chatSession = z.object({
  id: z
    .string()
    .uuid()
    .transform((value) => value as UUID)
    .brand('chat_session_id'),

  display_title: z.string().optional(),

  model: aiModel,
  history: z.array(chatHistoryItem),

  deleted_at: z.coerce.date().nullish(),
  created_at: z.coerce.date(),
});

export type ChatSession = z.infer<typeof chatSession>;

export const addHistoryItem = (
  chatSession: ChatSession,
  item:
    | Omit<z.infer<typeof messageHistoryItem>, 'id' | 'created_at'>
    | Omit<z.infer<typeof functionHistoryItem>, 'id' | 'created_at'>
): ChatSession => {
  const newItem =
    item.type === 'message'
      ? {
          ...item,
          type: item.type,
          id: toHistoryItemId(),
          created_at: new Date(),
        }
      : {
          ...item,
          type: item.type,
          id: toHistoryItemId(),
          created_at: new Date(),
        };

  return {
    ...chatSession,
    history: [...chatSession.history, newItem],
  };
};

export const skillLinkTypes = [
  'official_website',
  'github_repository',
  'wikipedia_page',
  'other',
] as const;

export const SkillLinkType = z.enum(skillLinkTypes);

export type SkillLinkType = z.infer<typeof SkillLinkType>;

export const RelationType = {
  // similarity
  // Encompasses both interchangeability and knowledge transfer between skills
  alternative: 'alternative',
  version: 'version',
  // hierarchy
  // Combined type that represents both prerequisite and structural relationships
  hierarchy: 'hierarchy',
  // bundle
  ecosystem: 'ecosystem',
  topical: 'topical',
} as const;

export type RelationType = (typeof RelationType)[keyof typeof RelationType];

export const relationType = z.enum([
  RelationType.alternative,
  RelationType.version,
  RelationType.hierarchy,
  RelationType.ecosystem,
  RelationType.topical,
]);
