import { z } from 'astro:schema';

import { createFormDataPreprocessor } from '@app/utils/form-data.astro';
import { commaSeparatedToArray } from '@app/utils/utils';
import {
  chatSession,
  historyItemBase,
  RelationType as SubTypes,
} from './shared';

export const id = z.string().uuid().brand('relation_proposal_id');

const session_id = chatSession.shape.id;
const history_item_id = historyItemBase.shape.id;

export const relationProposalStatus = [
  'pending',
  'accepted',
  'rejected',
  'archived',
] as const;

export type RelationProposalStatus = (typeof relationProposalStatus)[number];

// Base fields common to all proposal types
const baseProposalFields = {
  id,
  session_id,
  history_item_id,
  status: z.enum(relationProposalStatus),
  user_notes: z.string().optional(),
  user_feedback: z.coerce.number().int().min(1).max(5).optional(),
};

// Base fields common to all relationship types
const baseRelationFields = {
  description: z.string().optional(),
};

// Base fields for directional relationships (similarity and hierarchy) using names
const directionalNameFields = {
  ...baseRelationFields,
  source_skill_name: z.string(),
  target_skill_names: z.preprocess(
    commaSeparatedToArray,
    z.array(z.string()).min(1)
  ),
};

export const alternative = z.object({
  ...baseProposalFields,
  relation: z.object({
    ...baseRelationFields,
    type: z.literal(SubTypes.alternative),
    // How strong the relationship is (1-5)
    strength: z.coerce.number().int().min(1).max(5),
    // Non-directional approach using an array of skill names
    skill_names: z.preprocess(
      commaSeparatedToArray,
      z.array(z.string()).min(2)
    ),
  }),
});

export const versions = z.object({
  ...baseProposalFields,
  relation: z.object({
    ...baseRelationFields,
    type: z.literal(SubTypes.version),
    // Non-directional approach using an array of skill names
    skill_names: z.preprocess(
      commaSeparatedToArray,
      z.array(z.string()).min(2)
    ),
  }),
});

// Hierarchy relationship - combines both prerequisite and structural concepts
export const hierarchy = z.object({
  ...baseProposalFields,
  relation: z.object({
    ...directionalNameFields,
    type: z.literal(SubTypes.hierarchy),
    // Strength indicates the importance and nature of the hierarchical relationship (1-5)
    // 1: Loose connection
    //    - As prerequisite: Helpful but optional knowledge (nice-to-have)
    //    - As structural: Loosely related component/subset
    // 3: Moderate connection
    //    - As prerequisite: Important foundation but not strictly required
    //    - As structural: Significant component/subset
    // 5: Strong connection
    //    - As prerequisite: Essential/required knowledge
    //    - As structural: Core/fundamental component/subset
    strength: z.coerce.number().int().min(1).max(5),
  }),
});

// Bundle relationship (group of related skills)
export const bundle = z.object({
  ...baseProposalFields,
  relation: z.object({
    ...baseRelationFields,
    type: z.enum([SubTypes.ecosystem, SubTypes.topical]),
    skill_names: z.preprocess(
      commaSeparatedToArray,
      z.array(z.string()).min(2)
    ),
    name: z.string().min(1),
  }),
});

export const relationProposal = z.union([
  alternative,
  versions,
  hierarchy,
  bundle,
]);

export type RelationProposal = z.infer<typeof relationProposal>;

const topLevelOmit = {
  relation: true,
  id: true,
  session_id: true,
  history_item_id: true,
} as const;

// For update operations - handles partial updates
export const updateDto = createFormDataPreprocessor(
  z.union([
    alternative.omit(topLevelOmit).merge(
      z.object({
        relation: alternative.shape.relation,
        name: z.string().min(1),
      })
    ),
    versions.omit(topLevelOmit).merge(
      z.object({
        relation: versions.shape.relation,
        name: z.string().min(1),
      })
    ),
    hierarchy.omit(topLevelOmit).merge(
      z.object({
        relation: hierarchy.shape.relation,
        name: z.string().min(1),
      })
    ),
    bundle.omit(topLevelOmit).merge(
      z.object({
        relation: bundle.shape.relation,
        name: z.string().min(1),
      })
    ),
  ])
);

export type UpdateDto = z.infer<typeof updateDto>;

export const sessionWithProposals = chatSession.extend({
  proposals: z.array(relationProposal),
});

export type SessionWithProposals = z.infer<typeof sessionWithProposals>;
