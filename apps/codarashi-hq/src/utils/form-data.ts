/**
 * Utility functions for handling form data with support for nested objects and arrays
 */

import type { GenericRecord } from '@awe/core';

type NestedObject = Record<string, any>;

/**
 * Sets a value in a nested object structure based on a dot notation path
 * Handles array indices in the path (e.g., "user.skills.0")
 */
function setNestedValue(obj: NestedObject, path: string, value: any): void {
  const parts = path.split('.');
  let current = obj;

  // Process all parts except the last one (which will be the actual property to set)
  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    const nextPart = parts[i + 1];
    const isNextPartArrayIndex = !isNaN(Number(nextPart));

    // If this part doesn't exist yet, create it
    if (current[part] === undefined) {
      // If the next part is a number, create an array, otherwise create an object
      current[part] = isNextPartArrayIndex ? [] : {};
    }

    // Move to the next level
    current = current[part] as NestedObject;
  }

  // Get the last part (the actual property to set)
  const lastPart = parts[parts.length - 1];

  // If the last part is a number, treat it as an array index
  if (!isNaN(Number(lastPart))) {
    const index = Number(lastPart);
    // Make sure we have an array
    if (!Array.isArray(current)) {
      throw new Error(`Cannot set array index on non-array at path: ${path}`);
    }
    current[index] = value;
  } else {
    // Check if we already have a value for this property
    if (current[lastPart] !== undefined) {
      // If it's already an array, add the new value
      if (Array.isArray(current[lastPart])) {
        current[lastPart].push(value);
      } else {
        // Otherwise, convert to array with both values
        current[lastPart] = [current[lastPart], value];
      }
    } else {
      // Set the property with the single value
      current[lastPart] = value;
    }
  }
}

/**
 * Converts FormData to a JSON object with support for nested objects and arrays
 *
 * Features:
 * - Handles multiple values with the same key as arrays
 * - Supports nested objects with dot notation (e.g., "user.name", "user.skills.0")
 * - Preserves original values without type conversion
 *
 * @param formData The FormData object to convert
 * @returns A nested object representing the form data
 *
 * Example:
 * FormData with:
 * - "user.name" = "John"
 * - "user.skills.0" = "JavaScript"
 * - "user.skills.1" = "TypeScript"
 * - "isActive" = "true"
 *
 * Becomes:
 * {
 *   user: {
 *     name: "John",
 *     skills: ["JavaScript", "TypeScript"]
 *   },
 *   isActive: "true"
 * }
 */
export const formDataToJson = <T extends GenericRecord>(
  formData: FormData
): T => {
  const data = {} as NestedObject;

  formData.forEach((value, key) => {
    // Handle nested objects with dot notation (e.g., skills.0.type)
    if (key.includes('.')) {
      setNestedValue(data, key, value);
      return;
    }

    // Handle flat values
    const existingValue = data[key];

    if (Array.isArray(existingValue)) {
      data[key] = [...existingValue, value];
      return;
    }

    if (data[key]) {
      data[key] = [data[key], value];
      return;
    }

    data[key] = value;
  });

  return data as T;
};

/**
 * Converts a JSON object to FormData
 *
 * Features:
 * - Skips undefined and null values
 * - Converts non-string values to JSON strings
 *
 * @param data The object to convert to FormData
 * @returns A FormData object
 *
 * Example:
 * {
 *   name: "John",
 *   skills: ["JavaScript", "TypeScript"],
 *   profile: { age: 30 }
 * }
 *
 * Becomes FormData with:
 * - "name" = "John"
 * - "skills" = "["JavaScript","TypeScript"]"
 * - "profile" = "{"age":30}"
 */
export const jsonToFormData = <T extends GenericRecord>(data: T): FormData => {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return;
    }
    formData.append(
      key,
      typeof value !== 'string' ? JSON.stringify(value) : value
    );
  });

  return formData;
};
