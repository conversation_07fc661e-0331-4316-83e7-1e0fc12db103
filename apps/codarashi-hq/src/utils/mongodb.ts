import type { MakeNonNullable } from '@awe/core';

/**
 * Creates an object with only the defined properties from the source
 * Handles nested objects with dot notation for MongoDB updates
 */
export function createDefinedFieldsObject<T extends Record<string, any>>(
  obj: T,
  prefix = ''
): MakeNonNullable<T> {
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    const fieldKey = prefix ? `${prefix}.${key}` : key;

    if (value !== undefined) {
      if (
        value !== null &&
        typeof value === 'object' &&
        !Array.isArray(value)
      ) {
        // For nested objects, recurse with the current path as prefix
        Object.assign(result, createDefinedFieldsObject(value, fieldKey));
      } else {
        // For primitive values or arrays, add directly
        result[fieldKey] = value;
      }
    }
  }

  return result as MakeNonNullable<T>;
}
