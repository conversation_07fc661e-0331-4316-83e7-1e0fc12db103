/**
 * Astro-specific form data utilities
 */

import { z } from 'astro:schema';
import { formDataToJson } from './form-data';

/**
 * Creates a Zod preprocessor that converts FormData to JSON
 * This is specifically for use with Astro's schema validation
 */
export function createFormDataPreprocessor<T extends z.ZodType>(schema: T) {
  return z.preprocess((data) => {
    if (data instanceof FormData) {
      return formDataToJson(data);
    }
    return data;
  }, schema);
}
