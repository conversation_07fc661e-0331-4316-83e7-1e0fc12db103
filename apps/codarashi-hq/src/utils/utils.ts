import type { ChatSessionWithProposalCounts } from '@app/repositories/chat-session.repository';

export const getFirstPrompt = (
  session: ChatSessionWithProposalCounts
): string => {
  if (
    session.history.length > 0 &&
    session.history[0].type === 'message' &&
    session.history[0].user_prompt
  ) {
    const prompt = session.history[0].user_prompt;
    return prompt.substring(0, 30) + (prompt.length > 30 ? '...' : '');
  }

  return 'Empty chat';
};

export const arrayFromString = (val: unknown) =>
  typeof val === 'string' ? [val] : val;

export const commaSeparatedToArray = (val: unknown) =>
  typeof val === 'string'
    ? val
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : val;

export const copyToClipboard = (id: string) => {
  navigator.clipboard.writeText(id).catch((err) => {
    console.error('Failed to copy ID to clipboard:', err);
  });
};
