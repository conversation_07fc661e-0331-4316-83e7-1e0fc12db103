import { z } from 'zod';

import type { RelationProposal } from '@app/entities/rel-proposal.entity';
import { id as relationProposalId } from '@app/entities/rel-proposal.entity';
import {
  RelationType,
  SkillCategory,
  type AiModel,
  type ChatSession,
} from '@app/entities/shared';
import type { SkillProposal } from '@app/entities/skill-proposal.entity';
import { id as skillProposalId } from '@app/entities/skill-proposal.entity';
import {
  id as relationId,
  type SkillRelation,
} from '@app/entities/skill-relation.entity';
import { id as skillId, type Skill } from '@app/entities/skill.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import type {
  RelationAiResponse,
  PromptParams as RelationParams,
} from './relation.prompt';
import type {
  SkillsAiResponse,
  PromptParams as SkillsParams,
} from './skills.prompt';

/**
 * Interface for LLM providers that can generate skills
 *
 * This interface allows different LLM providers (Google, OpenAI, etc.)
 * to implement the same contract for skill generation while handling
 * provider-specific details internally.
 */
export type AiProvider = {
  /**
   * Generates skills based on the provided parameters
   *
   * @param params - Parameters for skill generation including target categories,
   *                 specializations, and user message
   * @returns A promise that resolves to either a successful result with generated skills
   *          or a failure result with an error
   */
  generateSkills: (params: SkillsParams) => Promise<SkillsAiResponse>;

  /**
   * Generates relations based on the provided parameters
   *
   * @param params - Parameters for relation generation including target categories,
   *                 specializations, and user message
   * @returns A promise that resolves to either a successful result with generated relations
   *          or a failure result with an error
   */
  generateRelation: (params: RelationParams) => Promise<RelationAiResponse>;

  callAgent: (params: {
    message: string;
    functions: AiFunctions;
    chatSession: ChatSession;
    sessionData: {
      categories: SkillCategory[];
      specializations: SoftwareSpecialization['name'][];
      skills: SkillProposal['skill']['name'][];
      relations: RelationProposal[];
    };
  }) => Promise<{
    chatSession: ChatSession;
  }>;
};

/**
 * Factory function to create a provider for a specific AI model
 *
 * @param model - The AI model to create a provider for
 * @returns The implementation of AiProvider for the specified model
 */
export async function createAiProviderForModel(
  model: AiModel
): Promise<AiProvider> {
  switch (model) {
    case 'gemini-2.0-flash': {
      const { googleAI } = await import('./google');
      return googleAI;
    }
    default:
      throw new Error(`Unsupported AI model: ${model}`);
  }
}

// will combine partly AiInferredParams + addiitonal served specifically by us
export type AiFunctions = {
  generateRelation: (params: {
    chat_session: ChatSession;
    seed_skills: string[];
    relation_type: RelationType;
    message: string;
  }) => Promise<RelationAiResponse>;
  generateSkills: (params: {
    chat_session: ChatSession;
    categories: SkillCategory[];
    specializations: SoftwareSpecialization['name'][];
    message: string;
  }) => Promise<SkillsAiResponse>;
  getRelationProposalById: (
    params: AiInferredParams['GetRelationProposalById']
  ) => Promise<RelationProposal>;
  getSkillProposalById: (
    params: AiInferredParams['GetSkillProposalById']
  ) => Promise<SkillProposal>;
  getRelationById: (
    params: AiInferredParams['GetRelationById']
  ) => Promise<SkillRelation>;
  getSkillById: (params: AiInferredParams['GetSkillById']) => Promise<Skill>;
};

type AiInferredParams = {
  Relation: z.infer<typeof AiInferredParams.relation>;
  Skills: z.infer<typeof AiInferredParams.skills>;
  FnName: z.infer<typeof AiInferredParams.fnName>;
  GetRelationProposalById: z.infer<
    typeof AiInferredParams.getRelationProposalById
  >;
  GetSkillProposalById: z.infer<typeof AiInferredParams.getSkillProposalById>;
  GetRelationById: z.infer<typeof AiInferredParams.getRelationById>;
  GetSkillById: z.infer<typeof AiInferredParams.getSkillById>;
};

export const aiFunctionNames = {
  generateRelation: 'generate_relation',
  generateSkills: 'generate_skills',
  getRelationProposalById: 'get_relation_proposal_by_id',
  getSkillProposalById: 'get_skill_proposal_by_id',
  getRelationById: 'get_relation_by_id',
  getSkillById: 'get_skill_by_id',
} as const;

// to validate params extracted by AI itself since we can't trust them on 100%
// these must match what we have in {aiProvider}.schema.ts files
export const AiInferredParams = {
  relation: z.object({
    seed_skills: z.array(z.string()),
    relation_type: z.enum([
      RelationType.alternative,
      RelationType.version,
      RelationType.hierarchy,
      RelationType.ecosystem,
      RelationType.topical,
    ]),
  }),
  getRelationProposalById: z.object({
    relation_proposal_id: relationProposalId,
  }),
  skills: z.object({
    categories: z.array(SkillCategory).optional(),
    specializations: z.array(z.string()).optional(),
  }),
  getSkillProposalById: z.object({
    skill_proposal_id: skillProposalId,
  }),
  getRelationById: z.object({
    relation_id: relationId,
  }),
  getSkillById: z.object({
    skill_id: skillId,
  }),
  fnName: z.enum([
    aiFunctionNames.generateRelation,
    aiFunctionNames.generateSkills,
    aiFunctionNames.getRelationProposalById,
    aiFunctionNames.getSkillProposalById,
    aiFunctionNames.getRelationById,
    aiFunctionNames.getSkillById,
  ]),
};
