import type { RelationProposal } from '@app/entities/rel-proposal.entity';
import type { SkillCategory } from '@app/entities/shared';
import type { SkillProposal } from '@app/entities/skill-proposal.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { productDescription } from './llm.descriptions';

export const createSystemPrompt = (params: {
  categories: SkillCategory[];
  specializations: SoftwareSpecialization['name'][];
  skills: SkillProposal['skill']['name'][];
  relations: RelationProposal[];
}): string => {
  return `
        Your task is to decide whether to generate a skill or skill-relation or even
        just answer a normal question based on your knowledge without picking any function call.

        Some general useful data you could use is:

        Available categories to work with: ${params.categories.join(', ')}
        Available specializations to work with: ${params.specializations.join(
          ', '
        )}
        ${
          params.skills.length
            ? `Already generated skills within the session: ${params.skills.join(
                ', '
              )}`
            : ''
        }
        ${
          params.relations.length
            ? `Already generated relations within the session: 
${params.relations.map(formatRelation).join('\n')}`
            : ''
        }

        If you do not find the parameters for a function call inside my message - try 
        searching in the above data I gave you and make a judgement and pick - don't ask me
        for help. For example if I tell you to generate a skill proposal for "solid.js" - you can decide this is
        category lib_or_framework and specializations - front end and web dev


        Some more general information about my overall project: ${productDescription}
    `;
};

/**
 * Helper function to format a relation proposal into a human-readable string
 * based on its discriminated union type
 */
export const formatRelation = (proposal: RelationProposal): string => {
  const { relation } = proposal;

  switch (relation.type) {
    case 'alternative':
      return `Alternative (${
        relation.strength || 'medium'
      }/5): ${relation.skill_names.join(' ↔ ')}`;
    case 'version':
      return `Version: ${relation.skill_names.join(' ↔ ')}`;
    case 'hierarchy':
      return `Hierarchy (${relation.strength || 'medium'}/5): ${
        relation.source_skill_name
      } → ${relation.target_skill_names.join(', ')}`;
    case 'ecosystem':
      return `Ecosystem: "${
        relation.name
      }" containing [${relation.skill_names.join(', ')}]`;
    case 'topical':
      return `Topical: "${
        relation.name
      }" containing [${relation.skill_names.join(', ')}]`;
    default:
      return `Unknown relation type: ${(relation as any).type}`;
  }
};
