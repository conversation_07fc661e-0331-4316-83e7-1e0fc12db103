import { SkillCategory, type ChatSession } from '@app/entities/shared';
import {
  skillProposal,
  type SkillProposal,
} from '@app/entities/skill-proposal.entity';
import { z } from 'astro:schema';
import { productDescription } from './llm.descriptions';

/**
 * Different AI providers will have to adhere to this response format and
 * they have different ways to achieve it.
 */
export const skillsAiResponse = z.object({
  comment: z.string().nonempty(),

  skills: z.array(
    skillProposal.shape.skill.merge(
      z.object({
        // specializations arrive as strings
        specializations: z.array(z.string()),
      })
    )
  ),
});

export type SkillsAiResponse = z.infer<typeof skillsAiResponse>;

export type PromptParams = {
  target: {
    // skills should be within those
    categories: SkillCategory[];

    // skills should be within those
    specializations: string[];
  };
  options: {
    //to avoid repetition
    exclude_skills: string[];

    // for generally better AI context, might help
    available_categories: string[];
    available_specializations: string[];

    previous_proposals: SkillProposal[];
  };
  message: string;

  session: ChatSession;
};

/**
 * This prompt construction should ideally be universal for all AI providers
 */
export const createSystemPrompt = (
  params: Omit<PromptParams, 'message'>
): string => {
  const { categories, specializations } = params.target;
  const {
    exclude_skills,
    available_categories,
    available_specializations,
    previous_proposals,
  } = params.options;

  const instructions = {
    links: `For skill.links[] try to provide up to 3 items which are more official like GitHub repository or official website or wikipedia or similar.
     If the link is highly relevant but doesnt fit into the first three categories - use other`,
    alternative_names:
      'For skill.alternative_names provide up to 5, giving none is fine.',

    specializations: specializations.length
      ? `Return skills within the following specializations (one of or all): [${specializations.join(
          ','
        )}]. For your reference my system supports the following specializations: [${available_specializations.join(
          ','
        )}]`
      : '',

    categories: categories.length
      ? `Return skills within the following category options (one of or all): [${categories.join(
          ', '
        )}]. For your reference my system supports the following categories: [${available_categories.join(
          ','
        )}]`
      : '',

    exclusions: exclude_skills.length
      ? `Names of skills to exclude as I already have them in my DB: [${[
          ...exclude_skills,
        ].join(',')}]`
      : '',

    previous_proposals: previous_proposals.length
      ? `Previous proposals you gave me already: ${JSON.stringify(
          previous_proposals
        )}`
      : '',
  } as const;

  return `
    ${productDescription}

    ${systemMessage}

    Given the provided schema return a list of skills in valid JSON format. Instructions for you:

    ${instructions.links}
    ${instructions.alternative_names}
    ${instructions.categories}
    ${instructions.specializations}
    ${instructions.exclusions}
    ${instructions.previous_proposals}

    In the top level "comment" field provide general information why you made your choices and just give me the general context.

  `;
};

const systemMessage =
  "Currently we're concentrated on the task of filling our database of skills and avoiding duplications.";
