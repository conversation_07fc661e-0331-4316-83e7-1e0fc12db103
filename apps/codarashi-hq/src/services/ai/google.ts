import { GoogleGenerativeAI, type Content } from '@google/generative-ai';

import { addHistoryItem, type ChatSession } from '@app/entities/shared';
import { UnexpectedError } from '@awe/core';
import { match } from 'ts-pattern';
import { createSystemPrompt as createAgentSystemPrompt } from './agent.prompt';
import {
  createSkillsResponseSchema,
  functionSchemas,
  getRelationResponseSchemaByType,
} from './google.schema';
import type { AiProvider } from './llm-provider';
import { AiInferredParams } from './llm-provider';
import {
  createSystemPrompt as createRelationSystemPrompt,
  relationAiResponse,
  type RelationAiResponse,
  type PromptParams as RelationPromptParams,
} from './relation.prompt';
import {
  createSystemPrompt,
  skillsAiResponse,
  type SkillsAiResponse,
  type PromptParams as SkillsPromptParams,
} from './skills.prompt';

/**
 * Google Gemini implementation of the SkillsGenerator interface
 */
class GoogleSkillsGenerator implements AiProvider {
  private static instance: GoogleSkillsGenerator;
  #genAI: GoogleGenerativeAI;

  private constructor() {
    this.#genAI = new GoogleGenerativeAI(import.meta.env.GEMINI_API_KEY);
  }

  public static getInstance(): GoogleSkillsGenerator {
    if (!GoogleSkillsGenerator.instance) {
      GoogleSkillsGenerator.instance = new GoogleSkillsGenerator();
    }
    return GoogleSkillsGenerator.instance;
  }

  #convertHistoryToContent(history: ChatSession['history']): Content[] {
    return history.flatMap((item) => {
      if (item.type === 'message') {
        return [
          { role: 'user', parts: [{ text: item.user_prompt }] },
          { role: 'model', parts: [{ text: item.model_response }] },
        ];
      } else {
        return [
          { role: 'user', parts: [{ text: item.function_call.name }] },
          { role: 'model', parts: [{ text: item.function_response }] },
        ];
      }
    });
  }

  async generateSkills(params: SkillsPromptParams): Promise<SkillsAiResponse> {
    const { available_categories, available_specializations } = params.options;

    const schema = createSkillsResponseSchema(
      available_categories,
      available_specializations
    );

    const model = this.#genAI.getGenerativeModel({
      model: 'gemini-2.0-flash',
      generationConfig: {
        responseMimeType: 'application/json',
        responseSchema: schema,
      },
      systemInstruction: createSystemPrompt(params),
    });

    const chat = model.startChat({
      history: this.#convertHistoryToContent(params.session.history),
    });

    const result = await chat.sendMessage(params.message);
    const plainTextResponse = result.response.text();

    const parsed = skillsAiResponse.safeParse(JSON.parse(plainTextResponse));

    if (!parsed.success) {
      throw new UnexpectedError('Failed to parse AI response', {
        cause: parsed.error,
      });
    }

    return parsed.data;
  }

  async generateRelation(
    params: RelationPromptParams
  ): Promise<RelationAiResponse> {
    const model = this.#genAI.getGenerativeModel({
      model: 'gemini-2.0-flash',
      generationConfig: {
        responseMimeType: 'application/json',
        responseSchema: getRelationResponseSchemaByType(params.type),
      },
      systemInstruction: createRelationSystemPrompt(params),
    });

    const chat = model.startChat({
      history: this.#convertHistoryToContent(params.session.history),
    });

    const result = await chat.sendMessage(params.message);
    const plainTextResponse = result.response.text();

    const parsed = relationAiResponse.safeParse(JSON.parse(plainTextResponse));

    if (!parsed.success) {
      throw new UnexpectedError('Failed to parse AI response', {
        cause: parsed.error,
      });
    }

    return parsed.data;
  }

  async callAgent({
    chatSession,
    message,
    functions,
    sessionData,
  }: Parameters<AiProvider['callAgent']>[0]): Promise<{
    chatSession: ChatSession;
  }> {
    let chatSessionDraft = { ...chatSession };

    const model = this.#genAI.getGenerativeModel({
      model: 'gemini-2.0-flash',
      tools: [
        {
          functionDeclarations: [
            functionSchemas.generateRelation,
            functionSchemas.generateSkills,
            functionSchemas.getRelationProposalById,
            functionSchemas.getSkillProposalById,
            functionSchemas.getRelationById,
            functionSchemas.getSkillById,
          ],
        },
      ],
      systemInstruction: createAgentSystemPrompt({
        categories: sessionData.categories,
        specializations: sessionData.specializations,
        skills: sessionData.skills,
        relations: sessionData.relations,
      }),
    });

    const modelHistory: Content[] = this.#convertHistoryToContent(
      chatSession.history
    );

    let chat = model.startChat({
      history: modelHistory,
    });

    // Send initial message to the model
    const { response } = await chat.sendMessage(message);

    const functionCalls = response?.functionCalls();

    if (!functionCalls) {
      chatSessionDraft = addHistoryItem(chatSession, {
        type: 'message' as const,
        user_prompt: message,
        model_response: response?.text() ?? '',
      } as const);

      return {
        chatSession: chatSessionDraft,
      };
    }

    for (const functionCall of functionCalls) {
      const { name, args } = functionCall;

      const parsedName = AiInferredParams.fnName.parse(name);

      await match(parsedName)
        .with('generate_relation', async () => {
          const validatedArgs = AiInferredParams.relation.parse(args);

          const result = await functions.generateRelation({
            chat_session: chatSession,
            seed_skills: validatedArgs.seed_skills,
            relation_type: validatedArgs.relation_type,
            message,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: result.comment,
          } as const);
        })
        .with('generate_skills', async () => {
          const validatedArgs = AiInferredParams.skills.parse(args);

          const result = await functions.generateSkills({
            chat_session: chatSession,
            categories: validatedArgs.categories ?? [],
            specializations: validatedArgs.specializations ?? [],
            message,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: result.comment,
          } as const);
        })
        .with('get_relation_proposal_by_id', async () => {
          const validatedArgs =
            AiInferredParams.getRelationProposalById.parse(args);

          const result = await functions.getRelationProposalById({
            relation_proposal_id: validatedArgs.relation_proposal_id,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: 'Retrieved relation from DB',
          } as const);
        })
        .with('get_skill_proposal_by_id', async () => {
          const validatedArgs =
            AiInferredParams.getSkillProposalById.parse(args);

          const result = await functions.getSkillProposalById({
            skill_proposal_id: validatedArgs.skill_proposal_id,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: 'Retrieved skill proposal from DB',
          } as const);
        })
        .with('get_relation_by_id', async () => {
          const validatedArgs = AiInferredParams.getRelationById.parse(args);

          const result = await functions.getRelationById({
            relation_id: validatedArgs.relation_id,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: 'Retrieved relation from DB',
          } as const);
        })
        .with('get_skill_by_id', async () => {
          const validatedArgs = AiInferredParams.getSkillById.parse(args);

          const result = await functions.getSkillById({
            skill_id: validatedArgs.skill_id,
          });

          const function_response_part = {
            name,
            response: { result },
          };

          chatSessionDraft = addHistoryItem(chatSession, {
            type: 'function' as const,
            function_call: functionCall,
            function_response: JSON.stringify(
              function_response_part.response.result
            ),
            comment: 'Retrieved skill from DB',
          } as const);
        })
        .exhaustive();
    }

    if (functionCalls.length > 0) {
      return await this.callAgent({
        message,
        chatSession: chatSessionDraft,
        functions,
        sessionData,
      });
    }

    return {
      chatSession: chatSessionDraft,
    };
  }
}

// Export a singleton instance
export const googleAI = GoogleSkillsGenerator.getInstance();
