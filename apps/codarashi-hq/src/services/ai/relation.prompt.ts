import { z } from 'astro:schema';

import {
  alternative,
  bundle,
  hierarchy,
  versions,
} from '@app/entities/rel-proposal.entity';
import { RelationType, type ChatSession } from '@app/entities/shared';
import { productDescription } from './llm.descriptions';

/**
 * Different AI providers will have to adhere to this response format and
 * they have different ways to achieve it.
 */
export const relationAiResponse = z.object({
  comment: z.string().nonempty(),

  relation: z.union([
    alternative.shape.relation,
    versions.shape.relation,
    hierarchy.shape.relation,
    bundle.shape.relation,
  ]),
});

export type RelationAiResponse = z.infer<typeof relationAiResponse>;

export type PromptParams = {
  seed_skill_names: string[];
  type: RelationType;

  message: string;
  session: ChatSession;
};

/**
 * This prompt construction should ideally be universal for all AI providers
 */
export const createSystemPrompt = (
  params: Omit<PromptParams, 'message'>
): string => {
  const { type, seed_skill_names, session } = params;

  const instructions = {
    // General instruction about the requested relation type
    relationTypeGuidance: `You are being asked to generate a ${type} relationship.
    Focus specifically on this type and its characteristics.`,

    // For hierarchy relationships, provide specific guidance
    hierarchyGuidance:
      type === RelationType.hierarchy
        ? `For hierarchy relationships, consider:
        - Is this primarily a prerequisite relationship (one skill needed before learning another)?
        - Is this primarily a structural relationship (one skill is a subset/component of another)?
        - Assign an appropriate strength (1-3) based on the importance of the connection:
          * Strength 1: Loose connection (helpful but optional)
          * Strength 2: Moderate connection (important but not essential)
          * Strength 3: Strong connection (essential/core component)
        - For source_skill_name, use the more foundational skill
        - For target_skill_names, include all skills that build upon or are components of the source skill`
        : '',

    // For alternative relationships, provide specific guidance
    alternativeGuidance:
      type === RelationType.alternative
        ? `For alternative relationships, consider:
        - Skills that serve similar purposes or solve similar problems
        - Assign an appropriate strength (1-3) based on how interchangeable they are:
          * Strength 1: Minimal overlap, different paradigms but same general purpose
          * Strength 2: Significant overlap, can often substitute for each other
          * Strength 3: Nearly complete interchangeability, very similar approaches
        - Include at least 2 skills in skill_names that are alternatives to each other
        - Strength is meant from the perspective of a developer to switch from one to the other`
        : '',

    // For version relationships, provide specific guidance
    versionGuidance:
      type === RelationType.version
        ? `For version relationships, consider:
        - Different versions of the same core technology
        - Assign an appropriate strength (1-3) based on how different the versions are:
          * Strength 1: Minor differences, high backward compatibility
          * Strength 2: Significant differences, some backward compatibility
          * Strength 3: Major differences, limited backward compatibility
        - Include at least 2 skills in skill_names that are versions of each other
        - Strength is meant from the perspective of a developer to switch from one to the other`
        : '',

    // For bundle relationships, provide specific guidance
    bundleGuidance:
      type === RelationType.ecosystem || type === RelationType.topical
        ? `For bundle relationships, consider:
        - Technologies that are commonly used together
        - For ecosystem bundles, focus on technologies in the same technical ecosystem
        - For topical bundles, focus on technologies that address the same domain/problem
        - Provide a descriptive name for the bundle
        - Include at least 2 skills in skill_names that belong to the bundle`
        : '',

    // Seed skills guidance
    seedSkillsGuidance: `Base your relation on these seed skill names: [${seed_skill_names.join(
      ', '
    )}].
      These are the starting point for your relationship - incorporate them appropriately based on the relation type.`,

    // Response format guidance
    responseFormat: `Ensure your response includes:
      1. A thoughtful comment explaining the relationship and why you structured it this way
      2. A properly formatted relation object matching the schema for the ${type} relationship type
      3. Accurate and helpful descriptions that explain the nature of the relationship`,
  } as const;

  return `
    ${productDescription}

    ${systemMessage}

    Given the provided schema return a list of skills in valid JSON format. Instructions for you:

    ${instructions.relationTypeGuidance}
    ${instructions.hierarchyGuidance}
    ${instructions.alternativeGuidance}
    ${instructions.versionGuidance}
    ${instructions.bundleGuidance}
    ${instructions.seedSkillsGuidance}
    ${instructions.responseFormat}

    In the top level "comment" field provide general information why you made your choices and just give me the general context.

  `;
};

const systemMessage = `
  Currently we're concentrated on the task of filling our database with information about the relation between skills.

  We have 4 categories of relations we want to use for the final application:
  
  - Alternative - an example could be React and Svelte. We also have a 1-3 strength indicator where 3 is strongest.
  
  - Version - an example could be Python2 and Python3. We also have a 1-3 strength indicator where 3 is strongest.
  
  - Hierarchy - This combines both prerequisite and structural relationships with a strength (1-3) indicating importance:
    * Example of prerequisite: Ruby and Rails (need to learn Ruby before Rails)
    * Example of structural: JavaScript and TypeScript (TypeScript is a superset of JavaScript)
    * Strength 1: Loose connection (helpful but optional prerequisite or loosely related component)
    * Strength 2: Moderate connection (important foundation or significant component)
    * Strength 3: Strong connection (essential prerequisite or core/fundamental component)
  
  - Bundle - an example could be component based front end frameworks (react, vue, svelte), 
    observability tools (Sentry, Datadog) or ecosystems - if a user enters React we should 
    consider proposing Redux.

  We also generate response for only 1 of those types - look for the type you need to generate elsewhere
  in the prompt.
`;
