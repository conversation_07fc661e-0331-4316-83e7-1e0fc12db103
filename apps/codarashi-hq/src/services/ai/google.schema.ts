import { RelationType, skillLinkTypes } from '@app/entities/shared';
import {
  SchemaType,
  type ResponseSchema,
  type Schema,
} from '@google/generative-ai';
import { aiFunctionNames } from './llm-provider';

export function createSkillSchema(
  availableCategories: string[],
  availableSpecializations: string[]
): Schema {
  return {
    type: SchemaType.OBJECT,
    description: 'An individual skill returned from the prompt',
    properties: {
      category: {
        type: SchemaType.STRING,
        description: 'Category of the skill',
        nullable: false,
        enum: [...availableCategories],
      },
      specializations: {
        type: SchemaType.ARRAY,
        description: 'Software specializations of the skill',
        nullable: false,
        items: {
          type: SchemaType.STRING,
          description: 'Software specialization of the skill',
          nullable: false,
          enum: [...availableSpecializations],
        },
      },
      name: {
        type: SchemaType.STRING,
        description: 'Name of the skill',
        nullable: false,
      },
      alternative_names: {
        type: SchemaType.ARRAY,
        description: 'Alternative names of the skill',
        nullable: true,
        items: {
          type: SchemaType.STRING,
          description: 'An alternative name for the skill',
          nullable: false,
        },
      },
      description: {
        type: SchemaType.STRING,
        description: 'Description of the skill',
        nullable: false,
      },
      links: {
        type: SchemaType.ARRAY,
        description: 'Links to the skill',
        nullable: true,
        items: {
          type: SchemaType.OBJECT,
          description:
            'Public links to the skill, preferrably official websites or GitHub repository',
          nullable: false,
          properties: {
            type: {
              type: SchemaType.STRING,
              description: 'Type of the link',
              nullable: false,
              enum: [...skillLinkTypes],
            },
            url: {
              type: SchemaType.STRING,
              description: 'URL of the link',
              nullable: false,
            },
          },
        },
      },
    },
    required: ['category', 'specializations', 'name'],
  };
}

export function createSkillsResponseSchema(
  availableCategories: string[],
  availableSpecializations: string[]
): ResponseSchema {
  const skillSchema = createSkillSchema(
    availableCategories,
    availableSpecializations
  );

  return {
    description: 'The result of a skills prompt',
    type: SchemaType.OBJECT,
    properties: {
      comment: {
        type: SchemaType.STRING,
        description: 'Any remarks from the model regarding the generated skill',
        nullable: false,
      },
      skills: {
        type: SchemaType.ARRAY,
        description: 'All generated skills',
        nullable: false,
        items: skillSchema,
      },
    },
    required: ['skills', 'comment'],
  };
}

// Base relation fields schema
const baseRelationFields = {
  description: {
    type: SchemaType.STRING,
    description: 'Description of the relationship',
    nullable: true,
  },
};

// Create schema for alternative relation
function createAlternativeRelationSchema(): Schema {
  return {
    type: SchemaType.OBJECT,
    description: 'Alternative relationship between skills',
    properties: {
      ...baseRelationFields,
      type: {
        type: SchemaType.STRING,
        description: 'Type of relationship',
        nullable: false,
        enum: [RelationType.alternative],
      },
      strength: {
        type: SchemaType.INTEGER,
        description:
          'How strong the relationship is (1-5, where 1 is minimal overlap, 3 is moderate overlap, 5 is nearly complete interchangeability)',
        nullable: false,
      },
      skill_names: {
        type: SchemaType.ARRAY,
        description:
          'Names of skills that are alternatives to each other (must include at least 2 skills)',
        nullable: false,
        items: {
          type: SchemaType.STRING,
          description: 'Name of a skill',
          nullable: false,
        },
      },
    },
    required: ['type', 'strength', 'skill_names'],
  };
}

// Create schema for version relation
function createVersionRelationSchema(): Schema {
  return {
    type: SchemaType.OBJECT,
    description: 'Version relationship between skills',
    properties: {
      ...baseRelationFields,
      type: {
        type: SchemaType.STRING,
        description: 'Type of relationship',
        nullable: false,
        enum: [RelationType.version],
      },
      skill_names: {
        type: SchemaType.ARRAY,
        description:
          'Names of skills that are versions of each other (must include at least 2 skills)',
        nullable: false,
        items: {
          type: SchemaType.STRING,
          description: 'Name of a skill',
          nullable: false,
        },
      },
    },
    required: ['type', 'skill_names'],
  };
}

// Create schema for hierarchy relation
function createHierarchyRelationSchema(): Schema {
  return {
    type: SchemaType.OBJECT,
    description:
      'Hierarchy relationship between skills (prerequisite or structural)',
    properties: {
      ...baseRelationFields,
      type: {
        type: SchemaType.STRING,
        description: 'Type of relationship',
        nullable: false,
        enum: [RelationType.hierarchy],
      },
      source_skill_name: {
        type: SchemaType.STRING,
        description: 'Name of the source/foundation skill',
        nullable: false,
      },
      target_skill_names: {
        type: SchemaType.ARRAY,
        description:
          'Names of skills that build upon or are components of the source skill (must include at least 1 skill)',
        nullable: false,
        items: {
          type: SchemaType.STRING,
          description: 'Name of a skill',
          nullable: false,
        },
      },
      strength: {
        type: SchemaType.INTEGER,
        description:
          'Strength of the hierarchical relationship (1: Loose connection, 3: Moderate connection, 5: Strong connection)',
        nullable: false,
      },
    },
    required: ['type', 'source_skill_name', 'target_skill_names', 'strength'],
  };
}

// Create schema for bundle relation
function createBundleRelationSchema(): Schema {
  return {
    type: SchemaType.OBJECT,
    description: 'Bundle relationship between skills (ecosystem or topical)',
    properties: {
      ...baseRelationFields,
      type: {
        type: SchemaType.STRING,
        description: 'Type of relationship',
        nullable: false,
        enum: [RelationType.ecosystem, RelationType.topical],
      },
      skill_names: {
        type: SchemaType.ARRAY,
        description:
          'Names of skills that belong to the bundle (must include at least 2 skills)',
        nullable: false,
        items: {
          type: SchemaType.STRING,
          description: 'Name of a skill',
          nullable: false,
        },
      },
      name: {
        type: SchemaType.STRING,
        description: 'Name of the bundle',
        nullable: false,
      },
    },
    required: ['type', 'skill_names', 'name'],
  };
}

// Create relation response schemas for each type
function createAlternativeRelationResponseSchema(): ResponseSchema {
  return {
    description: 'Response for alternative relationship',
    type: SchemaType.OBJECT,
    properties: {
      comment: {
        type: SchemaType.STRING,
        description:
          'Explanation of the relationship and why it was structured this way',
        nullable: false,
      },
      relation: createAlternativeRelationSchema(),
    },
    required: ['comment', 'relation'],
  };
}

function createVersionRelationResponseSchema(): ResponseSchema {
  return {
    description: 'Response for version relationship',
    type: SchemaType.OBJECT,
    properties: {
      comment: {
        type: SchemaType.STRING,
        description:
          'Explanation of the relationship and why it was structured this way',
        nullable: false,
      },
      relation: createVersionRelationSchema(),
    },
    required: ['comment', 'relation'],
  };
}

function createHierarchyRelationResponseSchema(): ResponseSchema {
  return {
    description: 'Response for hierarchy relationship',
    type: SchemaType.OBJECT,
    properties: {
      comment: {
        type: SchemaType.STRING,
        description:
          'Explanation of the relationship and why it was structured this way',
        nullable: false,
      },
      relation: createHierarchyRelationSchema(),
    },
    required: ['comment', 'relation'],
  };
}

function createBundleRelationResponseSchema(): ResponseSchema {
  return {
    description: 'Response for bundle relationship',
    type: SchemaType.OBJECT,
    properties: {
      comment: {
        type: SchemaType.STRING,
        description:
          'Explanation of the relationship and why it was structured this way',
        nullable: false,
      },
      relation: createBundleRelationSchema(),
    },
    required: ['comment', 'relation'],
  };
}

// Helper function to get the appropriate relation response schema based on relation type
export function getRelationResponseSchemaByType(
  relationType: RelationType
): ResponseSchema {
  switch (relationType) {
    case RelationType.alternative:
      return createAlternativeRelationResponseSchema();
    case RelationType.version:
      return createVersionRelationResponseSchema();
    case RelationType.hierarchy:
      return createHierarchyRelationResponseSchema();
    case RelationType.ecosystem:
    case RelationType.topical:
      return createBundleRelationResponseSchema();
    default:
      throw new Error(`Unsupported relation type: ${relationType}`);
  }
}

export const functionSchemas = {
  generateRelation: {
    description: 'Generate a relation between skills',
    name: aiFunctionNames.generateRelation,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        seed_skills: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.STRING,
          },
        },
        relation_type: {
          type: SchemaType.STRING,
          enum: [
            RelationType.alternative,
            RelationType.version,
            RelationType.hierarchy,
            RelationType.ecosystem,
            RelationType.topical,
          ],
        },
      },
      required: ['seed_skills', 'relation_type'],
    },
  },
  generateSkills: {
    description: 'Generate skills',
    name: aiFunctionNames.generateSkills,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        categories: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.STRING,
          },
        },
        specializations: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.STRING,
          },
        },
      },
      required: [],
    },
  },
  getRelationProposalById: {
    description:
      'Retrieve a specific relation proposal by its unique identifier',
    name: aiFunctionNames.getRelationProposalById,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        relation_id: {
          type: SchemaType.STRING,
          description:
            'The unique identifier of the relation proposal to retrieve',
        },
      },
      required: ['relation_id'],
    },
  },
  getSkillProposalById: {
    description: 'Retrieve a specific skill proposal by its unique identifier',
    name: aiFunctionNames.getSkillProposalById,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        skill_proposal_id: {
          type: SchemaType.STRING,
          description:
            'The unique identifier of the skill proposal to retrieve',
        },
      },
      required: ['skill_proposal_id'],
    },
  },
  getRelationById: {
    description: 'Retrieve a specific relation by its unique identifier',
    name: aiFunctionNames.getRelationById,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        relation_id: {
          type: SchemaType.STRING,
          description: 'The unique identifier of the relation to retrieve',
        },
      },
      required: ['relation_id'],
    },
  },
  getSkillById: {
    description: 'Retrieve a specific skill by its unique identifier',
    name: aiFunctionNames.getSkillById,
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        skill_id: {
          type: SchemaType.STRING,
          description: 'The unique identifier of the skill to retrieve',
        },
      },
      required: ['skill_id'],
    },
  },
};
