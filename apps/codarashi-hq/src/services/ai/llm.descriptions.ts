export const productDescription = `
    <PERSON><PERSON><PERSON> is an online resume builder and general job hunting suite of tools specialized
    for software engineers. It relies on its own high quality taxonomy system with skills,
    industries, company types, software categories, skill relations (similarities, alternatives),
    tailored specifically for mainstream software engineers. At least initially it doesn't aim
    to cover every existing niche skill or aspect of programming but the more mainstream ones.
    It's implied that the ultra-niche professionals wouldn't need service as ours.

    The extensive taxonomy system should help with 2 important features:
    - on-point suggestions and auto completions when filling up the resume data
    - candidate-vs-job_description matching score based on skill similarities, transferability etc.

    Some of the main features besides resume building itself are:

    - automated or highly assisted tailoring to job descriptions
    - autofilling of profile data into 3rd party application forms or ATS forms (through browser extension most probably)
    - a web "companion" to every tailored resume which contains much more detailed data which otherwise
      wouldn't fit inside the resume pdf. That companion may highlight to viewers why that certain candidate
      is a good fit for their specific job description and show the reason and data for that.
    - hints how to prepare for the interview and possible questions and topics
    - application tracking for candidates, with statuses and note taking
`;

export const skillRelationStrengthExamples = `
  # Similarity/Alternative Relationship Strength Examples

  ## Strength 1: Barely similar, minimal knowledge transfer
  - JavaScript vs PHP: Both are programming languages used for web development, but have different syntax, execution models, and ecosystems. Knowledge of one provides only general programming concepts that transfer to the other.
  - React vs Angular: While both are frontend frameworks, they have fundamentally different approaches to state management, templating, and component architecture. A developer would need significant retraining when switching.
  - MongoDB vs PostgreSQL: One is a document-oriented NoSQL database while the other is a relational SQL database. Core database concepts transfer, but query languages, data modeling approaches, and optimization techniques are very different.

  ## Strength 2: Somewhat similar, limited knowledge transfer
  - Python vs JavaScript: Different syntax and execution models, but similar enough that many programming patterns and concepts transfer well between them. A developer proficient in one can become productive in the other relatively quickly.
  - React vs Vue: Both use component-based architecture and virtual DOM concepts. The mental models are similar enough that skills transfer well, though there are still significant differences in implementation details.
  - MySQL vs PostgreSQL: Both are relational databases using SQL. Core concepts and query patterns transfer almost completely, though each has unique features and optimization techniques.

  ## Strength 3: Moderately similar, good knowledge transfer
  - Java vs C#: Both are statically-typed, object-oriented languages with similar syntax and programming paradigms. Core concepts like classes, interfaces, and generics work similarly, though libraries and frameworks differ.
  - Express.js vs Koa.js: Both are Node.js web frameworks with similar middleware patterns. The core concepts transfer well, with only implementation details and API differences to learn.
  - Docker vs Podman: Both container technologies with similar commands and concepts. Knowledge of one transfers almost directly to the other with minor syntax differences.

  ## Strength 4: Highly similar, nearly complete knowledge transfer
  - React vs Preact: Preact is a lightweight alternative to React with almost identical API. The core concepts are the same, with only minor implementation differences and performance optimizations.
  - Python 3.9 vs Python 3.10: Consecutive versions with incremental changes. Most code and knowledge transfers directly, with only new features and minor breaking changes to learn.
  - PostgreSQL 13 vs PostgreSQL 14: Consecutive database versions with high compatibility. Query language and most features remain the same, with only new capabilities to learn.

  ## Strength 5: Nearly identical, complete knowledge transfer
  - TypeScript vs JavaScript: TypeScript is a superset of JavaScript, so JavaScript knowledge transfers completely. The only new concepts to learn are TypeScript's type system.
  - React vs React Native: The core component model, hooks, and state management approaches are identical. The main differences are in layout systems and platform-specific APIs.
  - Python 2 vs Python 3: Different versions of the same language with some syntax changes but the same core concepts and paradigms. Most knowledge transfers directly with minimal adaptation.
`;

export const agenticChatSystemMessage = `
    You are a helpful assistant that can generate skills and relations between skills or answer general questions.
    
`;
