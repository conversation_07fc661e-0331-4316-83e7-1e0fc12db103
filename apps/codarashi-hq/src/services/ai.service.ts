import type { ClientSession } from 'mongodb';

import type { RelationProposal } from '@app/entities/rel-proposal.entity';
import {
  skillCategories,
  SkillCategory,
  type AiModel,
  type ChatHistoryItem,
  type ChatSession,
} from '@app/entities/shared';
import type { SkillProposal } from '@app/entities/skill-proposal.entity';
import type { Skill } from '@app/entities/skill.entity';
import { type SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { relationProposalRepository } from '@app/repositories/rel-proposal.repository';
import { relationRepository } from '@app/repositories/relation.repository';
import { skillProposalRepository } from '@app/repositories/skill-proposal.repository';
import { skillsRepository } from '@app/repositories/skill.repository';
import { swSpecRepository } from '@app/repositories/sw-spec.repository';
import {
  createAiProviderForModel,
  type AiFunctions,
} from '@app/services/ai/llm-provider';
import type { RelationAiResponse } from '@app/services/ai/relation.prompt';
import type { SkillsAiResponse } from '@app/services/ai/skills.prompt';

type Artifacts = Array<
  | (SkillsAiResponse & { type: 'skills-generation' })
  | (RelationAiResponse & { type: 'relation-generation' })
>;

export class AiService {
  /**
   * Handles agent-based chat interactions with the AI
   *
   * @param message User's message to the AI
   * @param sessionId ID of the chat session
   * @returns Updated chat session and context information
   */
  async callAgent({
    message,
    chatSession,
    model,
  }: {
    message: string;
    chatSession: ChatSession;
    model: AiModel;
  }): Promise<{
    chatSession: ChatSession;
    artifacts: Artifacts;
  }> {
    const aiProvider = await createAiProviderForModel(model);
    const artifacts: Artifacts = [];

    // Define the functions that will be available to the AI
    const aiFunctions: AiFunctions = {
      generateSkills: async (params) => {
        const result = await this.generateSkills(params);
        artifacts.push({ ...result, type: 'skills-generation' });
        return result;
      },
      generateRelation: async (params) => {
        const result = await this.generateRelation(params);
        artifacts.push({ ...result, type: 'relation-generation' });
        return result;
      },
      getRelationProposalById: async (params) => {
        return await relationProposalRepository.getByIdOrThrow(
          params.relation_proposal_id
        );
      },
      getSkillProposalById: async (params) => {
        return await skillProposalRepository.getByIdOrThrow(
          params.skill_proposal_id
        );
      },
      getRelationById: async (params) => {
        return await relationRepository.getByIdOrThrow(params.relation_id);
      },
      getSkillById: async (params) => {
        return await skillsRepository.getByIdOrThrow(params.skill_id);
      },
    };

    const result = await aiProvider.callAgent({
      message,
      functions: aiFunctions,
      chatSession,
      sessionData: {
        categories: Object.values(SkillCategory),
        specializations: (
          await swSpecRepository.listAll()
        ).map((spec) => spec.name),
        skills: (
          await skillProposalRepository.listBySessionId(chatSession.id)
        ).map((proposal) => proposal.skill.name),
        relations: await relationProposalRepository.listBySessionId(
          chatSession.id
        ),
      },
    });

    return {
      chatSession: result.chatSession,
      artifacts,
    };
  }

  /**
   * Generates skill proposals based on the provided parameters
   *
   * @param params Parameters for skill generationUnknown function: get_relation_by_id
   * @returns A promise that resolves to the generation result
   */
  async generateSkills(
    params: Parameters<AiFunctions['generateSkills']>[0]
  ): Promise<SkillsAiResponse> {
    const specializations = await swSpecRepository.listByNames(
      params.specializations
    );

    const existingSkillsWithinFilter = await skillsRepository.listBy({
      categories: params.categories,
      specializations: specializations.map((spec) => spec.id),
    });

    const existingProposals = params.chat_session.id
      ? await skillProposalRepository.listBySessionId(params.chat_session.id)
      : [];

    const allSpecs = await swSpecRepository.listAll();

    const allSpecsById = allSpecs.reduce((acc, item) => {
      acc[item.id] = item;
      return acc;
    }, {} as Record<string, SoftwareSpecialization>);

    const allSpecsByName = allSpecs.reduce((acc, item) => {
      acc[item.name] = item;
      return acc;
    }, {} as Record<string, SoftwareSpecialization>);

    const targetSpecNames = params.specializations
      .map((specId) => allSpecsById[specId])
      .filter(Boolean)
      .map((elem) => elem.name);

    return await this.processSkillGeneration(params, {
      existingSkillsWithinFilter,
      existingProposals,
      targetSpecNames,
      allSpecsByName,
      allSpecs,
    });
  }

  private async processSkillGeneration(
    params: Parameters<AiFunctions['generateSkills']>[0],
    context: {
      existingSkillsWithinFilter: Skill[];
      existingProposals: SkillProposal[];
      targetSpecNames: string[];
      allSpecsByName: Record<string, SoftwareSpecialization>;
      allSpecs: SoftwareSpecialization[];
    }
  ): Promise<SkillsAiResponse> {
    const aiProvider = await createAiProviderForModel('gemini-2.0-flash');

    return await aiProvider.generateSkills({
      message: params.message,
      target: {
        categories: params.categories.length
          ? params.categories
          : [...skillCategories],
        specializations: context.targetSpecNames.length
          ? context.targetSpecNames
          : context.allSpecs.map((spec) => spec.name),
      },
      options: {
        exclude_skills: context.existingSkillsWithinFilter.map(
          (skill) => skill.name
        ),
        previous_proposals: context.existingProposals,
        available_categories: [...skillCategories],
        available_specializations: context.allSpecs.map((spec) => spec.name),
      },
      session: params.chat_session,
    });
  }

  /**
   * Persists the generated skills to the database
   */
  async persistSkillGeneration({
    data,
    sessionId,
    dbSession,
    historyItemId,
  }: {
    data: SkillsAiResponse;
    sessionId: ChatSession['id'];
    dbSession: ClientSession;
    historyItemId: ChatHistoryItem['id'];
  }) {
    if (!data.skills.length) {
      return {
        comment: data.comment,
        proposals: [],
      };
    }

    const specializations = await swSpecRepository.listAll();

    const proposals = await skillProposalRepository.createMultiple(
      data.skills.map((skillProposal) => ({
        skill: {
          ...skillProposal,
          specializations: skillProposal.specializations.map(
            (specName) =>
              specializations.find((spec) => spec.name === specName)?.id
          ) as SoftwareSpecialization['id'][],
        },
        session_id: sessionId,
        history_item_id: historyItemId,
      })),
      dbSession
    );

    return {
      comment: data.comment,
      proposals,
    };
  }

  /**
   * Generates relation proposals based on the provided parameters
   *
   * @param params Parameters for relation generation
   * @param dbSession MongoDB client session for transaction handling
   * @returns A promise that resolves to the generation result
   */
  async generateRelation(
    params: Parameters<AiFunctions['generateRelation']>[0]
  ): Promise<RelationAiResponse> {
    const aiProvider = await createAiProviderForModel('gemini-2.0-flash');

    return await aiProvider.generateRelation({
      seed_skill_names: params.seed_skills,
      type: params.relation_type,
      message: params.message,
      session: params.chat_session,
    });
  }

  /**
   * Persists the relation proposal to the database
   */
  async persistRelationProposal({
    relation,
    sessionId,
    historyItemId,
    dbSession,
  }: {
    relation: RelationAiResponse['relation'];
    sessionId: ChatSession['id'];
    historyItemId: ChatHistoryItem['id'];
    dbSession: ClientSession;
  }): Promise<RelationProposal> {
    const relationProposal = await relationProposalRepository.create(
      {
        relation,
        session_id: sessionId,
        history_item_id: historyItemId,
        status: 'pending',
      },
      dbSession
    );

    return relationProposal;
  }
}

export const aiService = new AiService();
