---
import Layout from '@app/components/layouts/Layout.astro';
import { actions } from 'astro:actions';

const rawSpecializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);

const specializations = rawSpecializations.reduce(
  (acc, spec) => {
    if (!spec.parent_id) {
      // This is a top-level specialization
      acc.push({
        ...spec,
        children: rawSpecializations
          .filter((child) => child.parent_id === spec.id)
          .map((child) => ({ ...child, children: [] })),
      });
    }
    return acc;
  },
  [] as Array<
    (typeof rawSpecializations)[0] & {
      children: Array<(typeof rawSpecializations)[0] & { children: [] }>;
    }
  >
);
---

<Layout>
  <div class="flex flex-col gap-16 aw-container">
    <a href="/specializations/create" class="aw-button self-start">Create</a>

    {specializations.length === 0 && <div>No specializations</div>}

    <ul class="space-y-2">
      {
        specializations.map((spec) => (
          <li class="flex flex-col gap-4">
            <a href={`/specializations/${spec.id}/edit`} class="aw-link">
              {spec.name}
            </a>

            {spec.children.length > 0 && (
              <ul class="flex flex-col gap-4">
                {spec.children.map((child) => (
                  <li class="flex flex-col gap-4 pl-4">
                    <a
                      href={`/specializations/${child.id}/edit`}
                      class="aw-link"
                    >
                      {child.name}
                    </a>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))
      }
    </ul>
  </div>
</Layout>
