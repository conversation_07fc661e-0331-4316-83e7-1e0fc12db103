---
import Layout from '@app/components/layouts/Layout.astro';
import { CreateForm } from '@app/components/specializations/create-form';
import { actions } from 'astro:actions';

const rawSpecializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);

const specializations = rawSpecializations.filter((spec) => !spec.parent_id);
---

<Layout>
  <div class="flex flex-col gap-16 aw-container">
    <header>
      <a href="/specializations" class="aw-link">Back</a>
    </header>

    <CreateForm specializations={specializations} client:only="react" />
  </div>
</Layout>
