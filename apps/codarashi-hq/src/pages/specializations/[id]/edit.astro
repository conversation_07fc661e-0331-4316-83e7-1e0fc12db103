---
import Layout from '@app/components/layouts/Layout.astro';
import EditForm from '@app/components/specializations/edit-form';
import { actions } from 'astro:actions';

const { id } = Astro.params;

if (!id) {
  return new Response('Not found', { status: 404 });
}

const specialization = await Astro.callAction(
  actions.swSpecializations.get.orThrow,
  { id }
);

const rawSpecializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);

const specializations = rawSpecializations.filter((spec) => !spec.parent_id);
---

<Layout>
  {
    specialization ? (
      <EditForm
        specialization={specialization}
        specializations={specializations.filter(
          (spec) => spec.id !== specialization.id
        )}
        client:only="react"
      />
    ) : (
      <p>Specialization not found</p>
    )
  }
</Layout>
