---
import { actions } from 'astro:actions';

import Layout from '@app/components/layouts/Layout.astro';
import { CreateRelationButton } from '@app/components/skill-relations/create.button';
import { SkillRelationList } from '@app/components/skill-relations/skill-relation-list';

const relations = await Astro.callAction(
  actions.relations.list.orThrow,
  undefined
);
---

<Layout>
  <div class="flex flex-col gap-8 aw-container">
    <header
      class="flex justify-between items-center bg-white p-6 rounded-lg shadow-sm w-full"
    >
      <h1 class="text-2xl font-bold text-gray-800">Relations management</h1>
      <CreateRelationButton client:only="react" />
    </header>

    <div class="bg-white p-6 rounded-lg shadow-sm">
      {
        relations.length === 0 ? (
          <div class="text-center py-8 text-gray-500">
            <p class="text-lg">No skill relations found</p>
          </div>
        ) : (
          <SkillRelationList relations={relations} client:only="react" />
        )
      }
    </div>
  </div>
</Layout>

<style>
  .aw-container {
    max-width: 1200px;
  }
</style>
