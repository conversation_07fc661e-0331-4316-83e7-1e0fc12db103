---
import { actions } from 'astro:actions';

import Layout from '@app/components/layouts/Layout.astro';
import { CreateSkillButton } from '@app/components/skills/create-skill-button';
import { SkillList } from '@app/components/skills/skill-list';

const skills = await Astro.callAction(actions.skills.list.orThrow, undefined);

const specializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);
---

<Layout>
  <div class="flex flex-col gap-8 aw-container">
    <header
      class="flex justify-between items-center bg-white p-6 rounded-lg shadow-sm w-full"
    >
      <h1 class="text-2xl font-bold text-gray-800">Skills Management</h1>
      <CreateSkillButton
        specializations={specializations}
        client:only="react"
      />
    </header>

    <div class="bg-white p-6 rounded-lg shadow-sm">
      {
        skills.length === 0 ? (
          <div class="text-center py-8 text-gray-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-12 w-12 mx-auto mb-4 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <p class="text-lg">No skills found</p>
            <p class="mt-2">
              Click the "Create Skill" button to add your first skill
            </p>
          </div>
        ) : (
          <SkillList
            skills={skills}
            specializations={specializations}
            client:only="react"
          />
        )
      }
    </div>
  </div>
</Layout>

<style>
  .aw-container {
    max-width: 1200px;
  }
</style>
