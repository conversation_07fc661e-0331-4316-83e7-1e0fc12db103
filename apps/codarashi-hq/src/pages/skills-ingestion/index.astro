---
import { actions } from 'astro:actions';

import Layout from '@app/components/layouts/Layout.astro';
import ChatHistory from '@app/components/shared/chat-history.astro';
import ChatSessionList from '@app/components/skills-ingestion/chat-session.list.astro';
import { PromptForm } from '@app/components/skills-ingestion/prompt-form';
import { SkillProposalList } from '@app/components/skills-ingestion/skill-proposal-list';
import type { SkillProposal } from '@app/entities/skill-proposal.entity';

const sessionId = Astro.url.searchParams.get('session_id');

// If we have a session ID, fetch the session details and skill proposals
let currentSession = null;
let skillProposals: SkillProposal[] = [];

if (sessionId) {
  try {
    currentSession = await Astro.callAction(actions.chatSessions.get.orThrow, {
      id: sessionId,
    });

    skillProposals = await Astro.callAction(
      actions.skillProposals.listBySessionId.orThrow,
      { sessionId }
    );
  } catch (error) {
    console.error('Error fetching session data:', error);
  }
}

const specializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);
---

<Layout>
  <div class="grid grid-cols-4 gap-4 h-full">
    <!-- Left Column: Chat Sessions List -->
    <div class="border-r p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Chats</h2>
      <ChatSessionList />
    </div>

    <!-- Middle Column: Prompt Form and Chat History -->
    <div class="p-4 col-span-2 max-h-dvh">
      <h2 class="text-xl font-semibold mb-4">
        {sessionId ? 'Continue Chat' : 'New Chat'}
      </h2>

      {
        currentSession && (
          <div class="mb-6 overflow-y-auto max-h-1/2">
            <ChatHistory history={currentSession.history} />
          </div>
        )
      }

      <PromptForm
        sessionId={sessionId}
        specializations={specializations}
        client:load
      />
    </div>

    <!-- Right Column: Skill Proposals -->
    <div class="border-l p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Skill Proposals</h2>
      {
        sessionId ? (
          <SkillProposalList
            client:load
            proposals={skillProposals}
            specializations={specializations}
          />
        ) : (
          <div class="text-gray-500 italic">
            Select a chat session or start a new one to see skill proposals
          </div>
        )
      }
    </div>
  </div>
</Layout>
