---
import Layout from '@app/components/layouts/Layout.astro';
import {
  CloudDownloadIcon,
  CombinedIngestionIcon,
  RelationIcon,
  SkillsIcon,
  SpecializationIcon,
} from '@app/components/ui/icons';
---

<Layout>
  <div class="aw-container">
    <h1 class="text-3xl font-bold mb-8 text-center text-gray-800">
      Codarashi HQ Dashboard
    </h1>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Specializations Card -->
      <a href="/specializations" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <SpecializationIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            Specializations
          </h2>
          <p class="text-gray-600 text-center">
            Manage software specializations and expertise areas
          </p>
        </div>
      </a>

      <!-- Skills Ingestion Card -->
      <a href="/skills-ingestion" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <CloudDownloadIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            Skills Ingestion
          </h2>
          <p class="text-gray-600 text-center">
            Import and process new skills from external sources
          </p>
        </div>
      </a>

      <!-- Skills Card -->
      <a href="/skills" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <SkillsIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            Skills
          </h2>
          <p class="text-gray-600 text-center">
            View and manage technical skills and competencies
          </p>
        </div>
      </a>

      <!-- Relations CRUD Card -->
      <a href="/skill-relations" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <RelationIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            Relations Management
          </h2>
          <p class="text-gray-600 text-center">
            View, edit, and manage all skill relations in the system
          </p>
        </div>
      </a>

      <!-- Relation Ingestion Card -->
      <a href="/relation-ingestion" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <RelationIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            Relation Ingestion
          </h2>
          <p class="text-gray-600 text-center">
            Import and manage relationships between entities
          </p>
        </div>
      </a>

      <!-- Combined Ingestion Card -->
      <a href="/ingestion" class="group">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center h-full border border-gray-100 hover:border-blue-300"
        >
          <div
            class="w-16 h-16 mb-4 text-blue-500 group-hover:text-blue-600 transition-colors duration-300"
          >
            <CombinedIngestionIcon className="w-full h-full" client:only="react" />
          </div>
          <h2
            class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors duration-300"
          >
            AI Assistant
          </h2>
          <p class="text-gray-600 text-center">
            Combined skills and relations ingestion in one chat
          </p>
        </div>
      </a>
    </div>
  </div>
</Layout>

<style>
  .grid {
    --auto-grid-min-size: 300px;
  }
</style>
