---
import { actions } from 'astro:actions';

import Layout from '@app/components/layouts/Layout.astro';
import ChatSessionList from '@app/components/relation-ingestion/chat-session.list.astro';
import { PromptForm } from '@app/components/relation-ingestion/prompt.form';
import { RelationProposalList } from '@app/components/relation-ingestion/relation-proposal-list';
import ChatHistory from '@app/components/shared/chat-history.astro';
import type { RelationProposal } from '@app/entities/rel-proposal.entity';

const sessionId = Astro.url.searchParams.get('session_id');

// If we have a session ID, fetch the session details and relation proposals
let currentSession = null;
let relationProposals: RelationProposal[] = [];

if (sessionId) {
  try {
    currentSession = await Astro.callAction(actions.chatSessions.get.orThrow, {
      id: sessionId,
    });

    relationProposals = await Astro.callAction(
      actions.relProposals.listBySessionId.orThrow,
      { sessionId }
    );
  } catch (error) {
    console.error('Error fetching session data:', error);
  }
}

const specializations = await Astro.callAction(
  actions.swSpecializations.list.orThrow,
  undefined
);
---

<Layout>
  <div class="grid grid-cols-4 gap-4 h-full">
    <!-- Left Column: Chat Sessions List -->
    <div class="border-r p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Chats with Relations</h2>
      <ChatSessionList />
    </div>

    <!-- Middle Column: Prompt Form and Chat History -->
    <div class="p-4 col-span-2 max-h-dvh">
      <h2 class="text-xl font-semibold mb-4">
        {sessionId ? 'Continue Chat' : 'New Chat'}
      </h2>

      {
        currentSession && (
          <div class="mb-6 overflow-y-auto max-h-1/2">
            <ChatHistory history={currentSession.history} />
          </div>
        )
      }

      <PromptForm sessionId={sessionId ?? undefined} client:load />
    </div>

    <!-- Right Column: Relation Proposals -->
    <div class="border-l p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Relation Proposals</h2>
      {
        sessionId ? (
          <RelationProposalList
            client:load
            proposals={relationProposals}
            specializations={specializations}
          />
        ) : (
          <div class="text-gray-500 italic">
            Select a chat session or start a new one to see relation proposals
          </div>
        )
      }
    </div>
  </div>
</Layout>
