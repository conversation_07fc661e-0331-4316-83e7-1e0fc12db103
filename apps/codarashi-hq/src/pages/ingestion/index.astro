---
import { actions } from 'astro:actions';

import { Artifacts } from '@app/components/ingestion/artifacts';
import ChatSessionList from '@app/components/ingestion/chat-session.list.astro';
import { PromptForm } from '@app/components/ingestion/prompt.form';
import Layout from '@app/components/layouts/Layout.astro';
import ChatHistory from '@app/components/shared/chat-history.astro';

const sessionId = Astro.url.searchParams.get('session_id');

const chatSession = sessionId
  ? await Astro.callAction(actions.chatSessions.get.orThrow, { id: sessionId })
  : null;
---

<Layout>
  <div class="grid grid-cols-4 gap-4 h-full">
    <!-- Left Column: Chat Sessions List -->
    <div class="border-r p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Chats with Relations</h2>
      <ChatSessionList />
    </div>

    <!-- Middle Column: Prompt Form and Chat History -->
    <div class="p-4 col-span-2 max-h-dvh">
      <h2 class="text-xl font-semibold mb-2">
        {sessionId ? 'Continue Chat' : 'New Chat'}
      </h2>

      {
        chatSession && (
          <div class="mb-2 overflow-y-auto max-h-1/2">
            <ChatHistory history={chatSession.history} />
          </div>
        )
      }

      <PromptForm sessionId={sessionId ?? undefined} client:load />
    </div>

    <!-- Right Column: Artifacts -->
    <div class="border-l p-4 overflow-y-auto h-dvh">
      <h2 class="text-xl font-semibold mb-4">Artifacts</h2>
      {
        sessionId ? (
          <Artifacts sessionId={sessionId} client:only="react" />
        ) : (
          <div class="text-gray-500 italic">
            Select a chat session or start a new one to see artifacts
          </div>
        )
      }
    </div>
  </div>
</Layout>
