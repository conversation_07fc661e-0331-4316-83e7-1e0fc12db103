import { type FC } from 'react';

import { OverlayButton } from '@app/components/ui/overlay-button';
import type {
  SkillProposal,
  SkillProposalStatus,
} from '@app/entities/skill-proposal.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { EditSkillProposalForm } from './proposal.form';

export const SkillProposalList: FC<{
  proposals: SkillProposal[];
  specializations: SoftwareSpecialization[];
}> = ({ proposals, specializations }) => {
  if (proposals.length === 0) {
    return (
      <div className="text-gray-500 italic">
        No skill proposals for this chat session
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {proposals.map((proposal) => {
        return (
          <div key={proposal.id} className="border rounded p-3">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">{proposal.skill.name}</h3>

              <div className="flex gap-2 items-center">
                <span
                  className={`text-xs px-2 py-1 rounded ${getStatusColor(
                    proposal.status
                  )}`}
                >
                  {proposal.status}
                </span>

                <OverlayButton
                  disabled={proposal.status === 'accepted'}
                  className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                  dialog={
                    <EditSkillProposalForm
                      proposal={proposal}
                      specializations={specializations}
                    />
                  }
                >
                  Edit
                </OverlayButton>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

function getStatusColor(status: SkillProposalStatus): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'accepted':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'archived':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
