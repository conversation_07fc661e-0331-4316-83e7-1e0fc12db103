import { actions } from 'astro:actions';
import { useState } from 'react';

import { Button } from '@app/components/ui/button';
import { skillCategories } from '@app/entities/shared';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { cn } from '@app/utils/cn';

type PromptFormProps = {
  sessionId: string | null;
  specializations: SoftwareSpecialization[];
};

export const PromptForm = ({ sessionId, specializations }: PromptFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    const form = e.currentTarget;

    try {
      const formData = new FormData(form);

      const result = await actions.skillPrompts.generate.orThrow(formData);

      if (!sessionId) {
        history.pushState(null, '', `?session_id=${result.session_id}`);
      }

      window.location.reload();
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(
        'An error occurred while submitting the form. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="aw-container">
      <form className={cn('aw-form')} onSubmit={handleSubmit}>
        <input type="hidden" name="session_id" value={sessionId || ''} />

        <fieldset className="aw-form-field">
          <legend className="mb-2">Specializations</legend>
          <div className="flex flex-wrap gap-x-4 gap-y-2">
            {specializations.map((specialization) => (
              <div key={specialization.id} className="flex items-center gap-1">
                <input
                  type="checkbox"
                  id={specialization.id}
                  name="specializations"
                  value={specialization.id}
                  className="h-4 w-4 cursor-pointer"
                />
                <label
                  htmlFor={specialization.id}
                  className="text-sm cursor-pointer hover:text-blue-600"
                >
                  {specialization.name}
                </label>
              </div>
            ))}
          </div>
        </fieldset>

        <fieldset className="aw-form-field mt-3">
          <legend className="mb-2">Categories</legend>
          <div className="flex flex-wrap gap-x-4 gap-y-2">
            {skillCategories.map((category) => (
              <div key={category} className="flex items-center gap-1 group">
                <input
                  type="checkbox"
                  id={`skill_category_${category}`}
                  name="categories"
                  value={category}
                  className="h-4 w-4 cursor-pointer"
                />
                <label
                  htmlFor={`skill_category_${category}`}
                  className="text-sm cursor-pointer hover:text-blue-600"
                >
                  {category}
                </label>
              </div>
            ))}
          </div>
        </fieldset>

        <div className="aw-form-field mt-4">
          <label htmlFor="prompt-message">Message</label>
          <textarea
            name="message"
            id="prompt-message"
            className="aw-form-control"
            placeholder={
              sessionId
                ? 'Continue the conversation...'
                : "Describe the skills you're looking for..."
            }
            rows={4}
          />
        </div>

        <div>
          <Button type="submit" isLoading={isLoading}>
            {sessionId ? 'Send' : 'Generate'}
          </Button>
        </div>

        {error && <div className="aw-banner error">{error}</div>}
      </form>
    </div>
  );
};
