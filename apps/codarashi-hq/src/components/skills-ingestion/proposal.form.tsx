import { SkillLinkType } from '@app/entities/shared';
import type {
  SkillProposal,
  UpdateDto,
} from '@app/entities/skill-proposal.entity';
import { skillProposalStatus } from '@app/entities/skill-proposal.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { formDataToJson } from '@app/utils/form-data';
import { actions } from 'astro:actions';
import { useState } from 'react';
import { Button } from '../ui/button';
import { Dialog } from '../ui/dialog';
import { TrashIcon } from '../ui/icons/trash-icon';

export const EditSkillProposalForm: React.FC<{
  proposal: SkillProposal;
  specializations: SoftwareSpecialization[];
}> = ({ proposal, specializations, ...rest }) => {
  const [links, setLinks] = useState<
    Array<SkillProposal['skill']['links'][number] | null>
  >(proposal.skill.links?.slice() ?? []);

  const submit = async (formData: FormData) => {
    const payload = formDataToJson<UpdateDto>(formData);

    await actions.skillProposals.update
      .orThrow({
        id: proposal.id,
        payload,
      })
      .then(() => {
        window.location.reload();
      });
  };

  const handleAddLink = () => {
    setLinks((existingLinks) => [
      ...existingLinks,
      {
        type: 'other',
        url: 'https://example.com',
      } as const,
    ]);
  };

  const handleRemoveLink = (index: number) => {
    setLinks((existingLinks) => {
      const copiedLinks = [...existingLinks];
      copiedLinks[index] = null;
      return copiedLinks;
    });
  };

  return (
    <Dialog isModal={true} closeOnClickOutside={true} {...rest}>
      <Dialog.Header>Edit Skill Proposal</Dialog.Header>
      <Dialog.Content>
        <Dialog.Form>
          <div className="mb-4">
            <label htmlFor="skill.name" className="block text-sm font-medium">
              Skill Name
            </label>
            <input
              id="skill.name"
              required
              className={`aw-form-control`}
              name="skill.name"
              defaultValue={proposal.skill.name}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="status" className="block text-sm font-medium">
              Status
            </label>
            <select
              id="status"
              required
              className="aw-form-control"
              name="status"
              defaultValue={proposal.status}
            >
              <option value={skillProposalStatus[0]}>Pending</option>
              <option value={skillProposalStatus[1]}>Accepted</option>
              <option value={skillProposalStatus[2]}>Rejected</option>
              <option value={skillProposalStatus[3]}>Archived</option>
            </select>
          </div>

          <div className="mb-4">
            <label
              htmlFor="skill.category"
              className="block text-sm font-medium"
            >
              Category
            </label>
            <input
              id="skill.category"
              className={`aw-form-control`}
              name="skill.category"
              defaultValue={proposal.skill.category}
              readOnly
              disabled
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="skill.description"
              className="block text-sm font-medium"
            >
              Description
            </label>
            <textarea
              id="skill.description"
              required
              className={`aw-form-control`}
              rows={6}
              name="skill.description"
              defaultValue={proposal.skill.description}
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="skill.specializations"
              className="block text-sm font-medium"
            >
              Specializations
            </label>
            <div className="mt-2 space-y-2 flex flex-wrap gap-2">
              {specializations.map((spec) => (
                <div key={spec.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`spec-${spec.id}`}
                    name="skill.specializations"
                    value={spec.id}
                    defaultChecked={proposal.skill.specializations?.includes(
                      spec.id
                    )}
                    className="mr-2"
                  />
                  <label htmlFor={`spec-${spec.id}`}>{spec.name}</label>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-4">
            <label
              htmlFor="skill.alternative_names"
              className="block text-sm font-medium"
            >
              Alternative Names
            </label>
            <textarea
              id="skill.alternative_names"
              className={`aw-form-control`}
              rows={2}
              placeholder="Comma-separated values"
              name="skill.alternative_names"
              defaultValue={proposal.skill.alternative_names ?? ''}
            />
          </div>

          <div className="mb-4 flex flex-col gap-2 p-2 border rounded border-gray-200">
            <div className="flex gap-2 items-center">
              <h3>Links</h3>

              <Button
                onClick={handleAddLink}
                type="button"
                variant="outline"
                size="sm"
              >
                + Add
              </Button>
            </div>

            {links.map((link, index) => {
              if (!link) {
                return null;
              }

              return (
                <div key={index} className="flex gap-x-2 mb-2 flex-wrap">
                  <select
                    name={`skill.links.${index}.type`}
                    className="border rounded px-2 py-1"
                    aria-label="Link Type"
                    defaultValue={links[index]?.type || 'other'}
                  >
                    {Object.entries(SkillLinkType.Values).map(
                      ([key, value]) => (
                        <option key={key} value={value}>
                          {value}
                        </option>
                      )
                    )}
                  </select>

                  <input
                    type="text"
                    placeholder="URL"
                    aria-label="URL"
                    name={`skill.links.${index}.url`}
                    className="border rounded px-2 py-1"
                    defaultValue={links[index]?.url || ''}
                  />

                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Remove"
                    onClick={() => handleRemoveLink(index)}
                  >
                    <TrashIcon />
                  </Button>
                </div>
              );
            })}
          </div>

          <div className="mb-4">
            <label htmlFor="user_notes" className="block text-sm font-medium">
              User Notes
            </label>
            <textarea
              id="user_notes"
              name="user_notes"
              className={`aw-form-control`}
              rows={2}
              defaultValue={proposal.user_notes ?? ''}
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="user_feedback"
              className="block text-sm font-medium"
            >
              User Feedback (1-5)
            </label>
            <input
              type="number"
              id="user_feedback"
              name="user_feedback"
              className={`aw-form-control`}
              min={1}
              max={5}
              defaultValue={proposal.user_feedback ?? 1}
            />
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Dialog.CloseButton variant="outline" size="sm">
              Cancel
            </Dialog.CloseButton>
            <Dialog.SubmitButton variant="default" action={submit} size="sm">
              Save Changes
            </Dialog.SubmitButton>
          </div>
        </Dialog.Form>
      </Dialog.Content>
    </Dialog>
  );
};
