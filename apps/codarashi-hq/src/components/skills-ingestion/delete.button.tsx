import { actions } from 'astro:actions';

import { Dialog } from '@app/components/ui/dialog';
import { OverlayButton } from '@app/components/ui/overlay-button';
import { type SkillProposal } from '@app/entities/skill-proposal.entity';

export function DeleteSkillProposal({ proposal }: { proposal: SkillProposal }) {
  const handleDelete = async () => {
    try {
      await actions.skillProposals.delete.orThrow({ id: proposal.id });
      window.location.reload();
    } catch (error) {
      console.error('Failed to delete skill proposal:', error);
    }
  };

  return (
    <OverlayButton
      dialog={
        <Dialog isModal={true}>
          <Dialog.Header>Confirm Deletion</Dialog.Header>
          <Dialog.Content>
            <Dialog.Form>
              <div className="space-y-4">
                <p>
                  Are you sure you want to delete the skill proposal "
                  {proposal.skill.name}"? This action cannot be undone.
                </p>
                <div className="flex justify-end gap-2">
                  <Dialog.CloseButton variant="outline">
                    Cancel
                  </Dialog.CloseButton>
                  <Dialog.SubmitButton
                    variant="destructive"
                    action={handleDelete}
                  >
                    Delete
                  </Dialog.SubmitButton>
                </div>
              </div>
            </Dialog.Form>
          </Dialog.Content>
        </Dialog>
      }
      variant="destructive"
      size="sm"
    >
      Delete
    </OverlayButton>
  );
}
