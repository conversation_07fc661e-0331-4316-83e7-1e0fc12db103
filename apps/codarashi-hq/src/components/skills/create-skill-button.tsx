import { OverlayButton } from '@app/components/ui/overlay-button';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { AddIcon } from '@app/components/ui/icons';
import { SkillForm } from './skill.form';

type CreateSkillButtonProps = {
  specializations: SoftwareSpecialization[];
};

export function CreateSkillButton({ specializations }: CreateSkillButtonProps) {
  return (
    <OverlayButton
      dialog={<SkillForm specializations={specializations} />}
      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-200 flex items-center gap-2"
    >
      <AddIcon className="h-5 w-5" />
      Create Skill
    </OverlayButton>
  );
}
