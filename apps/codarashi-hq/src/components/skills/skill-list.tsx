import { actions } from 'astro:actions';

import { OverlayButton } from '@app/components/ui/overlay-button';
import { SkillCategory } from '@app/entities/shared';
import type { Skill } from '@app/entities/skill.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { DeleteSkillButton } from './delete-skill-button';
import { SkillForm } from './skill.form';

type SkillListProps = {
  skills: Skill[];
  specializations: SoftwareSpecialization[];
};

export function SkillList({ skills, specializations }: SkillListProps) {
  const handleDelete = async (id: Skill['id']) => {
    try {
      await actions.skills.delete.orThrow({ id });
      window.location.reload();
    } catch (error) {
      console.error('Failed to delete skill:', error);
    }
  };

  const getCategoryLabel = (category: SkillCategory) => {
    const labels = {
      language: 'Language',
      lib_or_framework: 'Library/Framework',
      tool: 'Tool',
      technique: 'Technique',
      soft: 'Soft Skill',
    };
    return labels[category] || category;
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {skills.map((skill) => (
          <div
            key={skill.id}
            className="border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-semibold">{skill.name}</h2>
              <span className="px-2 py-1 text-xs rounded-full bg-gray-100">
                {getCategoryLabel(skill.category)}
              </span>
            </div>

            {skill.description && (
              <p className="text-gray-600 mb-4 line-clamp-3">
                {skill.description}
              </p>
            )}

            <div className="flex flex-wrap gap-2 mb-4">
              {skill.specializations.length > 0 && (
                <div className="w-full">
                  <span className="text-sm font-medium">Specializations:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {skill.specializations.map((specId) => {
                      const spec = specializations.find((s) => s.id === specId);
                      return (
                        <span
                          key={specId}
                          className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800"
                        >
                          {spec?.name || specId}
                        </span>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2 mt-4">
              <OverlayButton
                dialog={
                  <SkillForm skill={skill} specializations={specializations} />
                }
                variant="outline"
                size="sm"
              >
                Edit
              </OverlayButton>
              <DeleteSkillButton skill={skill} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
