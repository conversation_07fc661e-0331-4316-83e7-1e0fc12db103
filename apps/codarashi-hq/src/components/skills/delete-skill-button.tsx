import { actions } from 'astro:actions';

import { Dialog } from '@app/components/ui/dialog';
import { OverlayButton } from '@app/components/ui/overlay-button';
import type { Skill } from '@app/entities/skill.entity';

type DeleteSkillButtonProps = {
  skill: Skill;
};

export function DeleteSkillButton({ skill }: DeleteSkillButtonProps) {
  const handleDelete = async (id: Skill['id']) => {
    try {
      await actions.skills.delete.orThrow({ id });
      window.location.reload();
    } catch (error) {
      console.error('Failed to delete skill:', error);
    }
  };

  return (
    <OverlayButton
      dialog={
        <Dialog isModal={true}>
          <Dialog.Header>Confirm Deletion</Dialog.Header>
          <Dialog.Content>
            <Dialog.Form>
              <div className="space-y-4">
                <p>
                  Are you sure you want to delete the skill "{skill.name}"? This
                  action cannot be undone.
                </p>
                <div className="flex justify-end gap-2">
                  <Dialog.CloseButton variant="outline">
                    Cancel
                  </Dialog.CloseButton>
                  <Dialog.SubmitButton
                    variant="destructive"
                    action={async () => {
                      await handleDelete(skill.id);
                    }}
                  >
                    Delete
                  </Dialog.SubmitButton>
                </div>
              </div>
            </Dialog.Form>
          </Dialog.Content>
        </Dialog>
      }
      variant="destructive"
      size="sm"
    >
      Delete
    </OverlayButton>
  );
}
