import { actions } from 'astro:actions';
import { useState } from 'react';

import { SkillCategory, SkillLinkType } from '@app/entities/shared';
import type { Skill } from '@app/entities/skill.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { Button } from '../ui/button';
import { Dialog } from '../ui/dialog';
import { TrashIcon } from '../ui/icons/trash-icon';

type SkillFormProps = {
  skill?: Skill;
  specializations: SoftwareSpecialization[];
};

export const SkillForm: React.FC<SkillFormProps> = ({
  skill,
  specializations,
  ...rest
}) => {
  const isEditMode = !!skill;
  const [links, setLinks] = useState<Array<Skill['links'][number] | null>>(
    skill?.links.slice() ?? []
  );

  const submit = async (formData: FormData) => {
    try {
      if (isEditMode && skill) {
        await actions.skills.update.orThrow(formData);
      } else {
        await actions.skills.create.orThrow(formData);
      }
      window.location.reload();
    } catch (error) {
      console.error('Error saving skill:', error);
      alert('Failed to save skill. Please try again.');
    }
  };

  const getCategoryLabel = (category: SkillCategory) => {
    const labels = {
      language: 'Language',
      lib_or_framework: 'Library/Framework',
      tool: 'Tool',
      technique: 'Technique',
      soft: 'Soft Skill',
    };
    return labels[category] || category;
  };

  const handleAddLink = () => {
    setLinks((existingLinks) => [
      ...existingLinks,
      {
        type: 'other',
        url: 'https://example.com',
      } as const,
    ]);
  };

  const handleRemoveLink = (index: number) => {
    setLinks((existingLinks) => {
      const copiedLinks = [...existingLinks];
      copiedLinks[index] = null;
      return copiedLinks;
    });
  };

  return (
    <Dialog
      isModal={true}
      closeOnClickOutside={true}
      {...rest}
      className="max-w-[750px]"
    >
      <Dialog.Header>
        {isEditMode ? 'Edit Skill' : 'Create Skill'}
      </Dialog.Header>
      <Dialog.Content>
        <Dialog.Form>
          <input type="hidden" name="id" value={skill?.id} />

          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium">
              Skill Name
            </label>
            <input
              id="name"
              required
              className="aw-form-control"
              name="name"
              defaultValue={skill?.name || ''}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="category" className="block text-sm font-medium">
              Category
            </label>
            <select
              id="category"
              required
              className="aw-form-control"
              name="category"
              defaultValue={skill?.category || 'language'}
            >
              {Object.entries(SkillCategory.Values).map(([key, value]) => (
                <option key={key} value={value}>
                  {getCategoryLabel(value as SkillCategory)}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium">
              Description
            </label>
            <textarea
              id="description"
              required
              className="aw-form-control"
              rows={4}
              name="description"
              defaultValue={skill?.description || ''}
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="specializations"
              className="block text-sm font-medium"
            >
              Specializations
            </label>
            <div className="mt-2 space-y-2 flex flex-wrap gap-2">
              {specializations.map((spec) => (
                <div key={spec.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`spec-${spec.id}`}
                    name="specializations"
                    value={spec.id}
                    defaultChecked={skill?.specializations.includes(spec.id)}
                    className="mr-2"
                  />
                  <label htmlFor={`spec-${spec.id}`}>{spec.name}</label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex flex-col gap-2 p-2 border rounded border-gray-200">
            <div className="flex gap-2 items-center">
              <h3>Links</h3>

              <Button
                onClick={handleAddLink}
                type="button"
                variant="outline"
                size="sm"
              >
                + Add
              </Button>
            </div>

            {links.map((link, index) => {
              if (!link) {
                return null;
              }

              return (
                <div key={index} className="flex gap-x-2 mb-2 flex-wrap">
                  <select
                    name={`links.${index}.type`}
                    className="border rounded px-2 py-1"
                    aria-label="Link Type"
                    defaultValue={links[index]?.type || 'other'}
                  >
                    {Object.entries(SkillLinkType.Values).map(
                      ([key, value]) => (
                        <option key={key} value={value}>
                          {value}
                        </option>
                      )
                    )}
                  </select>

                  <input
                    type="text"
                    placeholder="URL"
                    aria-label="URL"
                    name={`links.${index}.url`}
                    className="border rounded px-2 py-1"
                    defaultValue={links[index]?.url || ''}
                  />

                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Remove"
                    onClick={() => handleRemoveLink(index)}
                  >
                    <TrashIcon />
                  </Button>
                </div>
              );
            })}
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Dialog.CloseButton variant="outline" size="sm">
              Cancel
            </Dialog.CloseButton>
            <Dialog.SubmitButton variant="default" action={submit} size="sm">
              {isEditMode ? 'Save Changes' : 'Create Skill'}
            </Dialog.SubmitButton>
          </div>
        </Dialog.Form>
      </Dialog.Content>
    </Dialog>
  );
};
