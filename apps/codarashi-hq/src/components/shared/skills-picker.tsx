import { actions } from 'astro:actions';
import { useCallback, useEffect, useState, type FC } from 'react';

import type { LeanSkill, Skill } from '@app/entities/skill.entity';
import { Autocomplete } from './autocomplete';

type Props = {
  initialValues?: Array<Skill['id']>;
  name: string;
  label: string;
  maxItems?: number;
};

export const SkillsPicker: FC<Props> = ({
  initialValues,
  name,
  label,
  maxItems,
}: Props) => {
  const [selectedSkills, setSelectedSkills] = useState<LeanSkill[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [isSearching, setSearching] = useState(false);
  const [searchedSkills, setSearchedSkills] = useState<LeanSkill[]>([]);

  useEffect(() => {
    if (isLoading || !initialValues || initialValues.length === 0) return;

    setLoading(true);

    actions.skills.listByIds
      .orThrow({ ids: initialValues })
      .then((res) => {
        setSelectedSkills(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [initialValues]);

  const handleChange = useCallback(
    (newValues: string[]) => {
      setSelectedSkills((currentlySelected) => {
        return newValues.map((skillId) => {
          const item =
            currentlySelected.find((skill) => skill.id === skillId) ||
            searchedSkills.find((skill) => skill.id === skillId);

          if (!item) throw new Error(`Skill with id ${skillId} not found`);

          return item;
        });
      });
    },
    [searchedSkills]
  );

  const handleSearch = useCallback((searchTerm: string) => {
    setSearching(true);

    actions.skills.search
      .orThrow({ query: searchTerm })
      .then((res) => {
        setSearchedSkills(res);
      })
      .finally(() => {
        setSearching(false);
      });
  }, []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Autocomplete
      label={label}
      name={name}
      value={selectedSkills.map((skill) => ({
        label: skill.name,
        value: skill.id,
      }))}
      limitReached={maxItems !== undefined && selectedSkills.length >= maxItems}
      onChange={handleChange}
      onSearch={handleSearch}
      options={searchedSkills
        .filter(
          (skill) => !selectedSkills.map((item) => item.id).includes(skill.id)
        )
        .map((skill) => ({
          label: skill.name,
          value: skill.id,
        }))}
      loading={isSearching}
    />
  );
};
