---
import { CheckCircleIcon, CodeBracketIcon } from '@app/components/ui/icons';
import type { ChatHistoryItem } from '@app/entities/shared';
import { ScrollIntoView } from '@app/components/ui/scroll-into-view';

interface Props {
  history: ChatHistoryItem[];
}

const { history } = Astro.props;

// Helper function to truncate long responses
const truncateText = (text: string, maxLength = 150) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};
---

<div
  class="flex flex-col gap-4 h-full overflow-auto"
>
  <div class="flex flex-col gap-3">
    {
      history.map((item, index) => (
        <div
          class="flex flex-col gap-2"
        >
          {/* User message - right aligned with different styling */}
          <div class="flex justify-end">
            <div class="bg-blue-50 p-3 rounded-lg max-w-[85%] border-blue-200 border shadow-sm">
              <div class="text-xs text-gray-500 mb-1 flex items-center justify-between">
                <span>You</span>
                <span>{new Date(item.created_at).toLocaleString()}</span>
              </div>
              <div class="whitespace-pre-wrap">
                {item.type === 'message'
                  ? item.user_prompt
                  : `Function call: ${item.function_call.name}`}
              </div>
            </div>
          </div>

          {/* AI response - left aligned with different styling based on type */}
          <div class="flex justify-start">
            {item.type === 'message' ? (
              <div class="bg-gray-50 p-3 rounded-lg max-w-[85%] border-gray-200 border shadow-sm">
                <div class="text-xs text-gray-500 mb-1 flex items-center">
                  <span class="flex items-center">
                    <CheckCircleIcon
                      className="w-3 h-3 mr-1"
                      client:only="react"
                    />
                    AI Response
                  </span>
                </div>
                <div class="whitespace-pre-wrap">{item.model_response}</div>
              </div>
            ) : (
              <div class="bg-gray-50 p-3 rounded-lg max-w-[85%] border-gray-200 border shadow-sm">
                <div class="text-xs text-gray-500 mb-1 flex items-center justify-between">
                  <span class="flex items-center">
                    <CodeBracketIcon
                      className="w-3 h-3 mr-1"
                      client:only="react"
                    />
                    Function Call
                  </span>
                  <span class="font-mono text-xs bg-gray-100 px-1 rounded">
                    {item.function_call.name}
                  </span>
                </div>
                <div class="flex flex-col gap-1">
                  <div class="text-sm text-gray-700">{item.comment}</div>
                  {item.function_response && (
                    <div class="mt-1 pt-1 border-t border-gray-200">
                      <details>
                        <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                          View response preview
                        </summary>
                        <div class="mt-1 text-xs font-mono bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                          {truncateText(item.function_response, 300)}
                        </div>
                      </details>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      ))
    }
    {history.length > 0 && <ScrollIntoView client:only="react" />}
  </div>
</div>
