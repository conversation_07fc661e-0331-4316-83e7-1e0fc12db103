import { actions } from 'astro:actions';

import type { ChatSession } from '@app/entities/shared';
import { Dialog } from '../ui/dialog';
import { OverlayButton } from '../ui/overlay-button';

type EditableTitleProps = {
  initialTitle: string;
  sessionId: ChatSession['id'];
};

export const EditableTitle = ({
  initialTitle,
  sessionId,
}: EditableTitleProps) => {
  const saveTitle = async (formData: FormData) => {
    const newTitle = formData.get('title') as string;
    const trimmedTitle = newTitle.trim();

    if (trimmedTitle) {
      await actions.chatSessions.updateTitle({
        id: sessionId,
        display_title: trimmedTitle,
      });

      window.location.reload();
    }
  };

  return (
    <div className="relative flex items-center gap-2">
      <div className="font-medium truncate">{initialTitle}</div>
      <OverlayButton
        variant="ghost"
        size="sm"
        className="text-gray-500 hover:text-gray-700 p-1"
        aria-label="Edit title"
        onClick={(event) => {
          event.stopPropagation();
          event.preventDefault();
          return false;
        }}
        dialog={({ isOpen, close }) => (
          <Dialog isOpen={isOpen} onClose={close}>
            <Dialog.Header>Edit Title</Dialog.Header>
            <Dialog.Content>
              <Dialog.Form>
                <div className="mb-4">
                  <label
                    htmlFor="title"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Title
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    defaultValue={initialTitle}
                    className="w-full border rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
                    autoFocus
                  />
                </div>
              </Dialog.Form>
            </Dialog.Content>
            <Dialog.Footer>
              <Dialog.CloseButton variant="outline" size="sm">
                Cancel
              </Dialog.CloseButton>
              <Dialog.SubmitButton
                variant="default"
                action={saveTitle}
                size="sm"
              >
                Save
              </Dialog.SubmitButton>
            </Dialog.Footer>
          </Dialog>
        )}
      >
        &#9998; {/* Pencil icon in HTML */}
      </OverlayButton>
    </div>
  );
};
