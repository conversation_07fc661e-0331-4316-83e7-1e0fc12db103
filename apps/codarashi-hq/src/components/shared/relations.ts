import type { RelationProposal } from '@app/entities/rel-proposal.entity';
import { RelationType } from '@app/entities/shared';
import type { SkillRelation } from '@app/entities/skill-relation.entity';

export function getRelationTypeLabel(type: RelationType): string {
  switch (type) {
    case RelationType.alternative:
      return 'Alternative Skills';
    case RelationType.version:
      return 'Version Relationship';
    case RelationType.hierarchy:
      return 'Hierarchy Relationship';
    case RelationType.ecosystem:
      return 'Ecosystem Bundle';
    case RelationType.topical:
      return 'Topical Bundle';
    default:
      return 'Relation';
  }
}

export function getRelationSummary(
  relation: RelationProposal['relation'] | SkillRelation
): string {
  switch (relation.type) {
    case RelationType.alternative:
      if ('skill_names' in relation) {
        // Handle RelationProposal
        return `${relation.skill_names.length} skills with strength ${relation.strength}`;
      } else {
        // Handle SkillRelation
        return `${relation.skill_ids.length} skills with strength ${relation.strength}`;
      }

    case RelationType.version:
      if ('skill_names' in relation) {
        // Handle RelationProposal
        return `${relation.skill_names.length} skills`;
      } else {
        // Handle SkillRelation
        return `${relation.skill_ids.length} skills`;
      }

    case RelationType.hierarchy:
      if ('source_skill_name' in relation) {
        // Handle RelationProposal
        return `${
          relation.target_skill_names.length + 1
        } skills with strength ${relation.strength}`;
      } else {
        // Handle SkillRelation
        return `${relation.target_skill_ids.length + 1} skills with strength ${
          relation.strength
        }`;
      }

    case RelationType.ecosystem:
    case RelationType.topical:
      if ('skill_names' in relation) {
        // Handle RelationProposal
        return `${relation.skill_names.length} skills`;
      } else {
        // Handle SkillRelation
        return `${relation.skill_ids.length} skills`;
      }

    default:
      return '';
  }
}
