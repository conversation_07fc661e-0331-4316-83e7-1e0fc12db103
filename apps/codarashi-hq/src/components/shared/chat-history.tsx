import React from 'react';

import { CheckCircleIcon, CodeBracketIcon } from '@app/components/ui/icons';
import { ScrollIntoView } from '@app/components/ui/scroll-into-view';
import type { ChatHistoryItem } from '@app/entities/shared';

interface Props {
  history: ChatHistoryItem[];
}

const truncateText = (text: string, maxLength = 150) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const ChatHistory: React.FC<Props> = ({ history }) => {
  return (
    <div className="flex flex-col gap-4 h-full overflow-auto">
      <div className="flex flex-col gap-3">
        {history.map((item, index) => (
          <div key={index} className="flex flex-col gap-2">
            {/* User message - right aligned with different styling */}
            <div className="flex justify-end">
              <div className="bg-blue-50 p-3 rounded-lg max-w-[85%] border-blue-200 border shadow-sm">
                <div className="text-xs text-gray-500 mb-1 flex items-center justify-between">
                  <span>You</span>
                  <span>{new Date(item.created_at).toLocaleString()}</span>
                </div>
                <div className="whitespace-pre-wrap">
                  {item.type === 'message'
                    ? item.user_prompt
                    : `Function call: ${item.function_call?.name}`}
                </div>
              </div>
            </div>

            {/* AI response - left aligned with different styling based on type */}
            <div className="flex justify-start">
              {item.type === 'message' ? (
                <div className="bg-gray-50 p-3 rounded-lg max-w-[85%] border-gray-200 border shadow-sm">
                  <div className="text-xs text-gray-500 mb-1 flex items-center">
                    <span className="flex items-center">
                      <CheckCircleIcon className="w-3 h-3 mr-1" />
                      AI Response
                    </span>
                  </div>
                  <div className="whitespace-pre-wrap">
                    {item.model_response}
                  </div>
                </div>
              ) : (
                <div className="bg-gray-50 p-3 rounded-lg max-w-[85%] border-gray-200 border shadow-sm">
                  <div className="text-xs text-gray-500 mb-1 flex items-center justify-between">
                    <span className="flex items-center">
                      <CodeBracketIcon className="w-3 h-3 mr-1" />
                      Function Call
                    </span>
                    <span className="font-mono text-xs bg-gray-100 px-1 rounded">
                      {item.function_call?.name}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <div className="text-sm text-gray-700">{item.comment}</div>
                    {item.function_response && (
                      <div className="mt-1 pt-1 border-t border-gray-200">
                        <details>
                          <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                            View response preview
                          </summary>
                          <div className="mt-1 text-xs font-mono bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                            {truncateText(item.function_response, 300)}
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
        {history.length > 0 && <ScrollIntoView />}
      </div>
    </div>
  );
};

export default ChatHistory;
