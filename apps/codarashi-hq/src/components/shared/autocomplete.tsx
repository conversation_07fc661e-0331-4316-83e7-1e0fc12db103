import { useDebounce } from '@uidotdev/usehooks';
import { useEffect, useId, useState, type ReactNode } from 'react';

export type Props = {
  name: string;
  label: ReactNode;

  value: Option[];
  onChange: (newSelected: string[]) => void;

  onSearch: (searchTerm: string) => void;
  options: Option[];
  loading?: boolean;
  limitReached?: boolean;
};

export function Autocomplete({
  value,
  name,
  onChange,
  onSearch,
  label,
  options,
  loading,
  limitReached = false,
}: Props) {
  const [inputValue, setInputValue] = useState('');
  const debouncedInput = useDebounce(inputValue, 500);

  useEffect(() => {
    if (debouncedInput) {
      onSearch(debouncedInput);
    }
  }, [debouncedInput, onSearch]);

  const handleSelect = (option: string) => {
    if (!value.map((v) => v.value).includes(option)) {
      onChange([...value.map((v) => v.value), option]);
      setInputValue('');
    }
  };

  const handleUnselect = (option: string) => {
    onChange(value.filter((v) => v.value !== option).map((v) => v.value));
  };

  const datalistId = useId();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    const match = options.find((opt) => opt.value === newValue);

    if (match) {
      handleSelect(match.value);
    }
  };

  return (
    <div className="aw-form-field">
      {label && <label>{label}</label>}
      <input
        type="text"
        className="aw-form-control"
        value={inputValue}
        onChange={handleInputChange}
        list={datalistId}
        placeholder="Type to search..."
        autoComplete="off"
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            const match = options.find(
              (opt) => opt.value.toLowerCase() === inputValue.toLowerCase()
            );
            if (match) handleSelect(match.value);
          }
        }}
      />
      <datalist id={datalistId}>
        {options.map((opt) => (
          <option key={opt.value} value={opt.value} disabled={limitReached}>
            {opt.label}
          </option>
        ))}
      </datalist>

      {limitReached && (
        <div className="mt-2 text-xs text-red-500">Limit reached</div>
      )}

      {!loading && value.length === 0 && debouncedInput && (
        <div className="mt-2 text-xs">No results</div>
      )}

      {loading && <div className="mt-2 text-xs">Searching...</div>}

      <div className="mt-2 flex flex-wrap gap-2">
        {value.map((selected) => (
          <label key={selected.value} className="flex items-center gap-1">
            <input
              type="checkbox"
              checked
              name={name}
              value={selected.value}
              onChange={(event) =>
                event.target.checked
                  ? handleSelect(selected.value)
                  : handleUnselect(selected.value)
              }
              className="mr-2 aw-form-control"
            />
            {selected.label}
          </label>
        ))}
      </div>
    </div>
  );
}

export type Option = {
  label: ReactNode;
  value: string;
};
