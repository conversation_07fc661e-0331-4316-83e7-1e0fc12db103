import { actions } from 'astro:actions';

import type {
  RelationProposal,
  UpdateDto,
} from '@app/entities/rel-proposal.entity';
import { relationProposalStatus } from '@app/entities/rel-proposal.entity';
import { RelationType } from '@app/entities/shared';
import { formDataToJson } from '@app/utils/form-data';
import { Dialog } from '../ui/dialog';

type RelationProposalFormProps = {
  proposal: RelationProposal;
};

export const EditRelationProposalForm: React.FC<RelationProposalFormProps> = ({
  proposal,
  ...rest
}) => {
  const submit = async (formData: FormData) => {
    const payload = formDataToJson<UpdateDto>(formData);

    await actions.relProposals.update
      .orThrow({
        payload,
        id: proposal.id,
      })
      .then(() => {
        window.location.reload();
      });
  };

  // Determine the type of relation to render the appropriate form fields
  const relationType = proposal.relation.type;

  return (
    <Dialog isModal={true} closeOnClickOutside={true} {...rest}>
      <Dialog.Header>Edit Relation Proposal</Dialog.Header>
      <Dialog.Content>
        <Dialog.Form>
          {/* Common fields for all relation types */}
          <div className="mb-4">
            <label htmlFor="status" className="block text-sm font-medium">
              Status
            </label>
            <select
              id="status"
              required
              className="aw-form-control"
              name="status"
              defaultValue={proposal.status}
            >
              {relationProposalStatus.map((status) => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div className="aw-form-field mb-4">
            <label
              htmlFor="relation.name"
              className="block text-sm font-medium"
            >
              Name
            </label>
            <input
              type="text"
              id="relation.name"
              name="name"
              className="aw-form-control"
              defaultValue={
                'name' in proposal.relation
                  ? proposal.relation.name
                  : 'Untitled'
              }
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="relation.description"
              className="block text-sm font-medium"
            >
              Description
            </label>
            <textarea
              id="relation.description"
              className="aw-form-control"
              rows={4}
              name="relation.description"
              defaultValue={proposal.relation.description || ''}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="user_notes" className="block text-sm font-medium">
              User Notes
            </label>
            <textarea
              id="user_notes"
              name="user_notes"
              className="aw-form-control"
              rows={2}
              defaultValue={proposal.user_notes ?? ''}
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="user_feedback"
              className="block text-sm font-medium"
            >
              User Feedback (1-5)
            </label>
            <input
              type="number"
              id="user_feedback"
              name="user_feedback"
              className="aw-form-control"
              min={1}
              max={5}
              defaultValue={proposal.user_feedback ?? 1}
            />
          </div>

          <input type="hidden" name="relation.type" value={relationType} />

          {/* Relation type specific fields */}
          {relationType === RelationType.alternative && (
            <div className="mb-4">
              <label
                htmlFor="relation.strength"
                className="block text-sm font-medium"
              >
                Strength (1-5)
              </label>
              <input
                type="number"
                id="relation.strength"
                name="relation.strength"
                className="aw-form-control"
                min={1}
                max={5}
                defaultValue={proposal.relation.strength}
              />
              <div className="mt-4">
                <label
                  htmlFor="relation.skill_names"
                  className="block text-sm font-medium"
                >
                  Alternative Skills (comma-separated)
                </label>
                <textarea
                  id="relation.skill_names"
                  className="aw-form-control"
                  rows={3}
                  name="relation.skill_names"
                  defaultValue={proposal.relation.skill_names.join(', ')}
                />
              </div>
            </div>
          )}

          {relationType === RelationType.version && (
            <div className="mb-4">
              <label
                htmlFor="relation.skill_names"
                className="block text-sm font-medium"
              >
                Version Skills (comma-separated)
              </label>
              <textarea
                id="relation.skill_names"
                className="aw-form-control"
                rows={3}
                name="relation.skill_names"
                defaultValue={proposal.relation.skill_names.join(', ')}
              />
            </div>
          )}

          {relationType === RelationType.hierarchy && (
            <div className="mb-4">
              <label
                htmlFor="relation.source_skill_name"
                className="block text-sm font-medium"
              >
                Source Skill
              </label>
              <input
                type="text"
                id="relation.source_skill_name"
                name="relation.source_skill_name"
                className="aw-form-control"
                defaultValue={proposal.relation.source_skill_name}
              />

              <div className="mt-4">
                <label
                  htmlFor="relation.target_skill_names"
                  className="block text-sm font-medium"
                >
                  Target Skills (comma-separated)
                </label>
                <textarea
                  id="relation.target_skill_names"
                  className="aw-form-control"
                  rows={3}
                  name="relation.target_skill_names"
                  defaultValue={proposal.relation.target_skill_names.join(', ')}
                />
              </div>

              <div className="mt-4">
                <label
                  htmlFor="relation.strength"
                  className="block text-sm font-medium"
                >
                  Strength (1-5)
                </label>
                <input
                  type="number"
                  id="relation.strength"
                  name="relation.strength"
                  className="aw-form-control"
                  min={1}
                  max={5}
                  defaultValue={proposal.relation.strength}
                />
              </div>
            </div>
          )}

          {(relationType === RelationType.ecosystem ||
            relationType === RelationType.topical) && (
            <div className="mb-4">
              <div className="mt-4">
                <label
                  htmlFor="relation.skill_names"
                  className="block text-sm font-medium"
                >
                  Skills in Bundle (comma-separated)
                </label>
                <textarea
                  id="relation.skill_names"
                  className="aw-form-control"
                  rows={3}
                  name="relation.skill_names"
                  defaultValue={proposal.relation.skill_names.join(', ')}
                />
              </div>
            </div>
          )}

          <div className="flex justify-end gap-2 mt-6">
            <Dialog.CloseButton variant="outline" size="sm">
              Cancel
            </Dialog.CloseButton>
            <Dialog.SubmitButton variant="default" action={submit} size="sm">
              Save Changes
            </Dialog.SubmitButton>
          </div>
        </Dialog.Form>
      </Dialog.Content>
    </Dialog>
  );
};
