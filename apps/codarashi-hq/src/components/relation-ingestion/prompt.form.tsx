import { actions } from 'astro:actions';
import { useState } from 'react';

import { Button } from '@app/components/ui/button';
import { RelationType } from '@app/entities/shared';
import { cn } from '@app/utils/cn';

type Props = {
  sessionId?: string;
  proposalId?: string;
};

export const PromptForm = ({ sessionId, proposalId }: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    const form = e.currentTarget;

    try {
      const formData = new FormData(form);

      const result = await actions.relationPrompts.generate.orThrow(formData);

      if (!sessionId) {
        history.pushState(null, '', `?session_id=${result.session_id}`);
      }

      window.location.reload();
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(
        'An error occurred while submitting the form. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="aw-container">
      <form className={cn('aw-form')} onSubmit={handleSubmit}>
        <input type="hidden" name="session_id" value={sessionId || ''} />
        <input type="hidden" name="proposal_id" value={proposalId || ''} />

        <fieldset className="aw-form-field">
          <legend className="mb-2">Relation Type</legend>
          <div className="flex flex-wrap gap-x-4 gap-y-2">
            {Object.values(RelationType).map((type) => (
              <div key={type} className="flex items-center gap-1">
                <input
                  type="checkbox"
                  id={`relation_type_${type}`}
                  name="relation_type"
                  value={type}
                  className="h-4 w-4 cursor-pointer"
                />
                <label
                  htmlFor={`relation_type_${type}`}
                  className="text-sm cursor-pointer hover:text-blue-600"
                >
                  {type}
                </label>
              </div>
            ))}
          </div>
        </fieldset>

        <div className="aw-form-field mt-3">
          <label htmlFor="seed-skills">Seed Skills (comma-separated)</label>
          <textarea
            name="seed_skills"
            id="seed-skills"
            className="aw-form-control"
            placeholder="Enter seed skills separated by commas..."
            rows={3}
          />
        </div>

        <div className="aw-form-field mt-3">
          <label htmlFor="prompt-message">Message</label>
          <textarea
            name="message"
            id="prompt-message"
            className="aw-form-control"
            placeholder="Describe the relation or provide additional context..."
            rows={4}
          />
        </div>

        <div>
          <Button type="submit" isLoading={isLoading}>
            {sessionId ? 'Send' : 'Generate'}
          </Button>
        </div>

        {error && <div className="aw-banner error">{error}</div>}
      </form>
    </div>
  );
};
