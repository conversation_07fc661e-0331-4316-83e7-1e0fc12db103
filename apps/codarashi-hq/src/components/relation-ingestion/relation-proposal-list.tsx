import { type FC } from 'react';

import { OverlayButton } from '@app/components/ui/overlay-button';
import type {
  RelationProposal,
  RelationProposalStatus,
} from '@app/entities/rel-proposal.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { getRelationSummary, getRelationTypeLabel } from '../shared/relations';
import { EditRelationProposalForm } from './proposal.form';
import { RelatedSkills } from './related.skills';

export const RelationProposalList: FC<{
  proposals: RelationProposal[];
  specializations: SoftwareSpecialization[];
}> = ({ proposals, specializations }) => {
  if (proposals.length === 0) {
    return (
      <div className="text-gray-500 italic">
        No relation proposals for this chat session
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {proposals.map((proposal) => {
        return (
          <div
            key={proposal.id}
            className="border rounded p-3 flex flex-col gap-2"
          >
            <div className="flex justify-between items-center mb-2">
              <div className="flex flex-col">
                <h3 className="font-medium">
                  {getRelationTypeLabel(proposal.relation.type)}
                </h3>
                <div className="text-sm text-gray-600">
                  {getRelationSummary(proposal.relation)}
                </div>
              </div>

              <div className="flex gap-2 items-center">
                <span
                  className={`text-xs px-2 py-1 rounded ${getStatusColor(
                    proposal.status
                  )}`}
                >
                  {proposal.status}
                </span>

                <OverlayButton
                  disabled={proposal.status === 'accepted'}
                  className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                  dialog={<EditRelationProposalForm proposal={proposal} />}
                >
                  Edit
                </OverlayButton>
              </div>
            </div>

            {proposal.relation.description && (
              <div className="mt-2 text-sm text-gray-700">
                {proposal.relation.description}
              </div>
            )}

            <RelatedSkills
              proposal={proposal}
              specializations={specializations}
            />
          </div>
        );
      })}
    </div>
  );
};

function getStatusColor(status: RelationProposalStatus): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'accepted':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'archived':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
