import { actions } from 'astro:actions';
import { useEffect, useState, type FC } from 'react';

import type { RelationProposal } from '@app/entities/rel-proposal.entity';
import { RelationType } from '@app/entities/shared';
import type { SkillProposal } from '@app/entities/skill-proposal.entity';
import type { Skill } from '@app/entities/skill.entity';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { cn } from '@app/utils/cn';
import { copyToClipboard } from '@app/utils/utils';
import { DomainError } from '@awe/core';
import { DeleteSkillProposal } from '../skills-ingestion/delete.button';
import { EditSkillProposalForm } from '../skills-ingestion/proposal.form';
import { SkillForm } from '../skills/skill.form';
import { Button } from '../ui/button';
import { OverlayButton } from '../ui/overlay-button';

type Props = {
  proposal: RelationProposal;
  specializations: SoftwareSpecialization[];
};

type SkillStatus = {
  existingSkills: Skill[];
  existingProposals: SkillProposal[];
  newSkillNames: string[];
};

export const RelatedSkills: FC<Props> = ({ proposal, specializations }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [skillStatus, setSkillStatus] = useState<SkillStatus | null>(null);

  useEffect(() => {
    const fetchSkillStatus = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const skillNames = extractSkillNames(proposal);

        if (skillNames.length === 0) {
          setSkillStatus({
            existingSkills: [],
            existingProposals: [],
            newSkillNames: [],
          });
          return;
        }

        const result = await actions.checkSkillStatus.checkSkillStatus.orThrow({
          skill_names: skillNames,
        });
        setSkillStatus(result);
      } catch (err) {
        console.error('Error fetching skill status:', err);
        setError(
          err instanceof DomainError
            ? err.message
            : 'Failed to fetch skill status. Please try again.'
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchSkillStatus();
  }, [proposal]);

  if (isLoading) {
    return <div className="text-gray-500">Loading skill status...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (!skillStatus) {
    return null;
  }

  const { existingSkills, existingProposals, newSkillNames } = skillStatus;

  return (
    <div className="flex flex-col gap-4">
      {existingSkills.length > 0 && (
        <div className="border rounded p-3">
          <h3 className="font-medium mb-2">Existing Skills</h3>
          <ul className="list-disc pl-5 flex flex-col gap-2">
            {existingSkills.map((skill) => (
              <li
                key={skill.id}
                className="text-green-600 flex gap-2 justify-between items-center"
              >
                <span>{skill.name}</span>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(skill.id)}
                    className="text-xs"
                  >
                    Copy ID
                  </Button>
                  <OverlayButton
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                    dialog={
                      <SkillForm
                        skill={skill}
                        specializations={specializations}
                      />
                    }
                  >
                    Edit
                  </OverlayButton>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {existingProposals.length > 0 && (
        <div className="border rounded p-3">
          <h3 className="font-medium mb-2">Pending Proposals</h3>
          <ul className="list-disc flex flex-col gap-2">
            {existingProposals.map((proposal) => (
              <li key={proposal.id} className="flex gap-2 justify-between">
                <h4
                  className={cn('text-yellow-600', {
                    'opacity-50':
                      proposal.status === 'archived' ||
                      proposal.status === 'rejected',
                  })}
                >
                  {proposal.skill.name} ({proposal.status})
                </h4>

                <div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(proposal.id)}
                    className="text-xs"
                  >
                    Copy ID
                  </Button>

                  <DeleteSkillProposal proposal={proposal} />

                  <OverlayButton
                    disabled={proposal.status === 'accepted'}
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                    dialog={
                      <EditSkillProposalForm
                        proposal={proposal}
                        specializations={specializations}
                      />
                    }
                  >
                    Edit
                  </OverlayButton>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {newSkillNames.length > 0 && (
        <div className="border rounded p-3">
          <h3 className="font-medium mb-2">New Skills</h3>
          <ul className="list-disc pl-5">
            {newSkillNames.map((name) => (
              <li key={name} className="text-blue-600">
                {name}
              </li>
            ))}
          </ul>
        </div>
      )}

      {existingSkills.length === 0 &&
        existingProposals.length === 0 &&
        newSkillNames.length === 0 && (
          <div className="text-gray-500 italic">No related skills found</div>
        )}
    </div>
  );
};

const extractSkillNames = (proposal: RelationProposal): string[] => {
  switch (proposal.relation.type) {
    case RelationType.alternative:
      return proposal.relation.skill_names;
    case RelationType.version:
      return proposal.relation.skill_names;
    case RelationType.hierarchy:
      return [
        proposal.relation.source_skill_name,
        ...proposal.relation.target_skill_names,
      ];
    case RelationType.ecosystem:
    case RelationType.topical:
      return proposal.relation.skill_names;
    default:
      return [];
  }
};
