---
import { getFirstPrompt } from '@app/utils/utils';
import { actions } from 'astro:actions';
import { EditableTitle } from '../shared/editable-title';

const chatSessions = await Astro.callAction(
  actions.chatSessions.listWithProposals.orThrow,
  undefined
);

const currentSessionId = Astro.url.searchParams.get('session_id');
---

<div class="flex flex-col gap-2" role="list" aria-label="Chat sessions with relation proposals">
  <a
    href="/relation-ingestion"
    class="mb-4 p-2 border rounded hover:bg-gray-100 w-full text-left flex items-center"
    role="listitem"
  >
    <span class="text-blue-500 mr-2">+</span> New Chat
  </a>

  {
    chatSessions.length === 0 ? (
      <div class="text-gray-500 italic">No chat sessions with relation proposals yet</div>
    ) : (
      chatSessions.map((session) => (
        <a
          href={`/relation-ingestion?session_id=${session.id}`}
          class="relative flex border rounded cursor-pointer overflow-hidden"
          role="listitem"
          aria-current={currentSessionId === session.id ? 'page' : undefined}
        >
          {/* Vertical indicator line - visible only for selected chat */}
          <div
            class={`w-1 ${
              currentSessionId === session.id ? 'bg-blue-500' : 'bg-transparent'
            }`}
            aria-hidden="true"
          />

          {/* Chat content - same padding for all chats to maintain alignment */}
          <div
            class={`p-2 flex-1 ${
              currentSessionId === session.id
                ? 'bg-blue-50 border-blue-300'
                : 'hover:bg-gray-100'
            }`}
          >
            <div class="flex justify-between items-center">
              <EditableTitle
                client:visible
                sessionId={session.id}
                initialTitle={session.display_title ?? getFirstPrompt(session)}
              />
              <div class="flex items-center gap-2">
                <div class="text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-600">
                  {session.history.length}{' '}
                  {session.history.length === 1 ? 'message' : 'messages'}
                </div>
                {session.proposals && (
                  <div class="text-xs px-2 py-0.5 bg-indigo-100 rounded-full text-indigo-600">
                    {session.proposals.length}{' '}
                    {session.proposals.length === 1 ? 'relation' : 'relations'}
                  </div>
                )}
              </div>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              {new Date(session.created_at).toLocaleDateString()}
            </div>
          </div>
        </a>
      ))
    )
  }
</div>
