import type { <PERSON> } from 'react';

import { OverlayButton } from '@app/components/ui/overlay-button';
import type { SkillRelation } from '@app/entities/skill-relation.entity';
import { SkillRelationForm } from './relation.form';

export const EditRelationButton: FC<{ relation: SkillRelation }> = ({
  relation,
}) => {
  return (
    <OverlayButton
      dialog={<SkillRelationForm relation={relation} />}
      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-200 flex items-center gap-2"
    >
      Edit
    </OverlayButton>
  );
};
