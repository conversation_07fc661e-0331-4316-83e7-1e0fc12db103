import { actions } from 'astro:actions';
import { useState } from 'react';

import { Dialog } from '@app/components/ui/dialog';
import type { SkillRelation } from '@app/entities/skill-relation.entity';
import { OverlayButton } from '../ui/overlay-button';

export function DeleteSkillRelationButton({
  relation,
}: {
  relation: SkillRelation;
}) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await actions.relations.delete.orThrow({ id: relation.id });
      window.location.reload();
    } catch (error) {
      console.error('Failed to delete skill relation:', error);
      setIsDeleting(false);
    }
  };

  return (
    <OverlayButton
      dialog={
        <Dialog>
          <Dialog.Content>
            <Dialog.Header>
              <h2 className="text-lg font-semibold">Delete Skill Relation</h2>
            </Dialog.Header>

            <p className="text-sm text-gray-500">
              Are you sure you want to delete this skill relation? This action
              cannot be undone.
            </p>
          </Dialog.Content>

          <Dialog.Footer>
            <Dialog.Form>
              <div className="space-y-4">
                <p>
                  Are you sure you want to delete the relation? This action
                  cannot be undone.
                </p>
                <div className="flex justify-end gap-2">
                  <Dialog.CloseButton variant="outline" isLoading={isDeleting}>
                    Cancel
                  </Dialog.CloseButton>
                  <Dialog.SubmitButton
                    variant="destructive"
                    action={async () => {
                      await handleDelete();
                    }}
                    isLoading={isDeleting}
                  >
                    Delete
                  </Dialog.SubmitButton>
                </div>
              </div>
            </Dialog.Form>
          </Dialog.Footer>
        </Dialog>
      }
      variant="destructive"
      size="sm"
      isLoading={isDeleting}
    >
      Delete
    </OverlayButton>
  );
}
