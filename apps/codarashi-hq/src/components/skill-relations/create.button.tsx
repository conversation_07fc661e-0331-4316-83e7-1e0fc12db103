import type { <PERSON> } from 'react';

import { AddIcon } from '@app/components/ui/icons';
import { OverlayButton } from '@app/components/ui/overlay-button';
import { SkillRelationForm } from './relation.form';

export const CreateRelationButton: FC = () => {
  return (
    <OverlayButton
      dialog={<SkillRelationForm />}
      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-200 flex items-center gap-2"
    >
      <AddIcon className="h-5 w-5" />
      Create Relation
    </OverlayButton>
  );
};
