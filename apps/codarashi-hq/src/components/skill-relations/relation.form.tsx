import { actions } from 'astro:actions';
import { useState, type FC } from 'react';

import { RelationType } from '@app/entities/shared';
import type { SkillRelation } from '@app/entities/skill-relation.entity';
import { formDataToJson } from '@app/utils/form-data';
import { SkillsPicker } from '../shared/skills-picker';

import { Dialog } from '../ui/dialog';

export const SkillRelationForm: FC<{ relation?: SkillRelation }> = ({
  relation,
  ...rest
}) => {
  const [relationType, setRelationType] = useState<RelationType>(
    relation?.type || RelationType.alternative
  );

  const submit = async (formData: FormData) => {
    const payload = formDataToJson(formData);

    if (relation) {
      await actions.relations.update.orThrow(payload).then(() => {
        window.location.reload();
      });
    } else {
      await actions.relations.create.orThrow(payload).then(() => {
        window.location.reload();
      });
    }
  };

  return (
    <Dialog isModal={true} closeOnClickOutside={true} {...rest}>
      <Dialog.Header>Edit Relation</Dialog.Header>
      <Dialog.Content>
        <Dialog.Form>
          {/* Common fields for all relation types */}
          {relation && <input type="hidden" name="type" value={relationType} />}

          {relation && <input type="hidden" name="id" value={relation?.id} />}

          {!relation && (
            <select
              id="type"
              required
              className="aw-form-control"
              name="type"
              value={relationType}
              onChange={(e) => setRelationType(e.target.value as RelationType)}
            >
              {Object.values(RelationType).map((type) => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          )}

          <div className="aw-form-field">
            <label
              htmlFor="relation.name"
              className="block text-sm font-medium"
            >
              Name
            </label>
            <input
              type="text"
              id="relation.name"
              name="name"
              className="aw-form-control"
              defaultValue={relation?.name}
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="relation.description"
              className="block text-sm font-medium"
            >
              Description
            </label>
            <textarea
              id="relation.description"
              className="aw-form-control"
              rows={4}
              name="description"
              defaultValue={relation?.description || ''}
            />
          </div>

          {/* Relation type specific fields */}
          {relationType === RelationType.alternative && (
            <div className="mb-4">
              <label
                htmlFor="relation.strength"
                className="block text-sm font-medium"
              >
                Strength (1-5)
              </label>
              <input
                type="number"
                id="relation.strength"
                name="strength"
                className="aw-form-control"
                min={1}
                max={5}
                defaultValue={
                  relation && 'strength' in relation ? relation.strength : 1
                }
              />
              <div className="mt-4 aw-form-field">
                <SkillsPicker
                  initialValues={
                    relation && 'skill_ids' in relation
                      ? relation.skill_ids
                      : undefined
                  }
                  name="skill_ids"
                  label="Skills"
                />
              </div>
            </div>
          )}

          {relationType === RelationType.version && (
            <div className="mb-4 aw-form-field">
              <label
                htmlFor="relation.skill_names"
                className="block text-sm font-medium"
              >
                Version skills
              </label>
              <SkillsPicker
                initialValues={
                  relation && 'skill_ids' in relation
                    ? relation.skill_ids
                    : undefined
                }
                name="skill_ids"
                label="Skills"
              />
            </div>
          )}

          {relationType === RelationType.hierarchy && (
            <>
              <div className="mb-4 aw-form-field">
                <label
                  htmlFor="relation.source_skill_name"
                  className="block text-sm font-medium"
                >
                  Source skill
                </label>
                <SkillsPicker
                  initialValues={
                    relation && 'source_skill_id' in relation
                      ? [relation.source_skill_id]
                      : undefined
                  }
                  name="source_skill_id"
                  label="Source skill"
                  maxItems={1}
                />
              </div>

              <div className="mt-4 aw-form-field">
                <label
                  htmlFor="relation.target_skill_names"
                  className="block text-sm font-medium"
                >
                  Target skills
                </label>
                <SkillsPicker
                  initialValues={
                    relation && 'target_skill_ids' in relation
                      ? relation.target_skill_ids
                      : undefined
                  }
                  name="target_skill_ids"
                  label="Target skills"
                />
              </div>

              <div className="mt-4 aw-form-field">
                <label
                  htmlFor="relation.strength"
                  className="block text-sm font-medium"
                >
                  Strength (1-5)
                </label>
                <input
                  type="number"
                  id="relation.strength"
                  name="strength"
                  className="aw-form-control"
                  min={1}
                  max={5}
                  defaultValue={
                    relation && 'strength' in relation ? relation.strength : 1
                  }
                />
              </div>
            </>
          )}

          {(relationType === RelationType.ecosystem ||
            relationType === RelationType.topical) && (
            <div className="mb-4">
              <div className="mt-4 aw-form-field">
                <label
                  htmlFor="relation.skill_names"
                  className="block text-sm font-medium"
                >
                  Skills in bundle
                </label>
                <SkillsPicker
                  initialValues={
                    relation && 'skill_ids' in relation
                      ? relation.skill_ids
                      : undefined
                  }
                  name="skill_ids"
                  label="Skills in bundle"
                />
              </div>
            </div>
          )}
        </Dialog.Form>
      </Dialog.Content>

      <Dialog.Footer>
        <div className="flex justify-end gap-2 mt-6">
          <Dialog.CloseButton variant="outline" size="sm">
            Cancel
          </Dialog.CloseButton>
          <Dialog.SubmitButton variant="default" action={submit} size="sm">
            Save Changes
          </Dialog.SubmitButton>
        </div>
      </Dialog.Footer>
    </Dialog>
  );
};
