import type { SkillRelation } from '@app/entities/skill-relation.entity';
import { getRelationSummary, getRelationTypeLabel } from '../shared/relations';
import { DeleteSkillRelationButton } from './delete.button';
import { EditRelationButton } from './edit.button';

export function SkillRelationList({
  relations,
}: {
  relations: SkillRelation[];
}) {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {relations.map((relation) => (
          <div
            key={relation.id}
            className="border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-semibold">
                {relation.name} / {getRelationTypeLabel(relation.type)}
              </h2>
            </div>

            {relation.description && (
              <p className="text-gray-600 mb-4 line-clamp-3">
                {relation.description}
              </p>
            )}

            <div className="text-gray-700 mb-4">
              {getRelationSummary(relation)}
            </div>

            <div className="flex justify-end gap-2 mt-4">
              <DeleteSkillRelationButton relation={relation} />

              <EditRelationButton relation={relation} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
