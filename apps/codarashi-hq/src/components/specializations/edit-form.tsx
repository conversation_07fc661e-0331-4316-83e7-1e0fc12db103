import { actions } from 'astro:actions';

import { queryClient } from '@app/components/state/query';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';
import { useStore } from '@nanostores/react';
import { useMutation } from '@tanstack/react-query';

interface Props {
  specialization: SoftwareSpecialization;
  specializations: SoftwareSpecialization[];
}

export default function EditForm({ specialization, specializations }: Props) {
  const client = useStore(queryClient);

  const mutation = useMutation(
    {
      mutationFn: actions.swSpecializations.update,
    },
    client
  );

  return (
    <div className="aw-container">
      <div className="flex gap-8 items-center mb-16">
        <a href="/specializations" className="aw-link">
          Back
        </a>

        <h1 className="text-2xl">Edit '{specialization.name}'</h1>
      </div>

      <form
        className="aw-form"
        onSubmit={async (event) => {
          event.preventDefault();
          const formData = new FormData(event.currentTarget);

          mutation.mutate(formData);
        }}
      >
        <input type="hidden" name="id" value={specialization.id} />

        <div className="aw-form-field">
          <label htmlFor="parentId">Parent specialization</label>

          <select
            name="parent_id"
            id="parentId"
            className="aw-form-control"
            defaultValue={specialization.parent_id ?? undefined}
          >
            <option value="">No parent</option>

            {specializations.map((specialization) => (
              <option key={specialization.id} value={specialization.id}>
                {specialization.name}
              </option>
            ))}
          </select>
        </div>

        <div className="aw-form-field">
          <label htmlFor="name">Name</label>
          <textarea
            id="name"
            className="aw-form-control"
            name="name"
            defaultValue={specialization.name}
          />
        </div>

        <div className="aw-form-field">
          <label htmlFor="specialization-description">Description</label>
          <textarea
            name="description"
            id="specialization-description"
            className="aw-form-control"
            defaultValue={specialization.description ?? ''}
          />
        </div>

        {mutation.isError && (
          <div className="aw-banner error">
            {JSON.stringify(mutation.error)}
          </div>
        )}

        {mutation.isSuccess && (
          <div className="aw-banner success">Specialization updated!</div>
        )}

        <div className="flex gap-4 mt-4">
          <button className="aw-button">Update</button>
          <a
            href="/specializations"
            className="aw-button bg-gray-500 hover:bg-gray-700"
          >
            Cancel
          </a>
        </div>
      </form>
    </div>
  );
}
