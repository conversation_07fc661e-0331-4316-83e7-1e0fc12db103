import { useStore } from '@nanostores/react';
import { useMutation } from '@tanstack/react-query';
import { actions } from 'astro:actions';
import { navigate } from 'astro:transitions/client';
import type { FC } from 'react';

import { queryClient } from '@app/components/state/query';
import type { SoftwareSpecialization } from '@app/entities/sw-spec.entity';

export const CreateForm: FC<{
  specializations: SoftwareSpecialization[];
}> = ({ specializations }) => {
  const client = useStore(queryClient);

  const mutation = useMutation(
    {
      mutationFn: actions.swSpecializations.create,
      onSuccess: () => navigate('/specializations'),
    },
    client
  );

  return (
    <div className="aw-container">
      <form
        className="aw-form"
        onSubmit={(event) => {
          event.preventDefault();
          const formData = new FormData(event.currentTarget);

          mutation.mutate(formData);
        }}
      >
        <div className="aw-form-field">
          <label htmlFor="parentId">Parent specialization</label>

          <select name="parent_id" id="parentId" className="aw-form-control">
            <option value="">No parent</option>

            {specializations.map((specialization) => (
              <option key={specialization.id} value={specialization.id}>
                {specialization.name}
              </option>
            ))}
          </select>
        </div>

        <div className="aw-form-field">
          <label htmlFor="specialization-name">Name</label>
          <textarea
            name="name"
            id="specialization-name"
            className="aw-form-control"
          />
        </div>

        <div className="aw-form-field">
          <label htmlFor="specialization-description">Description</label>
          <textarea
            name="description"
            id="specialization-description"
            className="aw-form-control"
          />
        </div>

        <button className="aw-button" disabled={mutation.isPending}>
          {mutation.isPending ? 'Loading...' : 'Create'}
        </button>

        {mutation.isError && (
          <button className="aw-button" disabled={mutation.isPending}>
            {mutation.isPending ? 'Loading...' : 'Create'}
          </button>
        )}

        {mutation.isError && (
          <div className="aw-banner error">
            {JSON.stringify(mutation.error)}
          </div>
        )}

        {mutation.isSuccess && (
          <div className="aw-banner success">Specialization created!</div>
        )}
      </form>
    </div>
  );
};
