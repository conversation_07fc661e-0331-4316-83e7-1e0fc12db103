import { useStore } from '@nanostores/react';
import { useQueries } from '@tanstack/react-query';
import { actions } from 'astro:actions';
import { type FC } from 'react';

import { EditSkillProposalForm } from '@app/components/skills-ingestion/proposal.form';
import { queryClient } from '@app/components/state/query';
import { Button } from '@app/components/ui/button';
import { OverlayButton } from '@app/components/ui/overlay-button';
import type { RelationProposalStatus } from '@app/entities/rel-proposal.entity';
import type { SkillProposalStatus } from '@app/entities/skill-proposal.entity';
import { copyToClipboard } from '@app/utils/utils';
import { DomainError } from '@awe/core';
import { DeleteRelationButton } from '../relation-ingestion/delete-relation.button';
import { EditRelationProposalForm } from '../relation-ingestion/proposal.form';
import { RelatedSkills } from '../relation-ingestion/related.skills';
import { getRelationSummary } from '../shared/relations';
import { DeleteSkillProposal } from '../skills-ingestion/delete.button';

type Props = {
  sessionId: string;
};

export const Artifacts: FC<Props> = ({ sessionId }) => {
  const client = useStore(queryClient);

  const [relationProposalsQuery, skillProposalsQuery, specializationsQuery] =
    useQueries(
      {
        queries: [
          {
            queryKey: ['relationProposals', sessionId],
            queryFn: () =>
              actions.relProposals.listBySessionId.orThrow({ sessionId }),
            enabled: !!sessionId,
          },
          {
            queryKey: ['skillProposals', sessionId],
            queryFn: () =>
              actions.skillProposals.listBySessionId.orThrow({ sessionId }),
            enabled: !!sessionId,
          },
          {
            queryKey: ['specializations'],
            queryFn: () => actions.swSpecializations.list.orThrow(undefined),
          },
        ],
      },
      client
    );

  const isLoading =
    relationProposalsQuery.isFetching ||
    skillProposalsQuery.isFetching ||
    specializationsQuery.isFetching;
  const isError =
    relationProposalsQuery.isError ||
    skillProposalsQuery.isError ||
    specializationsQuery.isError;
  const error =
    relationProposalsQuery.error ||
    skillProposalsQuery.error ||
    specializationsQuery.error;

  if (isLoading) {
    return <div className="text-gray-500">Loading artifacts...</div>;
  }

  if (isError) {
    return (
      <div className="text-red-500">
        {error instanceof DomainError
          ? error.message
          : 'Failed to fetch artifacts. Please try again.'}
      </div>
    );
  }

  const relationProposals = relationProposalsQuery.data || [];
  const skillProposals = skillProposalsQuery.data || [];
  const specializations = specializationsQuery.data || [];

  const hasArtifacts =
    relationProposals.length > 0 || skillProposals.length > 0;

  if (!hasArtifacts) {
    return (
      <div className="text-gray-500 italic">
        No artifacts generated for this chat session
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {relationProposals.length > 0 && (
        <div className="flex flex-col gap-2">
          <h2 className="text-lg font-semibold">Relation Proposals</h2>
          <div className="flex flex-col gap-4">
            {relationProposals.map((proposal) => (
              <div key={proposal.id} className="border rounded p-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">
                    {getRelationSummary(proposal.relation)}
                  </h3>

                  <div className="flex gap-2 items-center">
                    <span
                      className={`text-xs px-2 py-1 rounded ${getRelationStatusColor(
                        proposal.status
                      )}`}
                    >
                      {proposal.status}
                    </span>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(proposal.id)}
                      className="text-xs"
                    >
                      Copy ID
                    </Button>

                    <DeleteRelationButton proposal={proposal} />

                    <OverlayButton
                      disabled={proposal.status === 'accepted'}
                      className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                      dialog={<EditRelationProposalForm proposal={proposal} />}
                    >
                      Edit
                    </OverlayButton>
                  </div>
                </div>

                <RelatedSkills
                  proposal={proposal}
                  specializations={specializations}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {skillProposals.length > 0 && (
        <div className="flex flex-col gap-2">
          <h2 className="text-lg font-semibold">Skill Proposals</h2>
          <div className="flex flex-col gap-4">
            {skillProposals.map((proposal) => (
              <div key={proposal.id} className="border rounded p-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">{proposal.skill.name}</h3>

                  <div className="flex gap-2 items-center">
                    <span
                      className={`text-xs px-2 py-1 rounded ${getSkillStatusColor(
                        proposal.status
                      )}`}
                    >
                      {proposal.status}
                    </span>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(proposal.id)}
                      className="text-xs"
                    >
                      Copy ID
                    </Button>

                    <DeleteSkillProposal proposal={proposal} />

                    <OverlayButton
                      disabled={proposal.status === 'accepted'}
                      className="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200"
                      dialog={
                        <EditSkillProposalForm
                          proposal={proposal}
                          specializations={specializations}
                        />
                      }
                    >
                      Edit
                    </OverlayButton>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

function getRelationStatusColor(status: RelationProposalStatus): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'accepted':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'archived':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
function getSkillStatusColor(status: SkillProposalStatus): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'accepted':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'archived':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
