import { actions } from 'astro:actions';
import { useState } from 'react';

import { Button } from '@app/components/ui/button';
import { cn } from '@app/utils/cn';

type Props = {
  sessionId?: string;
};

export const PromptForm = ({ sessionId }: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    const form = e.currentTarget;

    try {
      const formData = new FormData(form);

      const result = await actions.ingestion.callAgent.orThrow(formData);

      if (!sessionId) {
        history.pushState(null, '', `?session_id=${result.id}`);
      }

      window.location.reload();
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(
        'An error occurred while submitting the form. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form className={cn('aw-form')} onSubmit={handleSubmit}>
      <input type="hidden" name="session_id" value={sessionId || ''} />

      <div className="aw-form-field mt-3">
        <label htmlFor="prompt-message">Message</label>
        <textarea
          name="message"
          id="prompt-message"
          className="aw-form-control"
          placeholder="Say your wishes"
          autoFocus
          rows={4}
        />
      </div>

      <div>
        <Button type="submit" isLoading={isLoading}>
          Send
        </Button>
      </div>

      {error && <div className="aw-banner error">{error}</div>}
    </form>
  );
};
