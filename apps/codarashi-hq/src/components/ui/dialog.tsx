import type { ComponentProps, ReactNode } from 'react';
import React, {
  createContext,
  useActionState,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { createPortal } from 'react-dom';

import { UUID } from '@awe/core';

import { cn } from '../../utils/cn';
import type { ButtonProps } from './button';
import { Button } from './button';
import './dialog.css';

export type DialogProps = React.DialogHTMLAttributes<HTMLDialogElement> & {
  isOpen?: boolean;
  isModal?: boolean;
  children: ReactNode;
  closeOnClickOutside?: boolean;
  onClose?: () => void;
  className?: string;
};

/**
 * A reusable Dialog component that uses the native HTML dialog element.
 * Supports both modal and non-modal modes, with styled backdrop and options for
 * header, scrollable content, and sticky footer.
 */
// In the Dialog component, replace the useEffect for event listeners with direct props
export const Dialog = ({
  isOpen,
  isModal = true,
  children,
  closeOnClickOutside = true,
  onClose,
  className = '',
  ...rest
}: DialogProps) => {
  const dialogRef = useRef<HTMLDialogElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [formId] = useState(UUID.generate());

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  useEffect(() => {
    const dialogElement = dialogRef.current;
    if (!dialogElement) return;

    if (isOpen) {
      if (isModal) {
        if (!dialogElement.open) {
          dialogElement.showModal();
        }
      } else {
        if (!dialogElement.open) {
          dialogElement.show();
        }
      }
    } else if (dialogElement.open) {
      dialogElement.close();
    }
  }, [isOpen, isModal]);

  if (!isOpen || !isMounted) return null;

  const dialogContent = (
    <dialog
      {...rest}
      ref={(node) => {
        dialogRef.current = node;

        if (node !== null) {
          // clickAwayRef.current = node;
        }
      }}
      className={cn(
        {
          // Layout & positioning
          'relative m-auto p-0 max-w-[90vw] min-w-[800px] max-h-[90vh]': true,
          'flex flex-col overflow-hidden': true,

          // Appearance
          'border-none rounded-lg shadow-lg': true,
          'bg-white text-gray-800': true,
        },
        className
      )}
      onClick={(e) => e.stopPropagation()}
      onClose={onClose}
      onCancel={(e) => {
        e.preventDefault();
        onClose?.();
      }}
    >
      <DialogContext.Provider value={{ onClose, formId }}>
        {children}
      </DialogContext.Provider>
    </dialog>
  );

  return createPortal(dialogContent, document.body);
};

type DialogHeaderProps = {
  children: ReactNode;
  className?: string;
};

const Header = ({ children, className = '' }: DialogHeaderProps) => {
  const { onClose } = useDialogContext();

  return (
    <header
      className={cn(
        {
          // Layout & positioning
          'flex items-center justify-between p-4': true,

          // Appearance
          'border-b border-gray-200 bg-gray-50': true,
        },
        className
      )}
    >
      <h2 className="m-0 text-xl font-semibold">{children}</h2>
      <button
        type="button"
        className={cn({
          // Base styles
          'bg-transparent border-none p-0 w-7 h-7': true,

          // Layout
          'flex items-center justify-center': true,

          // Appearance
          'text-2xl rounded-full text-gray-500': true,

          // Interactive states
          'cursor-pointer hover:bg-gray-100 hover:text-gray-700 transition-colors':
            true,
        })}
        onClick={onClose}
        aria-label="Close"
      >
        ×
      </button>
    </header>
  );
};

type DialogContentProps = {
  children: ReactNode;
  className?: string;
};

const Content = ({ children, className = '' }: DialogContentProps) => {
  return (
    <div
      className={cn(
        {
          // Layout & positioning
          'flex-1 p-5': true,

          // Scrolling
          'overflow-y-auto max-h-[calc(90vh-130px)]': true,
        },
        className
      )}
    >
      {children}
    </div>
  );
};

type DialogFooterProps = {
  children: ReactNode;
  className?: string;
};

const Footer = ({ children, className = '' }: DialogFooterProps) => {
  return (
    <footer
      className={cn(
        {
          // Layout & positioning
          'p-4 flex justify-end gap-3 sticky bottom-0': true,

          // Appearance
          'border-t border-gray-200 bg-gray-50': true,
        },
        className
      )}
    >
      {children}
    </footer>
  );
};

const Form = ({
  children,
  className = '',
  onSubmit,
  ...props
}: ComponentProps<'form'>) => {
  const { formId } = useDialogContext();

  const handleSubmit = onSubmit
    ? (event: React.FormEvent<HTMLFormElement>) => {
        onSubmit(event);
      }
    : undefined;

  return (
    <form
      className={cn('flex-1 flex flex-col gap-4', className)}
      onSubmit={handleSubmit}
      id={formId}
      {...props}
    >
      {children}
    </form>
  );
};

type SubmitButtonProps = ButtonProps & {
  action: (formData: FormData) => Promise<unknown>;
};

const SubmitButton = React.forwardRef<HTMLButtonElement, SubmitButtonProps>(
  ({ action, children, ...props }, ref) => {
    const context = useDialogContext();

    const [_, formAction, isPending] = useActionState(
      async (_: any, formData: FormData) => {
        await action(formData);

        context?.onClose?.();
      },
      undefined
    );

    return (
      <Button
        type="submit"
        form={context.formId}
        formAction={formAction}
        ref={ref}
        isLoading={isPending}
        {...props}
      >
        {children}
      </Button>
    );
  }
);
SubmitButton.displayName = 'SubmitButton';

const CloseButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, ...props }, ref) => {
    const context = useDialogContext();

    return (
      <Button
        type="submit"
        ref={ref}
        form={context?.formId}
        {...props}
        formMethod="dialog"
        formNoValidate
      >
        {children}
      </Button>
    );
  }
);
CloseButton.displayName = 'CloseButton';

type DialogContextType = {
  onClose?: () => void;
  formId: string;
};

const DialogContext = createContext<DialogContextType | undefined>(undefined);

const useDialogContext = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error(
      'Dialog compound components must be used within a Dialog component'
    );
  }
  return context;
};

Dialog.Header = Header;
Dialog.Content = Content;
Dialog.Footer = Footer;
Dialog.Form = Form;
Dialog.SubmitButton = SubmitButton;
Dialog.CloseButton = CloseButton;
