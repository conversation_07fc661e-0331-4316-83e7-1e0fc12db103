import { useEffect, useRef, type ReactNode } from 'react';

type ScrollIntoViewProps = {
  children?: ReactNode;
  behavior?: ScrollIntoViewOptions['behavior'];
  block?: ScrollIntoViewOptions['block'];
  inline?: ScrollIntoViewOptions['inline'];
  enabled?: boolean;
};

export const ScrollIntoView = ({
  children,
  behavior = 'smooth',
  block = 'end',
  inline = 'nearest',
  enabled = true,
}: ScrollIntoViewProps) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (enabled && ref.current) {
      ref.current.scrollIntoView({ behavior, block, inline });
    }
  }, [behavior, block, inline, enabled]);

  return <div ref={ref}>{children}</div>;
};
