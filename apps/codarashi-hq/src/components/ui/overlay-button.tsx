import React, { cloneElement, useId, useState, type ReactElement } from 'react';

import { Button } from './button';
import type { Dialog } from './dialog';

type OverlayButtonProps = {
  dialog:
    | ReactElement<React.ComponentProps<typeof Dialog>>
    | ((params: {
        isOpen: boolean;
        close: () => void;
        id: string;
      }) => ReactElement<React.ComponentProps<typeof Dialog>>);
} & React.ComponentProps<typeof Button>;

export const OverlayButton: React.FC<OverlayButtonProps> = ({
  dialog,
  ...buttonProps
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const uniqueId = useId();

  return (
    <>
      <Button
        {...buttonProps}
        onClick={(event) => {
          buttonProps.onClick?.(event);
          setIsOpen(true);
        }}
        aria-expanded={isOpen}
        aria-haspopup="dialog"
        aria-controls={uniqueId}
      />

      {typeof dialog === 'function'
        ? dialog({
            isOpen,
            close: () => setIsOpen(false),
            id: uniqueId,
          })
        : cloneElement(dialog, {
            id: uniqueId,
            onClose: () => {
              dialog.props.onClose?.();
              setIsOpen(false);
            },
            onCancel: () => {
              dialog.props.onClose?.();
              setIsOpen(false);
            },
            isOpen,
          })}
    </>
  );
};
