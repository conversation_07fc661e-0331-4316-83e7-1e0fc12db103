import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

import { cn } from '@app/utils/cn';
import Slot from './slot';

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & {
    isLoading?: boolean;
    asChild?: boolean;
  };

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      isLoading,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    if (asChild) {
      if (!React.isValidElement<Record<string, unknown>>(children)) {
        throw new Error(
          'Button[asChild] requires a single valid React element as its child.'
        );
      }

      return <Slot>{children}</Slot>;
    }

    return (
      <button
        className={cn(buttonVariants({ variant, size, className }), {
          'cursor-wait': isLoading,
        })}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <div className="relative w-10 h-10 rounded-full border-4 border-t-blue-500 animate-spin mr-1"></div>
        ) : null}
        {children}
      </button>
    );
  }
);
Button.displayName = 'Button';

export { Button };

const buttonVariants = cva(
  cn({
    // Layout & positioning
    'inline-flex items-center justify-center': true,

    // Shape & appearance
    'rounded-md': true,

    // Typography
    'text-sm font-medium': true,

    // Cursor
    'cursor-pointer': true,

    // Transitions & animations
    'transition-colors': true,

    // Focus states
    'outline-offset-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-600':
      true,

    // Disabled state
    'disabled:opacity-50 disabled:pointer-events-none': true,
  }),
  {
    variants: {
      variant: {
        default: 'bg-blue-500 text-white hover:bg-blue-600',
        destructive: 'bg-red-600 text-white hover:bg-red-700',
        outline:
          'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);
