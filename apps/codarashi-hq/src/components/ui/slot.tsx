import { cn } from '@app/utils/cn';
import React from 'react';

type SlotProps = React.HTMLAttributes<HTMLElement> & {
  children: React.ReactElement<Record<string, unknown>>;
};

const Slot: React.FC<SlotProps> = ({ children, ...props }) => {
  if (!React.isValidElement(children)) {
    throw new Error('Slot requires a single valid React element as its child.');
  }

  return React.cloneElement(children, {
    ...props,
    className: cn(
      typeof children.props.className === 'string'
        ? children.props.className
        : '',
      props.className
    ),
  });
};

export default Slot;
