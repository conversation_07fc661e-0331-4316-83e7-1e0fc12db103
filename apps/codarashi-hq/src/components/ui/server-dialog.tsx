import { cn } from '../../utils/cn';
import type { ReactNode } from 'react';

/**
 * A server-only dialog component that works without client-side JavaScript.
 * Uses the native HTML dialog element and form method="dialog" for closing.
 */
const ServerDialog = ({
  id,
  children,
  className = '',
  open = false,
}: {
  id: string;
  children: ReactNode;
  className?: string;
  open?: boolean;
}) => {
  return (
    <dialog
      id={id}
      className={cn(
        'relative m-auto p-0 border-none rounded-lg shadow-lg max-w-[90vw] w-[500px] max-h-[90vh]',
        'flex flex-col overflow-hidden bg-white text-gray-800',
        className
      )}
      open={open}
    >
      {children}
    </dialog>
  );
};

const Title = ({
  children,
  className = '',
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <header
      className={cn(
        'flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50',
        className
      )}
    >
      <h2 className="m-0 text-xl font-semibold">{children}</h2>
      <form method="dialog">
        <button
          type="submit"
          className={cn(
            'bg-transparent border-none text-2xl cursor-pointer p-0 w-7 h-7',
            'flex items-center justify-center rounded-full text-gray-500',
            'hover:bg-gray-100 hover:text-gray-700 transition-colors'
          )}
          aria-label="Close"
        >
          ×
        </button>
      </form>
    </header>
  );
};

const Content = ({
  children,
  className = '',
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        'flex-1 p-5 overflow-y-auto max-h-[calc(90vh-130px)]',
        className
      )}
    >
      {children}
    </div>
  );
};

const Footer = ({
  children,
  className = '',
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <footer
      className={cn(
        'p-4 border-t border-gray-200 bg-gray-50 flex justify-end gap-3 sticky bottom-0',
        className
      )}
    >
      {children}
    </footer>
  );
};

const Form = ({
  children,
  className = '',
  action,
}: {
  children: ReactNode;
  className?: string;
  action?: string;
}) => {
  return (
    <form
      method="dialog"
      className={cn('flex-1 flex flex-col', className)}
      action={action}
    >
      {children}
    </form>
  );
};

ServerDialog.Title = Title;
ServerDialog.Content = Content;
ServerDialog.Footer = Footer;
ServerDialog.Form = Form;

export default ServerDialog;
