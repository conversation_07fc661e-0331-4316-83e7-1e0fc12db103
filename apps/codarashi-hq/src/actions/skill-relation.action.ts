import { ActionError, defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import {
  createDto,
  id as relationId,
  updateDto,
} from '@app/entities/skill-relation.entity';
import { relationRepository } from '@app/repositories/relation.repository';
import { DomainError } from '@awe/core';

export const actions = {
  list: defineAction({
    async handler() {
      return relationRepository.list();
    },
  }),

  create: defineAction({
    input: z.any().pipe(createDto),
    async handler(input, _context) {
      return relationRepository.create(input);
    },
  }),

  update: defineAction({
    input: z.any().pipe(updateDto),
    async handler(input, _context) {
      return relationRepository.update(input);
    },
  }),

  delete: defineAction({
    input: z.object({
      id: relationId,
    }),
    async handler({ id }, _context) {
      try {
        await relationRepository.delete(id);
        return { success: true };
      } catch (error) {
        if (error instanceof DomainError) {
          throw error;
        }
        throw new ActionError({
          code: 'BAD_REQUEST',
        });
      }
    },
  }),
};
