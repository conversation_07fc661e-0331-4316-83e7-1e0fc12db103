import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { client } from '@app/database';
import { aiModel, chatSession } from '@app/entities/shared';
import { chatSessionRepository } from '@app/repositories/chat-session.repository';
import { aiService } from '@app/services/ai.service';
import { must } from '@awe/core';

export const actions = {
  callAgent: defineAction({
    accept: 'form',
    input: z.object({
      session_id: chatSession.shape.id.optional(),
      message: z.string().min(1),
      model: aiModel.default('gemini-2.0-flash'),
    }),
    async handler(payload, _context) {
      const dbSession = client.startSession();
      const chatSession = await chatSessionRepository.getOrCreate(
        payload.model,
        payload.session_id
      );

      const operation = async () => {
        const result = await aiService.callAgent({
          message: payload.message,
          chatSession,
          model: payload.model,
        });
        const historyItemId = must(result.chatSession.history.at(-1)?.id);

        for (const artifact of result.artifacts) {
          if (artifact.type === 'skills-generation') {
            await aiService.persistSkillGeneration({
              data: artifact,
              sessionId: chatSession.id,
              dbSession,
              historyItemId,
            });
          } else if (artifact.type === 'relation-generation') {
            await aiService.persistRelationProposal({
              relation: artifact.relation,
              sessionId: chatSession.id,
              historyItemId,
              dbSession,
            });
          }
        }

        return await chatSessionRepository.replace(
          result.chatSession,
          dbSession
        );
      };

      return await dbSession.withTransaction(operation).finally(async () => {
        await dbSession.endSession();
      });
    },
  }),
};
