import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { client } from '@app/database';
import { chatSession } from '@app/entities/shared';
import { skillProposal, updateDto } from '@app/entities/skill-proposal.entity';
import { skillProposalRepository } from '@app/repositories/skill-proposal.repository';
import { skillsRepository } from '@app/repositories/skill.repository';

export const actions = {
  listBySessionId: defineAction({
    input: z.object({
      sessionId: chatSession.shape.id,
    }),
    async handler({ sessionId }, _context) {
      return skillProposalRepository.listBySessionId(sessionId);
    },
  }),

  update: defineAction({
    input: z.object({
      payload: updateDto,
      id: skillProposal.shape.id,
    }),
    async handler(dto, _context) {
      const existingProposal = await skillProposalRepository.getByIdOrThrow(
        dto.id
      );

      if (
        dto.payload.status === 'accepted' &&
        existingProposal.status === 'pending'
      ) {
        const dbSession = client.startSession();

        return await dbSession.withTransaction(async () => {
          const proposal = await skillProposalRepository.getByIdOrThrow(dto.id);

          const skill = {
            ...proposal.skill,
            ...dto.payload.skill,
          } as const;

          await skillsRepository.create(skill, dbSession);

          const result = await skillProposalRepository.update(
            dto.id,
            dto.payload,
            dbSession
          );

          // check if parsing still works as expected
          await skillProposalRepository.getByIdOrThrow(dto.id);

          return result;
        });
      }

      return await skillProposalRepository.update(dto.id, dto.payload);
    },
  }),
  delete: defineAction({
    input: z.object({
      id: skillProposal.shape.id,
    }),
    async handler({ id }, _context) {
      return await skillProposalRepository.delete(id);
    },
  }),
};
