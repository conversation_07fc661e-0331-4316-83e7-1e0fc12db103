import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { client } from '@app/database';
import { chatSession, relationType, RelationType } from '@app/entities/shared';
import { chatSessionRepository } from '@app/repositories/chat-session.repository';
import { aiService } from '@app/services/ai.service';
import type { RelationAiResponse } from '@app/services/ai/relation.prompt';
import { must } from '@awe/core';

export const actions = {
  generate: defineAction({
    accept: 'form',
    input: z.object({
      session_id: chatSession.shape.id.optional(),
      seed_skills: z.array(z.string()),
      relation_type: relationType,
      message: z.string().optional(), // nothing wrong with being empty
    }),
    async handler(payload, _context) {
      const dbSession = client.startSession();
      const chatSession = await chatSessionRepository.getOrCreate(
        'gemini-2.0-flash',
        payload.session_id
      );

      const operation = async () => {
        const result = await aiService.generateRelation({
          chat_session: chatSession,
          message: payload.message ?? '',
          seed_skills: payload.seed_skills,
          relation_type: payload.relation_type,
        });

        const updatedSession = await chatSessionRepository.addHistoryItem(
          chatSession,
          {
            type: 'message',

            user_prompt: payload.message ?? '',
            model_response: result.comment ?? '',
          },
          dbSession
        );

        return await aiService.persistRelationProposal({
          relation: result['relation'],
          sessionId: updatedSession.id,
          dbSession,
          historyItemId: must(updatedSession.history.at(-1)?.id),
        });
      };

      return await dbSession.withTransaction(operation).finally(async () => {
        await dbSession.endSession();
      });
    },
  }),
};

/**
 * Extracts all skill names from a relation object based on its type
 */
function extractSkillNamesFromRelation(
  relation: RelationAiResponse['relation']
): string[] {
  switch (relation.type) {
    case RelationType.alternative:
    case RelationType.version:
      return relation.skill_names;

    case RelationType.hierarchy:
      return [relation.source_skill_name, ...relation.target_skill_names];

    case RelationType.ecosystem:
    case RelationType.topical:
      return relation.skill_names;

    default:
      return [];
  }
}
