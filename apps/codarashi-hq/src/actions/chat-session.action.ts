import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { chatSession } from '@app/entities/shared';
import { chatSessionRepository } from '@app/repositories/chat-session.repository';

export const actions = {
  list: defineAction({
    async handler() {
      return chatSessionRepository.list();
    },
  }),

  listWithProposals: defineAction({
    async handler() {
      return chatSessionRepository.listWithProposals();
    },
  }),

  get: defineAction({
    input: z.object({
      id: chatSession.shape.id,
    }),
    async handler({ id }, _context) {
      return chatSessionRepository.getByIdOrFail(id);
    },
  }),

  updateTitle: defineAction({
    input: z.object({
      id: chatSession.shape.id,
      display_title: z.string(),
    }),
    async handler({ id, display_title }, _context) {
      return chatSessionRepository.updateTitle(id, { display_title });
    },
  }),
};
