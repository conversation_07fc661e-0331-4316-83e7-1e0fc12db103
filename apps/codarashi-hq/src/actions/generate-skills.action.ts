import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { client } from '@app/database';
import { chatSession, SkillCategory } from '@app/entities/shared';
import { chatSessionRepository } from '@app/repositories/chat-session.repository';
import { aiService } from '@app/services/ai.service';
import { must } from '@awe/core';
import { softwareSpecialization } from '../entities/sw-spec.entity';

export const actions = {
  generate: defineAction({
    accept: 'form',
    input: z.object({
      session_id: chatSession.shape.id.optional(),
      categories: z.array(SkillCategory),
      specializations: z.array(softwareSpecialization.shape.id),
      message: z.string().optional(), // nothing wrong with being empty
    }),
    async handler(payload, _context) {
      const dbSession = client.startSession();
      const chatSession = await chatSessionRepository.getOrCreate(
        'gemini-2.0-flash',
        payload.session_id
      );

      const operation = async () => {
        const result = await aiService.generateSkills({
          chat_session: chatSession,
          message: payload.message ?? '',
          categories: payload.categories,
          specializations: payload.specializations,
        });

        const updatedSession = await chatSessionRepository.addHistoryItem(
          chatSession,
          {
            type: 'message',
            user_prompt: payload.message ?? '',
            model_response: result.comment,
          } as const,
          dbSession
        );

        return await aiService.persistSkillGeneration({
          data: result,
          sessionId: updatedSession.id,
          dbSession,
          historyItemId: must(updatedSession.history.at(-1)?.id),
        });
      };

      return await dbSession.withTransaction(operation).finally(async () => {
        await dbSession.endSession();
      });
    },
  }),
};
