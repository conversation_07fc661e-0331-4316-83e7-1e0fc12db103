import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { skillsRepository } from '@app/repositories/skill.repository';
import { skillProposalRepository } from '@app/repositories/skill-proposal.repository';

export const actions = {
  checkSkillStatus: defineAction({
    input: z.object({
      skill_names: z.array(z.string()),
    }),
    async handler(payload) {
      const skillNames = payload.skill_names;

      // Find existing skills
      const existingSkills = await skillsRepository.findSimilarSkills(
        skillNames
      );

      // Find existing proposals that are not accepted
      const existingProposals =
        await skillProposalRepository.findSimilarProposals(skillNames);

      // Get names of existing skills and proposals
      const existingSkillNames = existingSkills.map((skill) => skill.name);
      const existingProposalNames = existingProposals.map(
        (proposal) => proposal.skill.name
      );

      // Find skill names that are neither existing skills nor proposals
      const newSkillNames = skillNames.filter(
        (name) =>
          !existingSkillNames.some(
            (existingName) => existingName.toLowerCase() === name.toLowerCase()
          ) &&
          !existingProposalNames.some(
            (proposalName) => proposalName.toLowerCase() === name.toLowerCase()
          )
      );

      return {
        existingSkills,
        existingProposals,
        newSkillNames,
      };
    },
  }),
};
