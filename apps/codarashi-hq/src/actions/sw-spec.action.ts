import { defineAction } from 'astro:actions';

import { softwareSpecialization } from '@app/entities/sw-spec.entity';
import { swSpecRepository } from '@app/repositories/sw-spec.repository';

export const actions = {
  list: defineAction({
    async handler() {
      return swSpecRepository.listAll();
    },
  }),

  create: defineAction({
    accept: 'form',
    input: softwareSpecialization.omit({ id: true }),
    async handler(payload, _context) {
      const item = await swSpecRepository.create(payload);

      return { status: 'success', data: item };
    },
  }),

  update: defineAction({
    accept: 'form',
    input: softwareSpecialization,
    async handler(payload, _context) {
      return await swSpecRepository.update(payload);
    },
  }),

  delete: defineAction({
    input: softwareSpecialization.pick({ id: true }),
    async handler({ id }, _context) {
      await swSpecRepository.delete(id);
    },
  }),

  get: defineAction({
    input: softwareSpecialization.pick({ id: true }),
    async handler({ id }, _context) {
      return swSpecRepository.getById(id);
    },
  }),
};
