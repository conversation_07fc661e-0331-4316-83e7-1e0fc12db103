import { ActionError, defineAction } from 'astro:actions';
import { z } from 'astro:schema';

import { createDto, skill, updateDto } from '@app/entities/skill.entity';
import { skillsRepository } from '@app/repositories/skill.repository';
import { DomainError, ErrorMessages } from '@awe/core';

export const actions = {
  list: defineAction({
    async handler() {
      return skillsRepository.list();
    },
  }),

  get: defineAction({
    input: z.any().pipe(
      z.object({
        id: skill.shape.id,
      })
    ),
    async handler({ id }, _context) {
      return skillsRepository.getById(id);
    },
  }),

  create: defineAction({
    accept: 'form',
    async handler(formData, _context) {
      const payload = createDto.safeParse(formData);

      if (payload.success) {
        return skillsRepository.create(payload.data);
      }

      throw new ActionError({
        code: 'BAD_REQUEST',
        message: payload.error.message,
      });
    },
  }),

  update: defineAction({
    accept: 'form',
    async handler(formData, _context) {
      const payload = updateDto.safeParse(formData);

      if (payload.success) {
        return skillsRepository.update(payload.data.id, payload.data);
      }

      throw new ActionError({
        code: 'BAD_REQUEST',
        message: payload.error.message,
      });
    },
  }),

  delete: defineAction({
    input: z.any().pipe(
      z.object({
        id: skill.shape.id,
      })
    ),
    async handler({ id }, _context) {
      try {
        await skillsRepository.delete(id);
        return { success: true };
      } catch (error) {
        if (error instanceof DomainError) {
          throw error;
        }
        throw new DomainError(ErrorMessages.EntityNotFound, { cause: error });
      }
    },
  }),

  search: defineAction({
    input: z.any().pipe(
      z.object({
        query: z.string(),
      })
    ),
    async handler({ query }, _context) {
      return skillsRepository.searchByName(query);
    },
  }),

  listByIds: defineAction({
    input: z.any().pipe(
      z.object({
        ids: z.array(skill.shape.id).nonempty(),
      })
    ),
    async handler({ ids }, _context) {
      return skillsRepository.getLeanSkillsByIds(ids);
    },
  }),
};
