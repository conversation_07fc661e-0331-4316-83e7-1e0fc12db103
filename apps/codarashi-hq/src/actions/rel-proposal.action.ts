import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';
import { match } from 'ts-pattern';

import { client } from '@app/database';
import {
  id as proposalId,
  updateDto,
  type RelationProposal,
} from '@app/entities/rel-proposal.entity';
import { chatSession } from '@app/entities/shared';
import { relationProposalRepository } from '@app/repositories/rel-proposal.repository';
import { relationRepository } from '@app/repositories/relation.repository';
import { skillsRepository } from '@app/repositories/skill.repository';
import { DomainError, ErrorMessages } from '@awe/core';

export const actions = {
  list: defineAction({
    async handler() {
      return relationProposalRepository.list();
    },
  }),

  listBySessionId: defineAction({
    input: z.object({
      sessionId: chatSession.shape.id,
    }),
    async handler({ sessionId }) {
      return relationProposalRepository.listBySessionId(sessionId);
    },
  }),

  get: defineAction({
    input: z.object({
      id: proposalId,
    }),
    async handler({ id }) {
      return relationProposalRepository.getByIdOrThrow(id);
    },
  }),

  update: defineAction({
    input: z.object({
      payload: updateDto,
      id: proposalId,
    }),
    async handler({ payload, id }): Promise<RelationProposal> {
      const existingProposal = await relationProposalRepository.getByIdOrThrow(
        id
      );

      if (
        payload.status === 'accepted' &&
        existingProposal.status === 'pending'
      ) {
        const dbSession = client.startSession();

        const operation = async () => {
          return await match(payload.relation)
            .with({ type: 'alternative' }, async (data) => {
              const skills = await skillsRepository.findSimilarSkills(
                data.skill_names
              );

              const relation = {
                ...data,
                skill_ids: skills.map((s) => s.id),
              } as const;

              await relationRepository.create(relation, dbSession);

              return await relationProposalRepository.update(
                id,
                payload,
                dbSession
              );
            })
            .with({ type: 'version' }, async (data) => {
              const skills = await skillsRepository.findSimilarSkills(
                data.skill_names
              );

              const relation = {
                ...data,
                skill_ids: skills.map((s) => s.id),
              } as const;

              await relationRepository.create(relation, dbSession);

              return await relationProposalRepository.update(
                id,
                payload,
                dbSession
              );
            })
            .with({ type: 'hierarchy' }, async (data) => {
              const sourceSkill = await skillsRepository.findSimilarSkill(
                data.source_skill_name
              );
              const targetSkills = await skillsRepository.findSimilarSkills(
                data.target_skill_names
              );

              if (!sourceSkill || !targetSkills.length) {
                throw new DomainError(ErrorMessages.InvalidAction, {
                  sourceSkill: data.source_skill_name,
                  targetSkills: data.target_skill_names,
                  missing: !sourceSkill ? 'source' : 'target',
                });
              }

              const relation = {
                ...data,
                source_skill_id: sourceSkill.id,
                target_skill_ids: targetSkills.map((s) => s.id),
              } as const;

              await relationRepository.create(relation, dbSession);

              return await relationProposalRepository.update(
                id,
                payload,
                dbSession
              );
            })
            .with({ type: 'ecosystem' }, async (data) => {
              const skills = await skillsRepository.findSimilarSkills(
                data.skill_names
              );

              const relation = {
                ...data,
                skill_ids: skills.map((s) => s.id),
              } as const;

              await relationRepository.create(relation, dbSession);

              return await relationProposalRepository.update(
                id,
                payload,
                dbSession
              );
            })
            .with({ type: 'topical' }, async (data) => {
              const skills = await skillsRepository.findSimilarSkills(
                data.skill_names
              );

              const relation = {
                ...data,
                skill_ids: skills.map((s) => s.id),
              } as const;

              await relationRepository.create(relation, dbSession);

              return await relationProposalRepository.update(
                id,
                payload,
                dbSession
              );
            })
            .exhaustive();
        };

        return await dbSession.withTransaction(operation);
      }

      return relationProposalRepository.update(id, payload);
    },
  }),

  delete: defineAction({
    input: z.object({
      id: proposalId,
    }),
    async handler({ id }) {
      return relationProposalRepository.delete(id);
    },
  }),
};
