import { actions as chatSessionActions } from './chat-session.action';
import { actions as checkSkillStatusActions } from './check-skill-status.action';
import { actions as generateRelationActions } from './generate-relation.action';
import { actions as generateSkillsActions } from './generate-skills.action';
import { actions as ingestionActions } from './ingestion.action';
import { actions as relProposalActions } from './rel-proposal.action';
import { actions as skillProposalActions } from './skill-proposal.action';
import { actions as relationActions } from './skill-relation.action';
import { actions as skillActions } from './skill.action';
import { actions as swSpecActions } from './sw-spec.action';

export const server = {
  swSpecializations: swSpecActions,
  skillPrompts: generateSkillsActions,
  chatSessions: chatSessionActions,
  skillProposals: skillProposalActions,
  skills: skillActions,
  relationPrompts: generateRelationActions,
  relProposals: relProposalActions,
  relations: relationActions,
  checkSkillStatus: checkSkillStatusActions,
  ingestion: ingestionActions,
};
