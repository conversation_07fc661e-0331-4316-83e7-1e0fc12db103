{"private": true, "name": "co<PERSON><PERSON>", "description": "", "license": "", "scripts": {}, "type": "module", "dependencies": {"@remix-run/node": "^2.3.0", "@remix-run/react": "^2.3.0", "@remix-run/serve": "^2.3.0", "isbot": "^3.6.8", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@remix-run/dev": "^2.3.0", "@remix-run/eslint-config": "^2.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.38.0", "typescript": "^5.1.6"}, "engines": {"node": ">=14"}, "sideEffects": false}