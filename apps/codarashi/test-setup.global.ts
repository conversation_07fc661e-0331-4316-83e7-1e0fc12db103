import { migrate } from 'drizzle-orm/libsql/migrator';
import { join } from 'path';

import { createDb } from './app/utils/database';

export async function setup() {
  // const url = 'file:' + join(__dirname, 'db/test-integration.db');
  const url = 'file::memory:?cache=shared';
  process.env.DB_HOST_TURSO = url;

  await runMigrations();
}

const runMigrations = async () => {
  console.log('Running migrations...');

  const db = createDb(process.env.DB_HOST_TURSO ?? '');

  await migrate(db, {
    migrationsFolder: join(__dirname, 'db/migrations'),
  });

  console.log('Migrations completed');
};
