{"name": "co<PERSON><PERSON>", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/codarashi", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/remix:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/codarashi"}}, "serve": {"executor": "@nx/remix:serve", "options": {"command": "npx remix-serve build/index.js", "manual": true, "port": 4200}}, "turso-dev": {"executor": "nx:run-commands", "options": {"cwd": "apps/codarashi", "command": "mkdir -p ./db/development && touch ./db/development/local.db && turso dev --db-file ./db/development/local.db"}}, "start": {"dependsOn": ["build"], "command": "remix-serve build/index.js --inspect", "options": {"cwd": "apps/codarashi"}}, "typecheck": {"command": "tsc --project tsconfig.app.json", "options": {"cwd": "apps/codarashi"}}, "typecheck:watch": {"command": "tsc --project tsconfig.app.json --watch", "options": {"cwd": "apps/codarashi"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/codarashi"}}, "test:watch": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/codarashi", "watch": true}}, "lint": {"executor": "@nx/eslint:lint"}, "db-generate": {"executor": "nx:run-commands", "options": {"command": "npx drizzle-kit generate --config ./drizzle.config.ts", "cwd": "apps/codarashi"}}, "db-migrate": {"executor": "nx:run-commands", "options": {"command": "npx drizzle-kit migrate --config ./drizzle.config.ts", "cwd": "apps/codarashi"}}, "db-studio": {"executor": "nx:run-commands", "options": {"command": "npx drizzle-kit studio --config ./drizzle.config.ts", "cwd": "apps/codarashi"}}}}