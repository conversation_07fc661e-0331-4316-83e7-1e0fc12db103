{"extends": "./tsconfig.json", "include": ["remix.env.d.ts", "app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx"], "exclude": ["tests/**/*.spec.ts", "tests/**/*.test.ts", "tests/**/*.spec.tsx", "tests/**/*.test.tsx", "tests/**/*.spec.js", "tests/**/*.test.js", "tests/**/*.spec.jsx", "tests/**/*.test.jsx", "app/**/*.spec.ts", "app/**/*.test.ts", "app/**/*.spec.tsx", "app/**/*.test.tsx", "app/**/*.spec.js", "app/**/*.test.js", "app/**/*.spec.jsx", "app/**/*.test.jsx"]}