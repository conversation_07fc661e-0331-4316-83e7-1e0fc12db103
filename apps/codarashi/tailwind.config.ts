import type { Config } from 'tailwindcss';

export default {
  content: ['./app/**/*.{js,jsx,ts,tsx}'],
  safelist: [],
  theme: {
    extend: {
      colors: {
        primary: '#B95F89',
        'primary-1': '#C56994',
        'primary-2': '#D17A9F',
        'primary-3': '#DC8AAA',
        'primary-4': '#E89BB5',
        'primary-5': '#F3ACC0',
        'primary+1': '#A7517E',
        'primary+2': '#954973',
        'primary+3': '#833F68',
        'primary+4': '#71365D',
        'primary+5': '#5F2C52',

        secondary: '#9CAF88',
        'secondary-1': '#A8B79A',
        'secondary-2': '#B4BFAC',
        'secondary-3': '#C0C7BE',
        'secondary-4': '#CCCFD0',
        'secondary-5': '#D8D7E2',
        'secondary+1': '#90A37A',
        'secondary+2': '#84976C',
        'secondary+3': '#788B5E',
        'secondary+4': '#6C7F50',
        'secondary+5': '#607342',

        tertiary: '#7B96A7',
        'tertiary-1': '#8BA1B2',
        'tertiary-2': '#9BACBD',
        'tertiary-3': '#ABB8C8',
        'tertiary-4': '#BBC3D3',
        'tertiary-5': '#CBCFDE',
        'tertiary+1': '#6F8B9B',
        'tertiary+2': '#637F8F',
        'tertiary+3': '#577384',
        'tertiary+4': '#4B6778',
        'tertiary+5': '#3F5B6C',

        regular: '#1C270C',
        neutral: '#737373',
        'neutral-1': '#838383',
        'neutral-2': '#949494',
        'neutral-3': '#A4A4A4',
        'neutral-4': '#B5B5B5',
        'neutral-5': '#C5C5C5',
        'neutral+1': '#666666',
        'neutral+2': '#595959',
        'neutral+3': '#4C4C4C',
        'neutral+4': '#3F3F3F',
        'neutral+5': '#323232',
        'on-primary': '#FFFFFF',
        'on-primary+1': '#E0E0E0',

        special: '#7421B8',
        'special-1': '#8432C2',
        'special-2': '#9443CC',
        'special-3': '#A454D6',
        'special-4': '#B465E0',
        'special-5': '#C576EA',
        'special+1': '#681DAA',
        'special+2': '#5C189C',
        'special+3': '#50148E',
        'special+4': '#440F80',
        'special+5': '#380B72',

        success: '#77DD77',
        'success-1': '#88E488',
        'success-2': '#99EB99',
        'success-3': '#AAF2AA',
        'success-4': '#BBF9BB',
        'success-5': '#CCFFCC',
        'success+1': '#6BD36B',
        'success+2': '#5FC95F',
        'success+3': '#53BF53',
        'success+4': '#47B547',
        'success+5': '#3BAB3B',

        danger: '#AA0114',
        'danger-1': '#B51725',
        'danger-2': '#C02D36',
        'danger-3': '#CA4347',
        'danger-4': '#D55958',
        'danger-5': '#E06F69',
        'danger+1': '#9B0113',
        'danger+2': '#8C0111',
        'danger+3': '#7D010F',
        'danger+4': '#6E010D',
        'danger+5': '#5F010B',

        warn: '#EDB32B',
        'warn-1': '#EEC140',
        'warn-2': '#EFCE55',
        'warn-3': '#F1DC6A',
        'warn-4': '#F2E97F',
        'warn-5': '#F4F694',
        'warn+1': '#E4A526',
        'warn+2': '#DA9721',
        'warn+3': '#D0891C',
        'warn+4': '#C67B17',
        'warn+5': '#BC6D12',

        info: '#2B65EC',
        'info-1': '#4A74ED',
        'info-2': '#6984EE',
        'info-3': '#8894EF',
        'info-4': '#A7A3F0',
        'info-5': '#C6B3F1',
        'info+1': '#255AE6',
        'info+2': '#1F4FDF',
        'info+3': '#1944D8',
        'info+4': '#1339D1',
        'info+5': '#0D2ECA',
      },
      fontFamily: {
        sans: ['JetBrains Mono Variable', 'monospace'],
        serif: ['Merriweather', 'serif'],
      },
      fontSize: {},
      spacing: {
        none: '0',
        xs: '.25rem', // 4px
        sm: '.5rem', // 8px
        'sm+1': '.75rem', // 12px
        md: '1rem', // 16px
        'md+1': '1.25rem', // 20px
        lg: '1.5rem', // 24px
        'lg+1': '2rem', // 32px
        xl: '3rem', // 48px
        '2xl': '4rem', // 64px
        huge: '6rem', // 96px
      },
    },
  },
  plugins: [],
} satisfies Config;
