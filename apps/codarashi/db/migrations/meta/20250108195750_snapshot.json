{"version": "6", "dialect": "sqlite", "id": "d425851e-98ad-41d3-9b6d-ae2bcddbba5b", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"profile-header-nodes": {"name": "profile-header-nodes", "columns": {"_id": {"name": "_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "profile_id": {"name": "profile_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "header_id": {"name": "header_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "new_line": {"name": "new_line", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "text_size": {"name": "text_size", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visible": {"name": "visible", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"profile-header-nodes_id_unique": {"name": "profile-header-nodes_id_unique", "columns": ["id"], "isUnique": true}}, "foreignKeys": {"profile-header-nodes_profile_id_profiles_id_fk": {"name": "profile-header-nodes_profile_id_profiles_id_fk", "tableFrom": "profile-header-nodes", "tableTo": "profiles", "columnsFrom": ["profile_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile-header-nodes_header_id_profile-sections_id_fk": {"name": "profile-header-nodes_header_id_profile-sections_id_fk", "tableFrom": "profile-header-nodes", "tableTo": "profile-sections", "columnsFrom": ["header_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "profiles": {"name": "profiles", "columns": {"_id": {"name": "_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "linkedin": {"name": "linkedin", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github": {"name": "github", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"profiles_id_unique": {"name": "profiles_id_unique", "columns": ["id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "profile-section-nodes": {"name": "profile-section-nodes", "columns": {"_id": {"name": "_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "profile_id": {"name": "profile_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "section_id": {"name": "section_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "spacing": {"name": "spacing", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"profile-section-nodes_id_unique": {"name": "profile-section-nodes_id_unique", "columns": ["id"], "isUnique": true}}, "foreignKeys": {"profile-section-nodes_profile_id_profiles_id_fk": {"name": "profile-section-nodes_profile_id_profiles_id_fk", "tableFrom": "profile-section-nodes", "tableTo": "profiles", "columnsFrom": ["profile_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile-section-nodes_section_id_profile-sections_id_fk": {"name": "profile-section-nodes_section_id_profile-sections_id_fk", "tableFrom": "profile-section-nodes", "tableTo": "profile-sections", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "profile-sections": {"name": "profile-sections", "columns": {"_id": {"name": "_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "profile_id": {"name": "profile_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "visible": {"name": "visible", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "gap": {"name": "gap", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "alignment": {"name": "alignment", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"profile-sections_id_unique": {"name": "profile-sections_id_unique", "columns": ["id"], "isUnique": true}}, "foreignKeys": {"profile-sections_profile_id_profiles_id_fk": {"name": "profile-sections_profile_id_profiles_id_fk", "tableFrom": "profile-sections", "tableTo": "profiles", "columnsFrom": ["profile_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "profile-tags": {"name": "profile-tags", "columns": {"_id": {"name": "_id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "id": {"name": "id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "profile_id": {"name": "profile_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"profile-tags_id_unique": {"name": "profile-tags_id_unique", "columns": ["id"], "isUnique": true}}, "foreignKeys": {"profile-tags_profile_id_profiles_id_fk": {"name": "profile-tags_profile_id_profiles_id_fk", "tableFrom": "profile-tags", "tableTo": "profiles", "columnsFrom": ["profile_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}