{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["vitest/globals", "vitest/importMeta", "vite/client", "node", "vitest", "@testing-library/jest-dom"]}, "include": ["vite.config.ts", "vitest.config.ts", "app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "tests/**/*.spec.ts", "tests/**/*.test.ts", "tests/**/*.spec.tsx", "tests/**/*.test.tsx", "tests/**/*.spec.js", "tests/**/*.test.js", "tests/**/*.spec.jsx", "tests/**/*.test.jsx"]}