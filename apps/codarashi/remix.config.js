import { createWatchPaths } from '@nx/remix';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

/**
 * @type {import('@remix-run/dev').AppConfig}
 */
export default {
  ignoredRouteFiles: ['**/.*'],
  // appDirectory: "app",
  // assetsBuildDirectory: "public/build",
  // serverBuildPath: "build/index.js",
  // publicPath: "/build/",
  watchPaths: () => createWatchPaths(__dirname),

  routes(defineRoutes) {
    return defineRoutes((route) => {
      route("/profile", "modules/profile/profile.route.tsx", () => {

        route("header/edit", "modules/profile/header/edit-header.route.tsx");
        route("header/quick-edit", "modules/profile/header/quick-edit-header.route.ts");
        route("header/nodes", "modules/profile/header/create-node.route.tsx");
        route("header/nodes/:nodeId/edit", "modules/profile/header/edit-node.route.tsx");
        route("header/nodes/:nodeId/delete", "modules/profile/header/delete-node.route.tsx");
        route("header/nodes/:nodeId/quick-edit", "modules/profile/header/quick-edit-node.route.ts");

        route("summary/edit", "modules/profile/summary/edit-summary.route.tsx");
        route("summary/quick-edit", "modules/profile/summary/quick-edit-summary.route.ts");
        route("summary/nodes", "modules/profile/summary/create-node.route.tsx");
        route("summary/nodes/:nodeId/edit", "modules/profile/summary/edit-node.route.tsx");
        route("summary/nodes/:nodeId/delete", "modules/profile/summary/delete-node.route.tsx");
        route("summary/nodes/:nodeId/move", "modules/profile/summary/move-node.route.ts");

      });

      route("/profile/settings/tags", "modules/profile/shared:tags/tags.route.tsx", () => {
        route("create", "modules/profile/shared:tags/create-tag.route.tsx");
        route(":tagId/delete", "modules/profile/shared:tags/delete-tag.route.tsx");
        route(":tagId/edit", "modules/profile/shared:tags/edit-tag.route.tsx");
      });

    });
  },
};
