import {
  CustomError,
  EmailAddress,
  <PERSON>rrorMessages,
  UnexpectedError,
} from '@awe/core';

import { db, DbOrTx } from '~/database.server';
import { generateId as generateUserId, UserId } from '~/domain-entities/User';
import * as entity from './entities/Profile';
import * as repo from './profile.drizzle.repository';
import { createDefaultSections } from './shared:sections/section.service';

const createProfile = async (
  dto: entity.CreateDto
): Promise<entity.Profile> => {
  const profile = entity.create(dto);

  try {
    await db.transaction(async (tx) => {
      await repo.createProfile(profile, tx);

      await createDefaultSections(profile, tx);
    });
  } catch (error) {
    throw new UnexpectedError(error);
  }

  return profile;
};

export const updateProfile = async (
  dto: entity.SaveDto,
  tx: DbOrTx = db
): Promise<entity.Profile> => {
  const profile = await findProfile(generateUserId(), tx);

  if (!profile) {
    throw new UnexpectedError('Profile not found');
  }

  const updatedProfile = entity.update(dto, profile);

  await repo.update(updatedProfile, tx);

  return updatedProfile;
};

const findProfile = async (
  userId: UserId,
  tx: DbOrTx = db
): Promise<entity.Profile | null> => {
  const result = await repo.findByUserId(userId, tx);

  return result;
};

export const getOrCreateProfile = async (): Promise<
  entity.Profile<'with_sections'>
> => {
  const userId = generateUserId();
  const profile = await repo.getWithSections(userId).catch(async (err) => {
    const knownError = CustomError.toError(err);

    if (CustomError.isDomainError(knownError, ErrorMessages.EntityNotFound)) {
      await createProfile({
        user_id: userId,
        email: EmailAddress.parse('<EMAIL>'),
      });

      return await repo.getWithSections(userId);
    }

    throw knownError;
  });

  return profile;
};

export const getProfile = async (
  userId: UserId = generateUserId()
): Promise<entity.Profile> => {
  const profile = await findProfile(userId);

  if (!profile) {
    throw new UnexpectedError('Profile not found');
  }

  return profile;
};
