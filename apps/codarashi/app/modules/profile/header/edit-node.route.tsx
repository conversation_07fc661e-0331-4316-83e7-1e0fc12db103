import {
  json,
  useActionData,
  useLoaderData,
  useOutletContext,
} from '@remix-run/react';

import { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { AppNavigate } from '~/components/navigation/navigate';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { toProfile } from '../entities/Profile';
import { getProfile } from '../profile.service';
import { getAllForProfile } from '../shared:tags/tags.service';
import { updateNode } from './backend/node.service';
import { HeaderNodeForm } from './components/forms/node.form';
import { HeaderNode, toId, toSaveDto } from './entities/HeaderNode';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params, payload }) => {
      const dto = toSaveDto(payload);
      const targetNodeId = toId(params.nodeId);

      const updatedNode = await updateNode(dto, targetNodeId);

      return json(
        {
          status: 'success',
          data: updatedNode,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = (args: LoaderFunctionArgs) =>
  createRouteLoader({
    loader: async () => {
      const profile = await getProfile();
      const tags = await getAllForProfile(profile.id);

      return json({ status: 'success', tags, profile } as const);
    },
  })(args);

export default function EditHeaderNodePage() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const context = useOutletContext<{ node?: HeaderNode }>();

  if (loaderData?.status === 'error') {
    return <div>Something went wrong.</div>;
  }

  if (actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  if (!context?.node) {
    return <div>Node not found.</div>;
  }

  return (
    <HeaderNodeForm
      tags={loaderData.tags}
      profile={toProfile(loaderData.profile)}
      node={context.node}
      order={context.node.order}
      nodeType={context.node.type}
    />
  );
}
