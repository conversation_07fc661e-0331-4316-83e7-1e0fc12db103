import { match } from 'ts-pattern';
import { z } from 'zod';

import {
  CustomUrl,
  DomainError,
  ErrorMessages,
  NanoId,
  Numbers,
  StrictExtract,
  StringOfLength,
  zodToDomainOrThrow,
} from '@awe/core';
import { id as tagParser } from '~/modules/profile/shared:tags/Tag';
import { Profile } from '../../entities/Profile';
import {
  profileEmail,
  profileGithub,
  profileLinkedin,
  profileLocation,
  profileName,
  profileTitle,
} from '../../entities/profile-fields';

export const Type = {
  Name: 'name',
  Title: 'title',
  Location: 'location',
  Email: 'email',
  LinkedIn: 'linkedin',
  GitHub: 'github',
  WebProfile: 'web-profile',
  Link: 'link',
} as const;

export type Type = (typeof Type)[keyof typeof Type];

export const TextSize = {
  Normal: 'normal',
  Large: 'large',
  ExtraLarge: 'extra-large',
} as const;

export type TextSize = (typeof TextSize)[keyof typeof TextSize];

export const QuickAction = {
  ChangeTextSize: 'change-text-size',
  MoveNext: 'move-next',
  MovePrev: 'move-prev',
  ToggleNewLine: 'toggle-new-line',
  ToggleVisibility: 'toggle-visibility',
} as const;

export type QuickAction = (typeof QuickAction)[keyof typeof QuickAction];

const quickActionParser = z.object({
  action: z.nativeEnum(QuickAction),
});
export const toQuickActionDto = (source: unknown) =>
  zodToDomainOrThrow(quickActionParser, source);
export type QuickActionDto = z.infer<typeof quickActionParser>;

const id = NanoId.parser('sctn_hdr_node');

export const toId = (source: unknown) =>
  zodToDomainOrThrow(z.object({ id }), { id: source }).id;

const baseNode = z.object({
  id,
  new_line: z.boolean(),
  text_size: z.nativeEnum(TextSize),
  order: Numbers.positiveIntegerParser,
  visible: z.boolean(),
});

const emailParser = baseNode.merge(
  z.object({
    type: z.literal(Type.Email),
    title: StringOfLength.parser(0, 100, 'email').optional().nullable(),
  })
);

const tagsParser = z.preprocess(
  (value) => (typeof value === 'string' ? [value] : value),
  z.array(tagParser).optional()
);

const titleParser = z
  .object({
    type: z.literal(Type.Title),
  })
  .merge(baseNode);

const nameParser = z
  .object({
    type: z.literal(Type.Name),
  })
  .merge(baseNode);

const locationParser = z
  .object({
    type: z.literal(Type.Location),
    tags: tagsParser,
  })
  .merge(baseNode);

const githubParser = z
  .object({
    type: z.literal(Type.GitHub),
    title: StringOfLength.parser(1, 100, 'github').nullable(),
    tags: tagsParser,
  })
  .merge(baseNode);

const linkedinParser = z
  .object({
    type: z.literal(Type.LinkedIn),
    title: StringOfLength.parser(1, 100, 'linkedin').nullable(),
    tags: tagsParser,
  })
  .merge(baseNode);

const webProfileParser = z
  .object({
    type: z.literal(Type.WebProfile),
    title: StringOfLength.parser(1, 100, 'web_profile').nullable(),
    tags: tagsParser,
  })
  .merge(baseNode);

const linkParser = z
  .object({
    type: z.literal(Type.Link),
    title: StringOfLength.parser(1, 100, 'custom_link').nullable(),
    value: CustomUrl.parser('custom_link'),
    tags: tagsParser,
  })
  .merge(baseNode);

export const parser = z.discriminatedUnion('type', [
  emailParser,
  titleParser,
  nameParser,
  locationParser,
  githubParser,
  linkedinParser,
  webProfileParser,
  linkParser,
]);

export type HeaderNode = z.infer<typeof parser>;
export const toNode = (source: unknown): HeaderNode =>
  zodToDomainOrThrow(parser, source);
export const toNodes = (source: unknown[]): HeaderNode[] =>
  zodToDomainOrThrow(z.object({ items: z.array(parser) }), { items: source })
    .items;

const saveDtoParser = z.discriminatedUnion('type', [
  emailParser.omit({ id: true }).merge(
    z.object({
      value: profileEmail,
    })
  ), // only paying users can change this value
  titleParser.omit({ id: true }).merge(
    z.object({
      value: profileTitle,
    })
  ),
  nameParser.omit({ id: true }).merge(
    z.object({
      value: profileName,
    })
  ),
  locationParser.omit({ id: true }).merge(
    z.object({
      value: profileLocation,
    })
  ),
  githubParser.omit({ id: true }).merge(
    z.object({
      value: profileGithub,
    })
  ),
  linkedinParser.omit({ id: true }).merge(
    z.object({
      value: profileLinkedin,
    })
  ),
  webProfileParser.omit({ id: true }),
  linkParser.omit({ id: true }),
]);

export const toSaveDto = (data: unknown) =>
  zodToDomainOrThrow(saveDtoParser, data);

export const valueComesFromProfile = (
  node: HeaderNode
): node is Exclude<
  HeaderNode,
  { type: typeof Type.Link } | { type: typeof Type.WebProfile }
> => {
  const nodesWithOwnValue = [
    Type.WebProfile,
    Type.Link,
  ] as HeaderNode['type'][];

  return !nodesWithOwnValue.includes(node.type);
};

export const updateNode = <T extends Type>(
  dto: Extract<z.infer<typeof saveDtoParser>, { type: T }>,
  node: Extract<HeaderNode, { type: T }>
): Extract<HeaderNode, { type: T }> => {
  return {
    type: node.type,
    id: node.id,
    text_size: dto.text_size || node.text_size || TextSize.Normal,
    new_line: dto.new_line ?? node.new_line ?? false,
    order: dto.order ?? node.order,

    ...('tags' in dto && 'tags' in node
      ? { tags: dto.tags || node.tags || [] }
      : {}),

    ...('title' in dto && 'title' in node
      ? { title: dto.title || node.title }
      : {}),

    ...('value' in dto && 'value' in node
      ? { value: dto.value || node.value }
      : {}),

    ...('visible' in dto && 'visible' in node
      ? { visible: dto.visible ?? node.visible ?? true }
      : {}),
  } as Extract<HeaderNode, { type: T }>;
};

const linkDtoParser = linkParser.omit({ id: true });

export const toCreateLinkDto = (data: unknown) =>
  zodToDomainOrThrow(linkDtoParser, data);

export type CreateLinkDto = z.infer<typeof linkDtoParser>;

export const createLinkNode = (
  dto: z.infer<typeof linkDtoParser>,
  id = generateNodeId()
): StrictExtract<HeaderNode, { type: 'link' }> => {
  return {
    ...dto,
    id,
  };
};

export type HeaderNodeSaveDto = z.infer<typeof saveDtoParser>;

export const generateNodeId = () => NanoId.generate('sctn_hdr_node');

export const getNextSize = (node: HeaderNode): TextSize => {
  const sizes = [...Object.values(TextSize), ...Object.values(TextSize)];
  const currentSize = node.text_size || TextSize.Normal;

  const currentPosition = sizes.indexOf(currentSize);

  return sizes[currentPosition + 1];
};

type LinkBasedNode =
  | StrictExtract<HeaderNode, { type: typeof Type.Link }>
  | StrictExtract<HeaderNode, { type: typeof Type.WebProfile }>
  | StrictExtract<HeaderNode, { type: typeof Type.GitHub }>
  | StrictExtract<HeaderNode, { type: typeof Type.LinkedIn }>;

export const isLinkBasedNode = (node: HeaderNode): node is LinkBasedNode => {
  const linkBasedNodes = [
    Type.Link,
    Type.WebProfile,
    Type.GitHub,
    Type.LinkedIn,
  ] as HeaderNode['type'][];

  return linkBasedNodes.includes(node.type);
};

export const getNodeLink = (node: LinkBasedNode, profile: Profile): string => {
  return match(node)
    .with({ type: Type.Link }, (item) => item.value)
    .with({ type: Type.WebProfile }, () => 'TODO')
    .with({ type: Type.GitHub }, () => profile.github)
    .with({ type: Type.LinkedIn }, () => profile.linkedin)
    .exhaustive();
};

export const getNodeValue = (node: HeaderNode, profile: Profile) => {
  return match(node)
    .with({ type: Type.Link }, (item) => item.value)
    .with({ type: Type.WebProfile }, () => 'TODO')
    .with({ type: Type.GitHub }, () => profile.github)
    .with({ type: Type.LinkedIn }, () => profile.linkedin)
    .with({ type: Type.Name }, () => profile.name)
    .with({ type: Type.Title }, () => profile.title)
    .with({ type: Type.Email }, () => profile.email)
    .with({ type: Type.Location }, () => profile.location)
    .exhaustive();
};

export const canBeHidden = (
  node: HeaderNode
): node is Exclude<
  HeaderNode,
  | { type: typeof Type.Name }
  | { type: typeof Type.Title }
  | { type: typeof Type.Email }
> => {
  const alwaysVisibleNodes = [
    Type.Name,
    Type.Title,
    Type.Email,
  ] as HeaderNode['type'][];
  return !alwaysVisibleNodes.includes(node.type);
};

export const canBeDeleted = (nodeType: Type): boolean => {
  const deletableNodes = [Type.Link] as HeaderNode['type'][];
  return deletableNodes.includes(nodeType);
};

export const changeTextSize = (node: HeaderNode): HeaderNode => {
  return {
    ...node,
    text_size: getNextSize(node),
  };
};

export const toggleLineBreak = (node: HeaderNode): HeaderNode => {
  return {
    ...node,
    new_line: !node.new_line,
  };
};

export const toggleVisibility = (node: HeaderNode): HeaderNode => {
  if (!canBeHidden(node)) {
    throw new DomainError(ErrorMessages.InvalidAction, {
      message: `Cannot toggle visibility of node of type: ${node.type}`,
    });
  }

  return {
    ...node,
    visible: !node.visible,
  };
};
