import { z } from 'zod';

import {
  DomainError,
  ErrorMessages,
  moveArrayItem,
  Numbers,
  StringOfLength,
  zodToDomainOrThrow,
} from '@awe/core';

import { Direction } from '~/utils/misc';
import {
  Alignment,
  Gap,
  generateSectionId,
  QuickAction,
  sectionBase,
  SectionType,
} from '../../shared:sections/section.shared';
import {
  generateNodeId,
  HeaderNode,
  parser as nodeParser,
  Type as NodeType,
  TextSize,
} from './HeaderNode';

export const parser = {
  withNodes: sectionBase.merge(
    z.object({
      type: z.literal(SectionType.Header),
      nodes: z.array(nodeParser).transform((nodes) => {
        return nodes.sort((a, b) => a.order - b.order);
      }),
    })
  ),
  base: sectionBase.merge(
    z.object({
      type: z.literal(SectionType.Header),
    })
  ),
};

type HeaderWithNodes = z.infer<typeof parser.withNodes>;
type BaseHeader = z.infer<typeof parser.base>;

type Variants = 'base' | 'with_nodes';

export type Header<T extends Variants = 'base'> = T extends 'base'
  ? BaseHeader
  : HeaderWithNodes;

export function toHeader(
  source: unknown,
  variant: 'with_nodes'
): Header<'with_nodes'>;
export function toHeader(source: unknown, variant: 'base'): Header<'base'>;
export function toHeader<T extends Variants>(
  source: unknown,
  variant: T
): Header<T> {
  return (
    variant === 'base'
      ? zodToDomainOrThrow(parser.base, source)
      : zodToDomainOrThrow(parser.withNodes, source)
  ) as Header<T>;
}

const saveDtoParser = parser.base
  .pick({
    gap: true,
    alignment: true,
    title: true,
  })
  .brand('Profile_Header_SaveDto');

export const toSaveDto = (source: unknown) =>
  zodToDomainOrThrow(saveDtoParser, source);

export type SaveDto = z.infer<typeof saveDtoParser>;

export const update = (
  header: Header<'base'>,
  dto: SaveDto
): Header<'base'> => {
  return {
    ...header,
    ...dto,
  };
};

const quickActionParser = z.object({
  action: z.enum([
    QuickAction.MoveNext,
    QuickAction.MovePrev,
    QuickAction.ToggleAlignment,
    QuickAction.ToggleSpacing,
  ]),
});
export type QuickActionDto = z.infer<typeof quickActionParser>;
export const toQuickActionDto = (source: unknown) =>
  zodToDomainOrThrow(quickActionParser, source);

export const moveNode = (
  nodes: HeaderNode[],
  nodeId: HeaderNode['id'],
  direction: Direction
): HeaderNode[] => {
  const index = nodes.findIndex((node) => node.id === nodeId);

  if (index === -1) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      message: 'Node not found: id: ' + nodeId,
    });
  }

  return moveArrayItem(
    nodes,
    index,
    direction === 'next' ? index + 1 : index - 1
  ).map((elem, index) => ({
    ...elem,
    order: Numbers.toPositiveInteger(index + 1),
  }));
};

export const createDefaults = (
  order: Numbers.PositiveInteger,
  id = generateSectionId()
): Header<'with_nodes'> => {
  return {
    id,
    type: SectionType.Header,
    visible: true,
    gap: Gap.Small,
    alignment: Alignment.Center,
    title: StringOfLength.generate('', 0, 50),
    order,
    nodes: [
      {
        type: NodeType.Name,
        id: generateNodeId(),
        text_size: TextSize.ExtraLarge,
        new_line: false,
        order: Numbers.toPositiveInteger(1),
        visible: true,
      },
      {
        type: NodeType.Title,
        id: generateNodeId(),
        text_size: TextSize.Large,
        new_line: false,
        order: Numbers.toPositiveInteger(2),
        visible: true,
      },
      {
        type: NodeType.Email,
        id: generateNodeId(),
        text_size: TextSize.Normal,
        new_line: true,
        order: Numbers.toPositiveInteger(3),
        visible: true,
      },
      {
        type: NodeType.Location,
        id: generateNodeId(),
        text_size: TextSize.Normal,
        new_line: false,
        order: Numbers.toPositiveInteger(4),
        visible: true,
      },
      {
        type: NodeType.WebProfile,
        id: generateNodeId(),
        visible: true,
        title: StringOfLength.generate('Web Profile', 1, 100, 'web_profile'),
        text_size: TextSize.Normal,
        new_line: false,
        order: Numbers.toPositiveInteger(5),
      },
      {
        type: NodeType.LinkedIn,
        id: generateNodeId(),
        visible: true,
        title: StringOfLength.generate('LinkedIn', 1, 100, 'linkedin'),
        text_size: TextSize.Normal,
        new_line: false,
        order: Numbers.toPositiveInteger(6),
      },
      {
        type: NodeType.GitHub,
        id: generateNodeId(),
        visible: true,
        title: StringOfLength.generate('GitHub', 1, 100, 'github'),
        text_size: TextSize.Normal,
        new_line: false,
        order: Numbers.toPositiveInteger(7),
      },
    ] as const,
  };
};
