import { createRemixStub } from '@remix-run/testing';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import * as ProfileRoute from '../profile.route';

describe('HeaderSection', () => {
  it('has context menu', async () => {
    const ProfileRouteStub = createRemixStub([
      {
        path: '/profile',
        Component: () => <ProfileRoute.default />,
        action: ProfileRoute.action,
        loader: ProfileRoute.loader,
      },
    ]);

    render(<ProfileRouteStub initialEntries={['/profile']} />);

    const headerSection = await screen.findByRole('region', {
      name: 'Header section',
    });

    const contextActionsButton = within(headerSection).getByRole('button', {
      name: 'Header section',
    });

    await userEvent.click(contextActionsButton);

    const menu = await screen.findByRole('menu', { name: 'Header section' });
    const actions = within(menu).getAllByRole('menuitem');

    expect(actions).toHaveLength(4);
    expect(actions[0]).toHaveAccessibleName('Edit');
    expect(actions[1]).toHaveAccessibleName('Change alignment to left');
    expect(actions[2]).toHaveAccessibleName('Adjust spacing');
    expect(actions[3]).toHaveAccessibleName('Move downwards');
  });
});
