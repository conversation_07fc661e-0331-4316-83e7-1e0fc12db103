import { exhaustive } from 'exhaustive';
import { FC } from 'react';
import { FaTurnDown } from 'react-icons/fa6';
import { match } from 'ts-pattern';

import { Typography } from '~/design-system';
import classNames from '~/utils/css';
import { Profile } from '../../entities/Profile';
import {
  canBeHidden,
  HeaderNode,
  HeaderNode as Node,
  Type as NodeType,
  TextSize,
} from '../entities/HeaderNode';
import { NodeContextActions } from './context-actions';

type Props = {
  profile: Profile;
  node: Node;
  position: number;
  isLast: boolean;
};

export const AbstractHeaderNode: FC<Props> = ({
  node,
  profile,
  position,
  isLast,
}) => {
  return (
    <NodeContextActions
      node={node}
      profile={profile}
      actions={{
        canMovePrev: position > 0,
        canMoveNext: !isLast,
        canDelete: node.type === 'link',
        canToggleVisibility: canBeHidden(node),
      }}
      label={
        <Typography
          size={exhaustive(node.text_size || TextSize.Normal, {
            [TextSize.Normal]: () => 'p',
            [TextSize.Large]: () => 'h4',
            [TextSize.ExtraLarge]: () => 'h3',
          })}
          className={classNames('flex gap-xs items-center', {
            'line-through': canBeHidden(node) && node.visible === false,
          })}
        >
          {node.new_line ? (
            <FaTurnDown className={'text-neutral+3 w-[8px]'} />
          ) : (
            ''
          )}
          {getDisplayName(node, profile)}
        </Typography>
      }
    />
  );
};

const getDisplayName = (node: HeaderNode, profile: Profile): string => {
  return match(node)
    .with({ type: NodeType.Name }, () => profile.name)
    .with({ type: NodeType.Title }, () => profile.title)
    .with({ type: NodeType.Email }, () => profile.email)
    .with({ type: NodeType.Link }, (item) =>
      'title' in node && node.title ? node.title : item.value
    )
    .with({ type: NodeType.WebProfile }, () =>
      'title' in node && node.title ? node.title : 'TODO'
    )
    .with({ type: NodeType.GitHub }, () =>
      'title' in node && node.title ? node.title : profile.github
    )
    .with({ type: NodeType.LinkedIn }, () =>
      'title' in node && node.title ? node.title : profile.linkedin
    )
    .with({ type: NodeType.Location }, () => profile.location)
    .exhaustive();
};
