import { FC, useEffect, useRef } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { StrictExtract } from '@awe/core';

import { VerticalStack } from '~/design-system';
import { TextField } from '~/design-system/Input';
import { TagsPicker } from '~/modules/profile/components/tags-picker';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { HeaderNodeSaveDto } from '../../entities/HeaderNode';

type Props = {
  tags: Tag[];
  form: UseFormReturn<StrictExtract<HeaderNodeSaveDto, { type: 'github' }>>;
};

export const GitHubForm: FC<Props> = ({ form, tags }) => {
  const firstInput = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    firstInput.current?.focus();
  }, []);

  return (
    <VerticalStack gap="lg">
      <Controller
        name="value"
        control={form.control}
        render={(params) => (
          <TextField
            label="GitHub profile URL"
            ref={(ref) => {
              params.field.ref(ref);
              firstInput.current = ref;
            }}
            value={params.field.value}
            onChange={(value) => params.field.onChange(value)}
            error={params.fieldState.error?.message}
          />
        )}
      />

      <Controller
        name="title"
        control={form.control}
        render={(params) => (
          <TextField
            label="Display name"
            {...params.field}
            value={params.field.value ?? ''}
            error={params.fieldState.error?.message}
          />
        )}
      />

      <Controller
        name="tags"
        control={form.control}
        render={(params) => (
          <div className="pb-md border-b border-neutral-5 border-solid">
            <TagsPicker
              tags={tags}
              value={params.field.value ?? []}
              onChange={(event) => {
                if (event.target.checked) {
                  params.field.onChange([
                    ...(params.field.value ?? []),
                    event.target.value,
                  ]);
                } else {
                  params.field.onChange(
                    params.field.value?.filter(
                      (id) => id !== event.target.value
                    ) ?? []
                  );
                }
              }}
            />
          </div>
        )}
      />

      <Controller
        name="visible"
        control={form.control}
        defaultValue={true}
        render={(params) => (
          <label className="flex items-center gap-sm cursor-pointer">
            <input
              type="checkbox"
              checked={!!params.field.value}
              onChange={params.field.onChange}
              className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
            />
            <span>Visible</span>
          </label>
        )}
      />
    </VerticalStack>
  );
};
