import { FC } from 'react';

import { TextSize } from '../../entities/HeaderNode';

type Props = {
  value: TextSize | undefined;
  onChange: React.ChangeEventHandler<HTMLInputElement>;
};

export const TextSizePicker: FC<Props> = ({ value, onChange }) => {
  return (
    <fieldset className="flex flex-col gap-sm">
      <legend className="mb-sm">Text size</legend>
      <label className="flex items-center gap-sm cursor-pointer">
        <input
          type="radio"
          name="size"
          value={TextSize.Normal}
          checked={value === TextSize.Normal}
          onChange={onChange}
          className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
        />
        <span>Normal</span>
      </label>

      <label className="flex items-center gap-sm cursor-pointer">
        <input
          type="radio"
          name="size"
          value={TextSize.Large}
          checked={value === TextSize.Large}
          onChange={onChange}
          className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
        />
        <span>Large</span>
      </label>

      <label className="flex items-center gap-sm cursor-pointer">
        <input
          type="radio"
          name="size"
          value={TextSize.ExtraLarge}
          checked={value === TextSize.ExtraLarge}
          onChange={onChange}
          className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
        />
        <span>Extra large</span>
      </label>
    </fieldset>
  );
};
