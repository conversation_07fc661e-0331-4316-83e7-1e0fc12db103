import { Form, useSubmit } from '@remix-run/react';
import { exhaustive } from 'exhaustive';
import { FC } from 'react';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';

import { Numbers, StrictExtract } from '@awe/core';
import {
  HorizontalStack,
  PrimaryButton,
  SecondaryButton,
} from '~/design-system';
import { Profile } from '~/modules/profile/entities/Profile';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { jsonToFormData } from '~/utils/form-data';
import { useFormResolver } from '~/utils/form-resolver';
import { AppSearchParams } from '~/utils/misc';
import {
  getNodeValue,
  HeaderNode,
  HeaderNodeSaveDto,
  Type as NodeType,
  TextSize,
  toSaveDto,
} from '../../entities/HeaderNode';
import { EmailForm } from './email.form';
import { GitHubForm } from './github.form';
import { LinkForm } from './link.form';
import { LinkedInForm } from './linkedin.form';
import { LocationForm } from './location.form';
import { NameForm } from './name.form';
import { TextSizePicker } from './shared';
import { TitleForm } from './title.form';
import { WebProfileForm } from './web-profile.form';

type Props = {
  nodeType: NodeType;
  tags: Tag[];
  profile: Profile;
  order: Numbers.PositiveInteger;
  node?: HeaderNode;
};

export const HeaderNodeForm: FC<Props> = ({
  nodeType,
  profile,
  node,
  order,
  tags,
}) => {
  const submit = useSubmit();

  const form = useForm<HeaderNodeSaveDto>({
    resolver: useFormResolver(toSaveDto),
    defaultValues: {
      type: nodeType,
      order,
      text_size: node?.text_size ?? TextSize.Normal,
      tags: [],
      new_line: node?.new_line ?? false,
      ...(node ? { value: getNodeValue(node, profile) } : {}),
      ...node,
    } as HeaderNodeSaveDto,
  });

  return (
    <Form
      method="post"
      action={`?${AppSearchParams.RedirectTo}=/profile`}
      onSubmit={form.handleSubmit(
        (formData) => {
          const dto = toSaveDto(formData);

          submit(jsonToFormData(dto), { method: 'post' });
        },
        (err) => console.log(err)
      )}
      className="flex flex-col gap-lg"
    >
      <input type="hidden" name="type" value={nodeType} />
      <input type="hidden" name="order" value={order} />

      {exhaustive(nodeType, {
        link: () => (
          <LinkForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'link' }>
              >
            }
            tags={tags}
          />
        ),
        name: () => (
          <NameForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'name' }>
              >
            }
          />
        ),
        title: () => (
          <TitleForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'title' }>
              >
            }
          />
        ),
        email: () => (
          <EmailForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'email' }>
              >
            }
          />
        ),
        location: () => (
          <LocationForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'location' }>
              >
            }
            tags={tags}
          />
        ),
        'web-profile': () => (
          <WebProfileForm
            webProfileUrl={'https://codarashi.com/profiles/1234'}
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'web-profile' }>
              >
            }
            tags={tags}
          />
        ),
        github: () => (
          <GitHubForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'github' }>
              >
            }
            tags={tags}
          />
        ),
        linkedin: () => (
          <LinkedInForm
            form={
              form as UseFormReturn<
                StrictExtract<HeaderNodeSaveDto, { type: 'linkedin' }>
              >
            }
            tags={tags}
          />
        ),
      })}

      <Controller
        name="new_line"
        control={form.control}
        render={(params) => (
          <label className="flex items-center gap-sm cursor-pointer">
            <input
              type="checkbox"
              checked={params.field.value}
              onChange={params.field.onChange}
              className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
            />
            <span>Start on new line</span>
          </label>
        )}
      />

      <Controller
        name="text_size"
        control={form.control}
        render={(params) => (
          <TextSizePicker
            value={params.field.value}
            onChange={params.field.onChange}
          />
        )}
      />

      <HorizontalStack gap="sm+1" itemsJustify="end">
        <SecondaryButton as="a" href="/profile">
          Cancel
        </SecondaryButton>
        <PrimaryButton>Save</PrimaryButton>
      </HorizontalStack>
    </Form>
  );
};
