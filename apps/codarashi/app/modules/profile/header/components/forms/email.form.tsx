import { FC, useEffect, useRef } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { StrictExtract } from '@awe/core';

import { VerticalStack } from '~/design-system';
import { TextField } from '~/design-system/Input';
import { HeaderNodeSaveDto } from '../../entities/HeaderNode';

type Props = {
  form: UseFormReturn<StrictExtract<HeaderNodeSaveDto, { type: 'email' }>>;
};

export const EmailForm: FC<Props> = ({ form }) => {
  const firstInput = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    firstInput.current?.focus();
  }, []);

  return (
    <VerticalStack gap="lg">
      <Controller
        name="value"
        control={form.control}
        render={(params) => (
          <TextField
            label="Email"
            ref={(ref) => {
              params.field.ref(ref);
              firstInput.current = ref;
            }}
            value={params.field.value}
            onChange={(value) => params.field.onChange(value)}
            error={params.fieldState.error?.message}
          />
        )}
      />

      <Controller
        name="title"
        control={form.control}
        render={(params) => (
          <TextField
            label="Display name"
            {...params.field}
            value={params.field.value ?? ''}
            error={params.fieldState.error?.message}
          />
        )}
      />
    </VerticalStack>
  );
};
