import { FC, useEffect, useRef } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { Form, useSubmit } from '@remix-run/react';
import {
  HorizontalStack,
  PrimaryButton,
  RemixLink,
  VerticalStack,
} from '~/design-system';
import { TextField } from '~/design-system/Input';
import { jsonToFormData } from '~/utils/form-data';
import { useFormResolver } from '~/utils/form-resolver';
import { AppSearchParams } from '~/utils/misc';
import { Alignment, Gap } from '../../shared:sections/section.shared';
import { Header, SaveDto, toSaveDto } from '../entities/Header';

type Props = {
  header: Header;
};

export const HeaderForm: FC<Props> = ({ header }) => {
  const submit = useSubmit();
  const firstInput = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (firstInput.current) {
      firstInput.current.focus();
    }
  }, []);

  const form = useForm<SaveDto>({
    resolver: useFormResolver(toSaveDto),
    defaultValues: {
      gap: header.gap,
      alignment: header.alignment,
      title: header.title,
    },
  });

  return (
    <Form
      method="post"
      action={`?${AppSearchParams.RedirectTo}=/profile`}
      className="flex flex-col gap-lg"
      onSubmit={(event) => {
        form.handleSubmit((dto) => {
          submit(jsonToFormData(dto), {
            method: 'post',
            action: '',
          });
        })(event);
      }}
    >
      <VerticalStack gap="lg">
        <legend>Edit header</legend>

        <Controller
          name="title"
          control={form.control}
          render={(params) => (
            <TextField
              label="Display name"
              {...params.field}
              error={params.fieldState.error?.message}
              ref={firstInput}
            />
          )}
        />

        <Controller
          name="alignment"
          control={form.control}
          render={(params) => (
            <fieldset className="flex flex-col gap-sm">
              <legend className="mb-sm">Alignment</legend>

              <label className="flex items-center gap-sm cursor-pointer">
                <input
                  type="radio"
                  checked={params.field.value === Alignment.Center}
                  {...params.field}
                  value={Alignment.Center}
                  onChange={params.field.onChange}
                  className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
                  autoFocus
                />
                <span>Center</span>
              </label>

              <label className="flex items-center gap-sm cursor-pointer">
                <input
                  type="radio"
                  {...params.field}
                  value={Alignment.Left}
                  checked={params.field.value === Alignment.Left}
                  onChange={params.field.onChange}
                  className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
                />
                <span>Left</span>
              </label>
            </fieldset>
          )}
        />

        <Controller
          name="gap"
          control={form.control}
          render={(params) => (
            <fieldset className="flex flex-col gap-sm">
              <legend className="mb-sm">Gap size</legend>

              <label className="flex items-center gap-sm cursor-pointer">
                <input
                  type="radio"
                  name="gap"
                  value={Gap.Small}
                  checked={params.field.value === Gap.Small}
                  onChange={params.field.onChange}
                  className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
                />
                <span>Small</span>
              </label>

              <label className="flex items-center gap-sm cursor-pointer">
                <input
                  type="radio"
                  name="gap"
                  value={Gap.Medium}
                  checked={params.field.value === Gap.Medium}
                  onChange={params.field.onChange}
                  className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
                />
                <span>Medium</span>
              </label>

              <label className="flex items-center gap-sm cursor-pointer">
                <input
                  type="radio"
                  name="gap"
                  value={Gap.Large}
                  checked={params.field.value === Gap.Large}
                  onChange={params.field.onChange}
                  className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
                />
                <span>Large</span>
              </label>
            </fieldset>
          )}
        />

        <HorizontalStack gap="sm+1" itemsJustify="end">
          <RemixLink to="/profile">Cancel</RemixLink>
          <PrimaryButton>Save</PrimaryButton>
        </HorizontalStack>
      </VerticalStack>
    </Form>
  );
};
