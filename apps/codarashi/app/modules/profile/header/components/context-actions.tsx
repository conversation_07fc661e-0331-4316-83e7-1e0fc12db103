import { useFetcher } from '@remix-run/react';
import { FC, ReactNode } from 'react';
import {
  FaChevronLeft,
  FaChevronRight,
  FaEye,
  FaEyeSlash,
  FaPencil,
  FaTrash,
  FaTurnDown,
  FaTurnUp,
} from 'react-icons/fa6';
import { IoEarthSharp } from 'react-icons/io5';
import { TbTextSize } from 'react-icons/tb';

import { IconButton, IconLink, IconLinkExternal } from '~/design-system';
import { NativePopover } from '~/design-system/NativePopover';
import classNames from '~/utils/css';
import { jsonToFormData } from '~/utils/form-data';
import { AppSearchParams, Direction } from '~/utils/misc';
import { Profile } from '../../entities/Profile';
import {
  getNextSize,
  getNodeLink,
  HeaderNode,
  isLinkBasedNode,
  QuickAction,
} from '../entities/HeaderNode';

type Props = {
  node: HeaderNode;
  profile: Profile;
  label: ReactNode;

  actions: {
    canMovePrev: boolean;
    canMoveNext: boolean;
    canDelete: boolean;
    canToggleVisibility: boolean;
  };
};

export const NodeContextActions: FC<Props> = ({
  node,
  profile,
  label,
  actions,
}) => {
  const fetcher = useFetcher();
  const fetcherConfig = {
    method: 'post',
    action: `/profile/header/nodes/${node.id}/quick-edit`,
  } as const;

  return (
    <NativePopover
      renderTrigger={(props) => (
        <button type="button" {...props}>
          {label}
        </button>
      )}
      uniqueId={node.id}
    >
      {({ close: closePopover }) => (
        <div className="flex items-center gap-md p-sm border-neutral-5 border bg-white rounded-md shadow-md">
          <IconLink
            to={`/profile/header/nodes/${node.id}/edit`}
            preventScrollReset
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            <FaPencil
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </IconLink>

          {actions.canDelete && (
            <IconLink
              to={`/profile/header/nodes/${node.id}/delete`}
              preventScrollReset
              onClick={() => {
                closePopover();
                return true;
              }}
            >
              <FaTrash
                className={classNames('text-neutral+3', {
                  'text-primary': false,
                })}
              />
            </IconLink>
          )}

          {actions.canToggleVisibility && 'visible' in node && (
            <fetcher.Form
              method="post"
              action={`/profile/header/nodes/${node.id}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.ToggleVisibility,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <IconButton
                type="submit"
                aria-label={node.visible ? 'Hide' : 'Show'}
              >
                {node.visible ? <FaEyeSlash /> : <FaEye />}
              </IconButton>
            </fetcher.Form>
          )}

          {isLinkBasedNode(node) && (
            <IconLinkExternal
              href={getNodeLink(node, profile)}
              aria-label="Open link"
              target="_blank"
              onClick={() => {
                closePopover();
                return true;
              }}
            >
              <IoEarthSharp />
            </IconLinkExternal>
          )}

          <fetcher.Form
            method="post"
            action={`/profile/header/nodes/${node.id}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
            className="flex"
            onSubmit={(event) => {
              event.preventDefault();

              fetcher.submit(
                jsonToFormData({
                  action: QuickAction.ChangeTextSize,
                  size: getNextSize(node),
                }),
                fetcherConfig
              );

              closePopover();
            }}
          >
            <input type="hidden" name="size" value={getNextSize(node)} />

            <IconButton
              type="submit"
              aria-label={`Change text size to ${getNextSize(node)}`}
            >
              <TbTextSize />
            </IconButton>
          </fetcher.Form>

          <fetcher.Form
            method="post"
            action={`/profile/header/nodes/${node.id}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
            className="flex"
            onSubmit={(event) => {
              event.preventDefault();

              fetcher.submit(
                jsonToFormData({
                  action: QuickAction.ToggleNewLine,
                }),
                fetcherConfig
              );

              closePopover();
            }}
          >
            <IconButton
              type="submit"
              aria-label={node.new_line ? 'Unset new line' : 'Set new line'}
            >
              {node.new_line ? <FaTurnUp /> : <FaTurnDown />}
            </IconButton>
          </fetcher.Form>

          {actions.canMovePrev && (
            <fetcher.Form
              method="post"
              action={`/profile/header/nodes/${node.id}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.MovePrev,
                    direction: Direction.Prev,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <input type="hidden" name="direction" value={Direction.Prev} />

              <IconButton type="submit" aria-label="Move backwards">
                <FaChevronLeft />
              </IconButton>
            </fetcher.Form>
          )}

          {actions.canMoveNext && (
            <fetcher.Form
              method="post"
              action={`/profile/header/nodes/${node.id}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.MoveNext,
                    direction: Direction.Next,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <input type="hidden" name="direction" value={Direction.Next} />

              <IconButton type="submit" aria-label="Move forwards">
                <FaChevronRight />
              </IconButton>
            </fetcher.Form>
          )}
        </div>
      )}
    </NativePopover>
  );
};
