import { eq } from 'drizzle-orm';

import { DomainError, ErrorMessages, UnexpectedError } from '@awe/core';
import { db, DbOrTx } from '~/database.server';
import { Profile } from '../../entities/Profile';
import { Header } from '../entities/Header';
import { HeaderNode, toNode, toNodes } from '../entities/HeaderNode';
import { headerNodes } from './node.table';

export const create = async (
  node: HeaderNode,
  profileId: Profile['id'],
  headerId: Header['id']
): Promise<void> => {
  const baseFields = {
    profile_id: profileId,
    header_id: headerId,
    type: node.type,
    id: node.id,

    text_size: node.text_size,
    new_line: node.new_line,
    visible: node.visible,
    order: node.order,
  } as const;

  try {
    await db.insert(headerNodes).values({
      ...baseFields,
      title: 'title' in node ? node.title : null,
      value: 'value' in node ? node.value : null,
    });
  } catch (err) {
    throw new UnexpectedError(err);
  }
};

export const createMultiple = async (
  nodes: HeaderNode[],
  headerId: Header['id'],
  profileId: Profile['id'],
  tx: DbOrTx = db
): Promise<void> => {
  try {
    await tx.insert(headerNodes).values(
      nodes.map((node) => ({
        id: node.id,
        type: node.type,
        order: node.order,
        text_size: node.text_size,
        new_line: node.new_line,
        visible: node.visible,
        title: 'title' in node ? node.title : null,
        value: 'value' in node ? node.value : null,
        profile_id: profileId,
        header_id: headerId,
      }))
    );
  } catch (error) {
    throw new UnexpectedError(error);
  }
};

export const updateOrder = async (nodes: HeaderNode[]) => {
  const updates = nodes.map((node) =>
    db
      .update(headerNodes)
      .set({ order: node.order })
      .where(eq(headerNodes.id, node.id))
  );

  try {
    await Promise.all(updates);
  } catch (err) {
    throw new UnexpectedError(err);
  }
};

export const update = async (
  id: HeaderNode['id'],
  node: HeaderNode,
  tx: DbOrTx = db
): Promise<HeaderNode> => {
  const result = await tx
    .update(headerNodes)
    .set(node)
    .where(eq(headerNodes.id, id))
    .returning();

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'HeaderNodeRepository',
      method: 'update',
      id,
    });
  }

  return node;
};

export const deleteOne = async (id: HeaderNode['id']): Promise<void> => {
  const result = await db
    .delete(headerNodes)
    .where(eq(headerNodes.id, id))
    .returning();

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'HeaderNodeRepository',
      method: 'deleteOne',
      id,
    });
  }
};

export const getById = async (id: HeaderNode['id']): Promise<HeaderNode> => {
  const result = await db
    .select()
    .from(headerNodes)
    .where(eq(headerNodes.id, id))
    .limit(1);

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'HeaderNodeRepository',
      method: 'getById',
      id,
    });
  }

  return toNode(result[0]);
};

export const findAllForHeader = async (
  headerId: Header['id']
): Promise<HeaderNode[]> => {
  const result = await db
    .select()
    .from(headerNodes)
    .where(eq(headerNodes.header_id, headerId));

  return toNodes(result);
};
