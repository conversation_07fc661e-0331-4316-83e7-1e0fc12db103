import { exhaustive } from 'exhaustive';

import { UnexpectedError } from '@awe/core';
import { Direction } from '~/utils/misc';
import { Profile } from '../../entities/Profile';
import { getProfile } from '../../profile.service';
import {
  changeOrder,
  toggleAlignment,
  toggleSpacing,
} from '../../shared:sections/Section';
import {
  getAllForProfile,
  updateSectionsOrder,
} from '../../shared:sections/section.service';
import {
  Header,
  QuickActionDto,
  SaveDto,
  update as updateEntity,
} from '../entities/Header';
import * as repo from './header.drizzle.repository';

export const handleQuickAction = async (dto: QuickActionDto) => {
  const profile = await getProfile();
  const header = await repo.getForProfile(profile.id);

  switch (dto.action) {
    case 'move-next':
    case 'move-prev': {
      const allSections = await getAllForProfile(profile.id);

      const updatedSections = changeOrder(
        allSections,
        header.id,
        dto.action === 'move-next' ? Direction.Next : Direction.Prev
      );

      await updateSectionsOrder(updatedSections);

      break;
    }
    case 'toggle-alignment':
    case 'toggle-spacing': {
      const updatedHeader = exhaustive(dto.action, {
        'toggle-alignment': () => toggleAlignment(header),
        'toggle-spacing': () => toggleSpacing(header),
      });

      await repo.update(updatedHeader);

      break;
    }
    default: {
      throw new UnexpectedError('Unknown action: ' + dto.action);
    }
  }

  return undefined;
};

export const update = async (dto: SaveDto): Promise<Header> => {
  const profile = await getProfile();
  const existingHeader = await repo.getForProfile(profile.id);
  const updatedHeader = updateEntity(existingHeader, dto);

  await repo.update(updatedHeader);

  return updatedHeader;
};

export const getWithNodes = async (
  profileId: Profile['id']
): Promise<Header<'with_nodes'>> => {
  return repo.getOneWithNodes(profileId);
};
