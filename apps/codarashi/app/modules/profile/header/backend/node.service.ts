import { exhaustive } from 'exhaustive';

import {
  DomainError,
  ErrorMessages,
  StrictExtract,
  UnexpectedError,
} from '@awe/core';
import { db, DbOrTx } from '~/database.server';
import { Profile } from '~/modules/profile/entities/Profile';
import { Direction } from '~/utils/misc';
import { getProfile, updateProfile } from '../../profile.service';
import { getSectionForType } from '../../shared:sections/section.service';
import { SectionType } from '../../shared:sections/section.shared';
import { Header, moveNode } from '../entities/Header';
import {
  canBeDeleted,
  changeTextSize,
  CreateLinkDto,
  createLinkNode,
  HeaderNode,
  HeaderNodeSaveDto,
  QuickActionDto,
  toggleLineBreak,
  toggleVisibility,
  Type,
  updateNode as updateEntity,
  valueComesFromProfile,
} from '../entities/HeaderNode';
import * as repo from './node.drizzle.repository';

export const updateNode = async (
  dto: HeaderNodeSaveDto,
  nodeId: HeaderNode['id'],
  tx: DbOrTx = db
): Promise<HeaderNode> => {
  const existingNode = await repo.getById(nodeId);
  const updatedNode = updateEntity(dto, existingNode);

  if (valueComesFromProfile(updatedNode) && 'value' in dto) {
    try {
      await tx.transaction(async (tx) => {
        await updateProfile(
          exhaustive(updatedNode.type, {
            [Type.Email]: () => {
              return { email: dto.value };
            },
            [Type.Name]: () => {
              return { name: dto.value };
            },
            [Type.GitHub]: () => {
              return { github: dto.value };
            },
            [Type.LinkedIn]: () => {
              return { linkedin: dto.value };
            },
            [Type.Title]: () => {
              return { title: dto.value };
            },
            [Type.Location]: () => {
              return { location: dto.value };
            },
          }),
          tx
        );

        await repo.update(nodeId, updatedNode, tx);
      });
    } catch (error) {
      throw new UnexpectedError(error, {
        service: 'NodeService',
        method: 'updateNode',
        dto,
        err: error,
      });
    }

    return updatedNode;
  }

  await repo.update(nodeId, updatedNode);

  return updatedNode;
};

export const deleteNode = async (nodeId: HeaderNode['id']): Promise<void> => {
  const node = await repo.getById(nodeId);

  if (canBeDeleted(node.type)) {
    return await repo.deleteOne(nodeId);
  }

  throw new DomainError(ErrorMessages.InvalidAction, {
    message: `Cannot delete node of type: ${node.type}`,
  });
};

export const createNode = async (
  dto: CreateLinkDto
): Promise<StrictExtract<HeaderNode, { type: 'link' }>> => {
  const newNode = createLinkNode(dto);
  const profile = await getProfile();
  const header = await getSectionForType(SectionType.Header, profile.id);

  await repo.create(newNode, profile.id, header.id);

  return newNode;
};

export const createMultiple = async (
  nodes: HeaderNode[],
  profileId: Profile['id'],
  headerId: Header['id'],
  tx: DbOrTx = db
) => {
  await repo.createMultiple(nodes, headerId, profileId, tx);

  return undefined;
};

const updateOrder = async (
  targetId: HeaderNode['id'],
  headerId: Header['id'],
  direction: Direction
) => {
  const nodes = await repo.findAllForHeader(headerId);
  const updatedNodes = moveNode(nodes, targetId, direction);

  await repo.updateOrder(updatedNodes);

  return updatedNodes;
};

const getById = async (id: HeaderNode['id']): Promise<HeaderNode> => {
  return await repo.getById(id);
};

export const handleQuickAction = async (
  dto: QuickActionDto,
  nodeId: HeaderNode['id']
) => {
  const profile = await getProfile();

  const updatedEntity = await exhaustive(dto.action, {
    'move-next': async () => {
      const header = await getSectionForType('header', profile.id);
      return await updateOrder(nodeId, header.id, Direction.Next);
    },
    'move-prev': async () => {
      const header = await getSectionForType('header', profile.id);
      return await updateOrder(nodeId, header.id, Direction.Prev);
    },
    'change-text-size': async () => {
      const targetNode = await getById(nodeId);

      const updatedNode = changeTextSize(targetNode);
      return await repo.update(nodeId, updatedNode);
    },
    'toggle-new-line': async () => {
      const targetNode = await getById(nodeId);

      const updatedNode = toggleLineBreak(targetNode);
      return await repo.update(nodeId, updatedNode);
    },
    'toggle-visibility': async () => {
      const targetNode = await getById(nodeId);

      const updatedNode = toggleVisibility(targetNode);
      return await repo.update(nodeId, updatedNode);
    },
  });

  return updatedEntity;
};
