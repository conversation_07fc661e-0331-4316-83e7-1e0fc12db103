import { and, asc, eq } from 'drizzle-orm';

import { ErrorMessages, UnexpectedError } from '@awe/core';
import { db } from '~/database.server';
import { Profile } from '../../entities/Profile';
import { profileSections } from '../../shared:sections/section.table';
import { Header, toHeader } from '../entities/Header';
import { headerNodes } from './node.table';

export const update = async (header: Header<'base'>): Promise<void> => {
  try {
    await db
      .update(profileSections)
      .set({
        title: header.title,
        visible: header.visible,
        gap: header.gap,
        alignment: header.alignment,
        order: header.order,
        updated_at: new Date(),
      })
      .where(eq(profileSections.id, header.id));
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'HeaderRepository',
      method: update.name,
      header,
    });
  }
};

export const getOneWithNodes = async (
  profileId: Profile['id']
): Promise<Header<'with_nodes'>> => {
  const result = await db
    .select({
      header: profileSections,
      node: headerNodes,
    })
    .from(profileSections)
    .leftJoin(headerNodes, eq(profileSections.id, headerNodes.header_id))
    .where(
      and(
        eq(profileSections.profile_id, profileId),
        eq(profileSections.type, 'header')
      )
    )
    .orderBy(asc(headerNodes.order));

  if (!result.length) {
    throw new UnexpectedError(ErrorMessages.EntityNotFound, {
      service: 'HeaderRepository',
      method: getOneWithNodes.name,
    });
  }

  return toHeader(
    {
      ...result[0].header,
      nodes: result.map((res) => res.node).filter(Boolean),
    },
    'with_nodes'
  );
};

export const getForProfile = async (
  profileId: Profile['id']
): Promise<Header> => {
  try {
    const result = await db
      .select()
      .from(profileSections)
      .where(
        and(
          eq(profileSections.profile_id, profileId),
          eq(profileSections.type, 'header')
        )
      );

    if (!result || result.length === 0) {
      throw new UnexpectedError(ErrorMessages.EntityNotFound, {
        service: 'HeaderRepository',
        method: getForProfile.name,
        profileId,
      });
    }

    return toHeader(result[0], 'base');
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'HeaderRepository',
      method: getForProfile.name,
      profileId,
      err,
    });
  }
};
