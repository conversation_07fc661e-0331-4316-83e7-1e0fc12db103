import { sql } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { Profile } from '../../entities/Profile';
import { profiles } from '../../profile.table';
import { profileSections } from '../../shared:sections/section.table';
import { Header } from '../entities/Header';
import { HeaderNode, TextSize, Type } from '../entities/HeaderNode';

export const headerNodes = sqliteTable('profile-header-nodes', {
  _id: integer('_id').primaryKey({ autoIncrement: true }),

  id: text('id').unique().notNull().$type<HeaderNode['id']>(),

  profile_id: text('profile_id')
    .notNull()
    .$type<Profile['id']>()
    .references(() => profiles.id),

  header_id: text('header_id')
    .notNull()
    .references(() => profileSections.id)
    .$type<Header['id']>(),

  type: text('type', {
    enum: [
      Type.Name,
      Type.Title,
      Type.Location,
      Type.Email,
      Type.LinkedIn,
      Type.GitHub,
      Type.WebProfile,
      Type.Link,
    ],
  })
    .notNull()
    .$type<HeaderNode['type']>(),

  new_line: integer('new_line', { mode: 'boolean' })
    .notNull()
    .$type<HeaderNode['new_line']>(),

  text_size: text('text_size', {
    enum: [TextSize.Normal, TextSize.Large, TextSize.ExtraLarge],
  })
    .notNull()
    .$type<HeaderNode['text_size']>(),

  // unstrict type for simplicity
  value: text('value'),

  visible: integer('visible', { mode: 'boolean' })
    .notNull()
    .$type<HeaderNode['visible']>(),

  order: integer('order').notNull().$type<HeaderNode['order']>(),

  // unstrict type for simplicity
  title: text('title'),

  created_at: integer('created_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updated_at: integer('updated_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});
