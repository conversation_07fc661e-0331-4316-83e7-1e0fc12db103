import { ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/react';

import { createRouteAction } from '~/utils/route-actions';
import { handleQuickAction } from './backend/node.service';
import { toId, toQuickActionDto } from './entities/HeaderNode';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params, payload }) => {
      const dto = toQuickActionDto(payload);
      const targetNodeId = toId(params.nodeId);

      const result = await handleQuickAction(dto, targetNodeId);

      return json(
        {
          status: 'success',
          data: result,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);
