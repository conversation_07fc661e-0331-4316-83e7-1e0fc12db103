import { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import {
  json,
  useActionData,
  useLoaderData,
  useOutletContext,
} from '@remix-run/react';

import { AppNavigate } from '~/components/navigation/navigate';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { toProfile } from '../entities/Profile';
import { getProfile } from '../profile.service';
import { getAllForProfile } from '../shared:tags/tags.service';
import { createNode } from './backend/node.service';
import { HeaderNodeForm } from './components/forms/node.form';
import {
  HeaderNode,
  Type as NodeType,
  toCreateLinkDto,
} from './entities/HeaderNode';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = toCreateLinkDto(payload);
      const newNode = await createNode(dto);

      return json(
        {
          status: 'success',
          data: newNode,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = (args: LoaderFunctionArgs) =>
  createRouteLoader({
    loader: async () => {
      const profile = await getProfile();
      const tags = await getAllForProfile(profile.id);

      return json({ status: 'success', tags, profile } as const);
    },
  })(args);

export default function CreateNodeRoute() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const context = useOutletContext<{ order: HeaderNode['order'] }>();

  if (loaderData.status === 'error') {
    return <div>Something went wrong.</div>;
  }

  if (actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  return (
    <HeaderNodeForm
      nodeType={NodeType.Link}
      profile={toProfile(loaderData.profile)}
      order={context.order}
      tags={loaderData.tags}
    />
  );
}
