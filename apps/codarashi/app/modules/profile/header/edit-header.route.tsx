import { ActionFunctionArgs } from '@remix-run/node';
import { json, useActionData, useOutletContext } from '@remix-run/react';

import { AppNavigate } from '~/components/navigation/navigate';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { update } from './backend/header.service';
import { HeaderForm } from './components/header.form';
import { Header, toSaveDto } from './entities/Header';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = toSaveDto(payload);

      const updatedHeader = await update(dto);

      return json(
        {
          status: 'success',
          data: updatedHeader,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = createRouteLoader({
  loader: async () => {
    return json({ status: 'success' } as const);
  },
});

export default function EditHeaderPage() {
  const actionData = useActionData<typeof action>();
  const context = useOutletContext<{ header?: Header }>();

  if (actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  if (!context?.header) {
    return <div>Header not found.</div>;
  }

  return <HeaderForm header={context.header} />;
}
