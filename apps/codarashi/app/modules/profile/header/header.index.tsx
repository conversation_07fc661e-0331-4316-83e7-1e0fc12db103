import { Outlet, useParams } from '@remix-run/react';
import { exhaustive } from 'exhaustive';
import { FC } from 'react';

import { Numbers } from '@awe/core';
import { HorizontalStack, Typography, VerticalStack } from '~/design-system';
import { useUrlNavigation } from '~/utils/navigation';
import { FormToggleLink } from '../components/form-toggle';
import { SectionTitle } from '../components/section-title';
import { VerticalDecoration } from '../components/v-decoration';
import { Profile } from '../entities/Profile';
import { Alignment, Gap } from '../shared:sections/section.shared';
import { AbstractHeaderNode } from './components/node';
import { Header } from './entities/Header';
import { HeaderNode } from './entities/HeaderNode';

type Props = {
  header: Header<'with_nodes'>;
  profile: Profile;
  isFirst?: boolean;
  isLast?: boolean;
};

export const HeaderSection: FC<Props> = ({
  header,
  isLast,
  isFirst,
  profile,
}) => {
  const params = useParams();
  const { fullPath } = useUrlNavigation();
  const isOnHeaderRoute = fullPath.includes('/header');
  const groupedNodes = useGroupedNodes(header.nodes);

  return (
    <HorizontalStack gap="sm+1" as="section">
      <VerticalDecoration />
      <VerticalStack className="grow gap-md">
        <HorizontalStack
          gap="lg"
          itemsAlign="center"
          className="px-sm"
          as="header"
        >
          <SectionTitle
            section={header}
            actions={{
              canMoveNext: !isLast,
              canMovePrev: !isFirst,
              canDelete: false,
              canToggleVisibility: false,
            }}
            accessibleName="Header section"
          >
            <Typography as="h2" size="h4" className="text-neutral font-bold">
              {header.title ? `${header.title} (Header)` : 'Header'}
            </Typography>
          </SectionTitle>

          <FormToggleLink
            to={isOnHeaderRoute ? '/profile' : '/profile/header/nodes'}
            opened={isOnHeaderRoute}
          />
        </HorizontalStack>

        {!isOnHeaderRoute && (
          <VerticalStack gap="md">
            <VerticalStack
              gap={exhaustive(header.gap, {
                [Gap.Small]: () => 'md+1',
                [Gap.Medium]: () => 'lg+1',
                [Gap.Large]: () => 'xl',
              })}
            >
              {groupedNodes.map((group, index) => {
                return (
                  <Group
                    key={group.map((node) => node.id).join(',')}
                    group={group}
                    profile={profile}
                    index={index}
                    header={header}
                    groupedNodes={groupedNodes}
                  />
                );
              })}
            </VerticalStack>
          </VerticalStack>
        )}

        {fullPath.includes('header') && (
          <Outlet
            context={{
              node: header.nodes.find((elem) => elem.id === params.nodeId),
              order: Numbers.toPositiveInteger(header.nodes.length + 1),
              header,
            }}
          />
        )}
      </VerticalStack>
    </HorizontalStack>
  );
};

const Group: FC<{
  group: Array<HeaderNode>;
  profile: Profile;
  index: number;
  header: Header;
  groupedNodes: Array<Array<HeaderNode>>;
}> = ({ group, header, index, groupedNodes, profile }) => {
  return (
    <HorizontalStack
      itemsAlign="end"
      className="flex-wrap"
      itemsJustify={header.alignment === Alignment.Center ? 'center' : 'start'}
      gap={exhaustive(header.gap, {
        [Gap.Small]: () => 'md+1',
        [Gap.Medium]: () => 'lg+1',
        [Gap.Large]: () => 'xl',
      })}
      /* used for test assertions */
      data-items-alignment={header.alignment}
      data-items-gap={header.gap}
      /* used for test assertions */
    >
      {group.map((node, groupIndex) => {
        const position = index + groupIndex;
        const isLast =
          index === groupedNodes.length - 1 && groupIndex === group.length - 1;

        return (
          <AbstractHeaderNode
            key={node.id}
            profile={profile}
            node={node}
            position={position}
            isLast={isLast}
          />
        );
      })}
    </HorizontalStack>
  );
};

const useGroupedNodes = (nodes: HeaderNode[]) => {
  return nodes.reduce(
    (acc, item) => {
      const latestGroup = acc[acc.length - 1] || [];

      if (item.new_line) {
        const newGroup = [item];
        acc.push(newGroup);
      } else {
        latestGroup.push(item);
      }

      return acc;
    },
    [[]] as Array<Array<HeaderNode>>
  );
};
