import { z } from 'zod';

import { NanoId, Numbers, StringOfLength, zodToDomainOrThrow } from '@awe/core';
import { id as tagId } from '~/modules/profile/shared:tags/Tag';
import { Direction, Spacing } from '~/utils/misc';

const id = NanoId.parser('sctn_node');

const generateId = () => NanoId.generate('sctn_node');

export const Type = {
  Paragraph: 'paragraph',
  Bullet: 'bullet',
} as const;

export type Type = (typeof Type)[keyof typeof Type];

export enum Embeds {
  Skill = '{{ skill: ',
  Tech = '{{ tech: ',
  Position = '{{ position: ',
  Industry = '{{ industry: ',
  Company = '{{ company: ',
  // possibilities: type of project, type of company, type of project
}

export const parser = z.object({
  id,
  type: z.nativeEnum(Type),
  spacing: z.nativeEnum(Spacing),
  tags: z
    .preprocess(
      (value) => (typeof value === 'string' ? [value] : value),
      z.array(tagId)
    )
    .optional(),
  value: StringOfLength.parser(1, 500),
  order: Numbers.positiveIntegerParser,
});

export const toNode = (source: unknown) => zodToDomainOrThrow(parser, source);
export type Node = z.infer<typeof parser>;

export const toId = (source: string | undefined) =>
  zodToDomainOrThrow(z.object({ id }), { id: source }).id;

const saveDto = parser.pick({
  spacing: true,
  value: true,
  tags: true,
  type: true,
  order: true,
});
export type SaveDto = z.infer<typeof saveDto>;
export const toSaveDto = (source: unknown) =>
  zodToDomainOrThrow(saveDto, source);

export const create = (dto: SaveDto, id = generateId()): Node => {
  return {
    id,
    ...dto,
  };
};

export const update = (dto: Omit<SaveDto, 'type'>, node: Node): Node => {
  return {
    ...node,
    ...dto,
    type: node.type,
  };
};

const moveParser = z.object({
  direction: z.nativeEnum(Direction),
});

export type MoveDto = z.infer<typeof moveParser>;

export const toMoveDto = (source: unknown) =>
  zodToDomainOrThrow(moveParser, source);

export const generateDefault = (
  order: Numbers.PositiveInteger,
  value = 'Lorem ipsum dolor' as Node['value']
): Node => ({
  id: generateId(),
  type: Type.Paragraph,
  value, // RICH HTML
  tags: [],
  spacing: Spacing.None,
  order,
});
