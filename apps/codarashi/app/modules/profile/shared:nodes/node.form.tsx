import { Form, useNavigation, useSubmit } from '@remix-run/react';
import { FC } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { capitalize } from '@awe/core';
import {
  HorizontalStack,
  PrimaryButton,
  RemixLink,
  Typography,
  VerticalStack,
} from '~/design-system';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { jsonToFormData } from '~/utils/form-data';
import { useFormResolver } from '~/utils/form-resolver';
import { AppSearchParams, Spacing } from '~/utils/misc';
import { Node, SaveDto, toSaveDto, Type } from './Node';

type Props = {
  node?: Node;
  tags?: Tag[];
  error?: string;
  formState?: Partial<SaveDto>;
};

// TODO: pass loader & action data as props from route components
export const NodeForm: FC<Props> = ({ node, formState, error }) => {
  const navigation = useNavigation();
  const isSubmitting = navigation.state === 'submitting';
  const submit = useSubmit();

  const { control, handleSubmit, register } = useForm<SaveDto>({
    resolver: useFormResolver(toSaveDto),
    defaultValues: {
      tags: [],
      ...node,
      ...formState,
    },
  });

  if (error) {
    // todo: errors
    return <div>{error}</div>;
  }

  return (
    <Form
      method="post"
      action={`?${AppSearchParams.RedirectTo}=/profile`}
      onSubmit={(event) => {
        handleSubmit((dto) => {
          submit(jsonToFormData(dto), { method: 'post' });
        })(event);
      }}
    >
      <VerticalStack
        gap="lg"
        className="mt-lg p-lg bg-stone-50 rounded m-auto max-w-[75%]"
      >
        <legend>
          <Typography size="h3">Summary node form</Typography>
        </legend>

        <Controller
          name="value"
          control={control}
          render={(params) => (
            <div className="flex flex-col gap-sm">
              <label htmlFor="summary_node_value">Value</label>
              <textarea
                {...params.field}
                id="summary_node_value"
                className="p-xs border-neutral-5 border border-solid rounded"
                rows={5}
                autoFocus
              />
              {params.fieldState.error?.message && (
                <div role="status" className="text-red-600">
                  {params.fieldState.error.message}
                </div>
              )}
            </div>
          )}
        />

        <fieldset>
          <legend className="mb-lg">Node type</legend>
          <div className="flex flex-col gap-md accent-primary">
            <label
              htmlFor="type_paragraph"
              className="flex gap-sm items-center cursor-pointer"
            >
              <input
                id="type_paragraph"
                className="w-[20px] h-[20px]"
                type="radio"
                {...register('type')}
                value={Type.Paragraph}
              />
              <span>Paragraph</span>
            </label>

            <label
              htmlFor="type_bullet"
              className="flex gap-sm items-center cursor-pointer"
            >
              <input
                id="type_bullet"
                className="w-[20px] h-[20px]"
                type="radio"
                {...register('type')}
                value={Type.Bullet}
              />
              <span>Bullet</span>
            </label>
          </div>
        </fieldset>

        <fieldset>
          <legend className="mb-6">Spacing</legend>

          <div className="flex flex-col gap-4">
            {Object.entries(Spacing).map(([key, value]) => (
              <label
                key={key}
                className="flex gap-2 items-center accent-primary cursor-pointer"
              >
                <input
                  className="w-[20px] h-[20px]"
                  type="radio"
                  value={value}
                  {...register('spacing')}
                />
                <span>{capitalize(key)}</span>
              </label>
            ))}
          </div>
        </fieldset>

        <HorizontalStack gap="lg" itemsJustify="end" className="items-center">
          <RemixLink to="/profile" preventScrollReset>
            Cancel
          </RemixLink>

          <PrimaryButton type="submit">
            {isSubmitting ? 'Saving...' : 'Save'}
          </PrimaryButton>
        </HorizontalStack>
      </VerticalStack>
    </Form>
  );
};
