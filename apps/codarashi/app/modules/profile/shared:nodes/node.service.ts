import { db, DbOrTx } from '~/database.server';
import { Profile } from '~/modules/profile/entities/Profile';
import { ProfileSection } from '~/modules/profile/shared:sections/Section';
import { Node, SaveDto, update as updateEntity } from './Node';
import * as repo from './node.drizzle.repository';

export const updateNode = async (
  dto: SaveDto,
  nodeId: Node['id']
): Promise<Node> => {
  const existingNode = await repo.getById(nodeId);
  const updatedNode = updateEntity(dto, existingNode);

  return await repo.update(nodeId, updatedNode);
};

export const deleteNode = async (nodeId: Node['id']): Promise<void> => {
  return await repo.deleteOne(nodeId);
};

export const createNode = async (
  node: Node,
  profileId: Profile['id'],
  sectionId: ProfileSection['id'],
  tx: DbOrTx = db
): Promise<Node> => {
  await repo.create(node, profileId, sectionId, tx);
  return node;
};

export const updateMultiple = async (nodes: Node[]): Promise<void> => {
  await repo.updateOrder(nodes);
};
