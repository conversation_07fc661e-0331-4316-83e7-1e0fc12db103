import { eq } from 'drizzle-orm';

import { DomainError, ErrorMessages, UnexpectedError } from '@awe/core';
import { db, DbOrTx } from '~/database.server';
import { Profile } from '../entities/Profile';
import { ProfileSection } from '../shared:sections/Section';
import { Node, toNode } from './Node';
import { profileNodes } from './node.table';

export const create = async (
  node: Node,
  profileId: Profile['id'],
  sectionId: ProfileSection['id'],
  tx: DbOrTx = db
): Promise<void> => {
  try {
    await tx.insert(profileNodes).values({
      ...node,
      profile_id: profileId,
      section_id: sectionId,
      tags: node.tags || [],
      created_at: new Date().toISOString(),
    });
  } catch (error) {
    throw new UnexpectedError('Failed to create node', { cause: error });
  }
};

export const updateOrder = async (nodes: Node[]) => {
  try {
    await db.transaction(async (trx) => {
      for (const node of nodes) {
        await trx
          .update(profileNodes)
          .set({ order: node.order })
          .where(eq(profileNodes.id, node.id));
      }
    });
  } catch (error) {
    throw new UnexpectedError('Failed to update order', { cause: error });
  }
};

export const update = async (
  id: Node['id'],
  updatedNode: Node
): Promise<Node> => {
  try {
    await db
      .update(profileNodes)
      .set({
        spacing: updatedNode.spacing,
        value: updatedNode.value,
        tags: updatedNode.tags,
        updated_at: new Date().toISOString(),
      })
      .where(eq(profileNodes.id, id));

    return updatedNode;
  } catch (error) {
    throw new UnexpectedError('Failed to update node', { cause: error });
  }
};

export const deleteOne = async (id: Node['id']): Promise<void> => {
  try {
    await db.delete(profileNodes).where(eq(profileNodes.id, id));
  } catch (error) {
    throw new UnexpectedError('Failed to delete node', { cause: error });
  }
};

export const getById = async (id: Node['id']): Promise<Node> => {
  try {
    const node = await db
      .select()
      .from(profileNodes)
      .where(eq(profileNodes.id, id))
      .limit(1);

    if (!node.length) {
      throw new DomainError(ErrorMessages.EntityNotFound, {
        service: 'NodeRepository',
        method: 'getById',
        id,
      });
    }

    return toNode(node[0]);
  } catch (error) {
    throw new UnexpectedError('Failed to find node by ID', { cause: error });
  }
};
