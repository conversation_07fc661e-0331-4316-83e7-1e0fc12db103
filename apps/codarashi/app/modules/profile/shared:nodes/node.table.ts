import { sql } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { Spacing } from '~/utils/misc';
import { Profile } from '../entities/Profile';
import { profiles } from '../profile.table';
import { BaseSection } from '../shared:sections/section.shared';
import { profileSections } from '../shared:sections/section.table';
import { Node, Type } from './Node';

export const profileNodes = sqliteTable('profile-section-nodes', {
  _id: integer('_id').primaryKey({ autoIncrement: true }),

  id: text('id').unique().notNull().$type<Node['id']>(),
  profile_id: text('profile_id')
    .notNull()
    .$type<Profile['id']>()
    .references(() => profiles.id),
  section_id: text('section_id')
    .notNull()
    .$type<BaseSection['id']>()
    .references(() => profileSections.id),

  type: text('type', { enum: [Type.Paragraph, Type.Bullet] })
    .$type<Type>()
    .notNull(),

  spacing: text('spacing', {
    enum: [Spacing.None, Spacing.Small, Spacing.Large, Spacing.Medium],
  })
    .$type<Spacing>()
    .notNull(),
  value: text('value').notNull(),
  tags: text('tags', { mode: 'json' }).$type<string[]>().notNull(),

  order: integer('order').notNull().$type<Node['order']>(),

  created_at: text('created_at')
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$type<string>(),
  updated_at: text('updated_at')
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$type<string>(),
});
