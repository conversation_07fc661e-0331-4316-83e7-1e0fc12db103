import { z } from 'zod';

import { NanoId, zodToDomainOrThrow } from '@awe/core';
import { id as userId } from '~/domain-entities/User';
import { sectionBase } from '../shared:sections/section.shared';
import {
  profileEmail,
  profileGithub,
  profileLinkedin,
  profileLocation,
  profileName,
  profileTitle,
} from './profile-fields';

export const id = NanoId.parser('prf');

export const generateId = () => NanoId.generate('prf');

type Variants = 'base' | 'with_sections';

const baseParser = z.object({
  id,
  user_id: userId,

  name: profileName,
  email: profileEmail,
  title: profileTitle,
  location: profileLocation,
  linkedin: profileLinkedin,
  github: profileGithub,
});

const parserWithSections = baseParser.extend({
  sections: z
    .array(
      sectionBase.pick({
        id: true,
        order: true,
        type: true,
      })
    )
    .transform((data) => data.sort((a, b) => a.order - b.order)),
});

type BaseProfile = z.infer<typeof baseParser>;
type ProfileWithSections = z.infer<typeof parserWithSections>;

export type Profile<T extends Variants = 'base'> = T extends 'base'
  ? BaseProfile
  : ProfileWithSections;

export type ProfileId = z.infer<typeof id>;

const createDto = baseParser.pick({
  user_id: true,
  email: true,
});
export type CreateDto = z.infer<typeof createDto>;

export const create = (
  dto: CreateDto,
  id = NanoId.generate('prf')
): Profile => {
  return {
    id,
    name: profileName.parse('John Doe'),
    title: profileTitle.parse('Software Engineer'),
    location: profileLocation.parse('Tokyo, Japan'),
    linkedin: profileLinkedin.parse('https://linkedin.com'),
    github: profileGithub.parse('https://github.com'),

    email: dto.email,
    user_id: dto.user_id,
  };
};

export const toProfile = <T extends Variants = 'base'>(
  source: unknown,
  variant: T = 'base' as T
): Profile<T> =>
  variant === 'base'
    ? (zodToDomainOrThrow(baseParser, source) as Profile<T>)
    : (zodToDomainOrThrow(parserWithSections, source) as Profile<T>);

const _saveDto = baseParser
  .pick({
    name: true,
    title: true,
    location: true,
    linkedin: true,
    github: true,
    email: true,
  })
  .partial();

export type SaveDto = z.infer<typeof _saveDto>;

const saveDto = _saveDto
  .refine((data) => Object.keys(data).length > 0, {
    message: 'No data provided',
    path: ['*'],
  })
  .transform((data) => data as SaveDto);

export const toSaveDto = (source: unknown): SaveDto =>
  zodToDomainOrThrow(saveDto, source);

export const update = (dto: SaveDto, profile: Profile): Profile => {
  return {
    ...profile,
    ...dto,
  };
};
