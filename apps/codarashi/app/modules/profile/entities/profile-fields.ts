import { CustomUrl, EmailAddress, StringOfLength } from '@awe/core';

export const profileName = StringOfLength.parser(1, 100, 'profile_name');
export const profileEmail = EmailAddress.parser;
export const profileTitle = StringOfLength.parser(1, 100, 'profile_title');

export const profileLocation = StringOfLength.parser(
  1,
  100,
  'profile_location'
);
export const profileLinkedin = CustomUrl.parser('linkedin_url');
export const profileGithub = CustomUrl.parser('github_url');
