import {
  FC,
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';

import { Profile } from '../entities/Profile';

type Props = PropsWithChildren<{
  profile: Profile;
}>;

type StoreInterface = {
  profile: Profile;
  updateProfile: (newProfile: Profile) => void;
};

const StoreContext = createContext<StoreInterface | null>(null);

export const ProfileStore: FC<Props> = ({ children, profile }) => {
  const [localProfile, setProfile] = useState(profile);

  useEffect(() => {
    setProfile(profile);
  }, [profile]);

  return (
    <StoreContext.Provider
      value={{ profile: localProfile, updateProfile: setProfile }}
    >
      {children}
    </StoreContext.Provider>
  );
};

export const useProfileStore = () => {
  const currentValue = useContext(StoreContext);

  if (!currentValue) {
    throw new Error('useProfileStore must be used within a ProfileStore');
  }

  return currentValue;
};
