import { FC, PropsWithChildren } from 'react';

import { HorizontalStack, VerticalStack } from '~/design-system';
import { MainNavigation } from '~/components/navigation/navigation';
import { SideNavigation } from './side-navigation';

export const ProfileLayout: FC<PropsWithChildren> = ({ children }) => {
  return (
    <VerticalStack gap="2xl" className="h-screen">
      <MainNavigation />

      <HorizontalStack as="main" gap="2xl" className="grow p-4">
        <SideNavigation />
        <HorizontalStack className="grow">{children}</HorizontalStack>
      </HorizontalStack>
    </VerticalStack>
  );
};
