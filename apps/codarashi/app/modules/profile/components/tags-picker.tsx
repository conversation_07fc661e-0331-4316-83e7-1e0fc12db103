import { FC } from 'react';

import { Tag } from '~/modules/profile/shared:tags/Tag';

type Props = {
  value: Array<Tag['id']>;
  tags: Tag[];
  onChange: React.ChangeEventHandler<HTMLInputElement>;
};

export const TagsPicker: FC<Props> = ({ value, tags, onChange }) => {
  return (
    <fieldset className="flex flex-col gap-sm">
      <legend className="mb-sm">Tags</legend>

      {tags.map((tag) => {
        return (
          <label
            className="flex items-center gap-sm cursor-pointer"
            key={tag.id}
          >
            <input
              type="checkbox"
              name="tags"
              value={tag.id}
              checked={value.includes(tag.id)}
              onChange={onChange}
              className="accent-primary w-[20px] h-[20px] rounded-md cursor-pointer"
            />
            <span>{tag.name}</span>
          </label>
        );
      })}
    </fieldset>
  );
};
