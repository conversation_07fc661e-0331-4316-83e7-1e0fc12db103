import { FC } from 'react';
import { FaPlus } from 'react-icons/fa6';

import { IconButton, IconLink } from '~/design-system';
import classNames from '~/utils/css';

type Props = {
  onClick: () => void;
  opened?: boolean;
};

export const FormToggleButton: FC<Props> = ({ onClick, opened }) => {
  return (
    <IconButton
      className={classNames(
        'rounded-full bg-secondary hover:!bg-secondary+2 !outline-neutral+2 outline-offset-2',
        {
          'rotate-45': !!opened,
        }
      )}
      onClick={onClick}
    >
      <FaPlus className="text-on-primary hover:scale-125" />
    </IconButton>
  );
};

type LinkProps = {
  to: string;
  opened?: boolean;
};

export const FormToggleLink: FC<LinkProps> = ({ to, opened }) => {
  return (
    <IconLink
      className={classNames(
        'rounded-full bg-secondary hover:!bg-secondary+2 !outline-neutral+2 outline-offset-2',
        {
          'rotate-45': !!opened,
        }
      )}
      to={to}
      preventScrollReset={true}
    >
      <FaPlus className="text-on-primary hover:scale-125" />
    </IconLink>
  );
};
