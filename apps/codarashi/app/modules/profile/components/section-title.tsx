import { useFetcher } from '@remix-run/react';
import { FC, ReactNode } from 'react';
import {
  FaAlignCenter,
  FaAlignLeft,
  FaChevronDown,
  FaChevronUp,
  FaEye,
  FaEyeSlash,
  FaPencil,
  FaTrash,
} from 'react-icons/fa6';
import { RxColumnSpacing } from 'react-icons/rx';

import { IconButton, IconLink } from '~/design-system';
import { NativePopover } from '~/design-system/NativePopover';
import classNames from '~/utils/css';
import { jsonToFormData } from '~/utils/form-data';
import { AppSearchParams, Direction } from '~/utils/misc';
import { ProfileSection } from '../shared:sections/Section';
import { QuickAction } from '../shared:sections/section.shared';

type Props = {
  children: ReactNode;
  section: ProfileSection;
  accessibleName?: string;

  actions: {
    canMovePrev: boolean;
    canMoveNext: boolean;
    canDelete: boolean;
    canToggleVisibility: boolean;
  };
};

export const SectionTitle: FC<Props> = ({
  children,
  section,
  actions,
  accessibleName,
}) => {
  const fetcher = useFetcher();
  const fetcherConfig = {
    method: 'post',
    action: `/profile/${section.type}/quick-edit`,
  } as const;

  const editLink = `/profile/${section.type}/edit`;

  return (
    <NativePopover
      renderTrigger={(props) => (
        <button type="button" aria-label={accessibleName} {...props}>
          {children}
        </button>
      )}
      uniqueId={section.id}
    >
      {({ close: closePopover }) => (
        <div className="flex items-center gap-md p-sm border-neutral-5 border bg-white rounded-md shadow-md">
          <IconLink
            role="menuitem"
            aria-label="Edit"
            to={editLink}
            preventScrollReset
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            <FaPencil
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </IconLink>

          {actions.canDelete && (
            <IconLink
              role="menuitem"
              aria-label="Delete section"
              to={`/profile/${section.type}/delete`}
              preventScrollReset
              onClick={() => {
                closePopover();
                return true;
              }}
            >
              <FaTrash
                className={classNames('text-neutral+3', {
                  'text-primary': false,
                })}
              />
            </IconLink>
          )}

          {'alignment' in section && (
            <fetcher.Form
              method="post"
              action={`/profile/${section.type}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.ToggleAlignment,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <IconButton
                role="menuitem"
                type="submit"
                aria-label={`Change alignment to ${
                  section.alignment === 'center' ? 'left' : 'center'
                }`}
              >
                {section.alignment === 'center' ? (
                  <FaAlignLeft />
                ) : (
                  <FaAlignCenter />
                )}
              </IconButton>
            </fetcher.Form>
          )}

          {'gap' in section && (
            <fetcher.Form
              method="post"
              action={`/profile/${section.type}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.ToggleSpacing,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <IconButton
                role="menuitem"
                type="submit"
                aria-label="Adjust spacing"
              >
                <RxColumnSpacing />
              </IconButton>
            </fetcher.Form>
          )}

          {actions.canToggleVisibility && (
            <fetcher.Form
              method="post"
              action={`/profile/${section.type}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.ToggleVisibility,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <IconButton
                role="menuitem"
                type="submit"
                aria-label={section.visible ? 'Hide section' : 'Show section'}
              >
                {section.visible ? <FaEyeSlash /> : <FaEye />}
              </IconButton>
            </fetcher.Form>
          )}

          {actions.canMovePrev && (
            <fetcher.Form
              method="post"
              action={`/profile/${section.type}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.MovePrev,
                    direction: Direction.Prev,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <input type="hidden" name="direction" value={Direction.Prev} />

              <IconButton
                role="menuitem"
                type="submit"
                aria-label="Move upwards"
              >
                <FaChevronUp />
              </IconButton>
            </fetcher.Form>
          )}

          {actions.canMoveNext && (
            <fetcher.Form
              method="post"
              action={`/profile/${section.type}/quick-edit?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    action: QuickAction.MoveNext,
                    direction: Direction.Next,
                  }),
                  fetcherConfig
                );

                closePopover();
              }}
            >
              <input type="hidden" name="direction" value={Direction.Next} />

              <IconButton
                role="menuitem"
                type="submit"
                aria-label="Move downwards"
              >
                <FaChevronDown />
              </IconButton>
            </fetcher.Form>
          )}
        </div>
      )}
    </NativePopover>
  );
};
