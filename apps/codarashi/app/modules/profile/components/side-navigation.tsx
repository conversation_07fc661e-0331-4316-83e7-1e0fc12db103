import { FC } from 'react';

import { RemixLink, Typography, VerticalStack } from '~/design-system';

export const SideNavigation: FC = () => {
  return (
    <VerticalStack gap="lg">
      <Typography
        size="h4"
        as="h2"
        className="mb-md border-b border-solid border-neutral-5 pb-sm"
      >
        Display
      </Typography>
      <RemixLink to="/profile#header" className="no-underline">
        Header
      </RemixLink>
      <RemixLink to="/profile#summary" className="no-underline">
        Summary
      </RemixLink>
      <RemixLink to="/profile#top-skills" className="no-underline">
        Top skills
      </RemixLink>
      <RemixLink to="/profile#top-tech" className="no-underline">
        Top tech
      </RemixLink>
      <RemixLink to="/profile#work-history" className="no-underline">
        Work history
      </RemixLink>
      <RemixLink to="/profile#education" className="no-underline">
        Education
      </RemixLink>
      <RemixLink to="/profile#languages" className="no-underline">
        Languages
      </RemixLink>
      <RemixLink to="/profile#certifications" className="no-underline">
        Certifications
      </RemixLink>

      <div className="mt-md gap-md+1 flex flex-col">
        <Typography
          size="h4"
          as="h2"
          className="border-b border-solid border-neutral-5 pb-sm"
        >
          Internal
        </Typography>
        <RemixLink className="no-underline" to="/profile/settings/tags">
          Tags
        </RemixLink>
      </div>
    </VerticalStack>
  );
};
