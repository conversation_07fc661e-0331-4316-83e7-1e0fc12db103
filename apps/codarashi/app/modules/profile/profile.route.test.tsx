import { createRemixStub } from '@remix-run/testing';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import * as HeaderQuickEditRoute from './header/quick-edit-header.route';
import * as ProfileRoute from './profile.route';
import * as SummaryQuickEditRoute from './summary/quick-edit-summary.route';

describe('ProfileRoute', () => {
  it('renders all sections', async () => {
    const ProfileRouteStub = createRemixStub([
      {
        path: '/profile',
        Component: () => <ProfileRoute.default />,
        action: ProfileRoute.action,
        loader: ProfileRoute.loader,
      },
    ]);

    render(<ProfileRouteStub initialEntries={['/profile']} />);

    expect(
      await screen.findByRole('heading', { name: 'Header', level: 2 })
    ).toBeInTheDocument();

    expect(
      await screen.findByRole('heading', { name: 'Summary', level: 2 })
    ).toBeInTheDocument();

    expect(await screen.findAllByRole('region')).toHaveLength(2);
  });

  it('can move a section down', async () => {
    const ProfileRouteStub = createRemixStub([
      {
        path: '/profile',
        Component: () => <ProfileRoute.default />,
        action: ProfileRoute.action,
        loader: ProfileRoute.loader,
      },
      {
        path: '/profile/header/quick-edit',
        action: HeaderQuickEditRoute.action,
      },
    ]);

    render(<ProfileRouteStub initialEntries={['/profile']} />);

    expect(await screen.findAllByRole('region')).toHaveLength(2);

    const sections = screen.getAllByRole('region');
    expect(
      within(sections[0]).getByRole('heading', { name: 'Header', level: 2 })
    ).toBeInTheDocument();
    expect(
      within(sections[1]).getByRole('heading', { name: 'Summary', level: 2 })
    ).toBeInTheDocument();

    const headerActions = screen.getByRole('button', {
      name: 'Header section',
    });

    await userEvent.click(headerActions);

    const menu = await screen.findByRole('menu', { name: 'Header section' });

    const moveDownwardsButton = within(menu).getByRole('menuitem', {
      name: 'Move downwards',
    });

    await userEvent.click(moveDownwardsButton);

    await waitFor(
      async () => {
        const updatedSections = await screen.findAllByRole('region');

        expect(
          within(updatedSections[0]).getByRole('heading', {
            name: 'Summary',
            level: 2,
          })
        ).toBeInTheDocument();
        expect(
          within(updatedSections[1]).getByRole('heading', {
            name: 'Header',
            level: 2,
          })
        ).toBeInTheDocument();
      },
      {
        timeout: 5000,
      }
    );
  });

  it('can move summary section up', async () => {
    const ProfileRouteStub = createRemixStub([
      {
        path: '/profile',
        Component: () => <ProfileRoute.default />,
        action: ProfileRoute.action,
        loader: ProfileRoute.loader,
      },
      {
        path: '/profile/summary/quick-edit',
        action: SummaryQuickEditRoute.action,
      },
    ]);

    render(<ProfileRouteStub initialEntries={['/profile']} />);

    expect(await screen.findAllByRole('region')).toHaveLength(2);

    const sections = screen.getAllByRole('region');
    expect(
      within(sections[0]).getByRole('heading', { name: 'Header', level: 2 })
    ).toBeInTheDocument();
    expect(
      within(sections[1]).getByRole('heading', { name: 'Summary', level: 2 })
    ).toBeInTheDocument();

    const summaryActions = screen.getByRole('button', {
      name: 'Summary section',
    });

    await userEvent.click(summaryActions);

    const menu = await screen.findByRole('menu', { name: 'Summary section' });

    const moveUpwardsButton = within(menu).getByRole('menuitem', {
      name: 'Move upwards',
    });

    await userEvent.click(moveUpwardsButton);

    await waitFor(
      async () => {
        const updatedSections = await screen.findAllByRole('region');

        expect(
          within(updatedSections[0]).getByRole('heading', {
            name: 'Summary',
            level: 2,
          })
        ).toBeInTheDocument();
        expect(
          within(updatedSections[1]).getByRole('heading', {
            name: 'Header',
            level: 2,
          })
        ).toBeInTheDocument();
      },
      {
        timeout: 5000,
      }
    );
  });
});
