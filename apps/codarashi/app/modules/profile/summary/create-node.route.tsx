import { ActionFunctionArgs } from '@remix-run/node';
import { json, useActionData, useLoaderData } from '@remix-run/react';

import { AppNavigate } from '~/components/navigation/navigate';
import { getProfile } from '~/modules/profile/profile.service';
import {
  create as createNodeEntity,
  toSaveDto,
} from '~/modules/profile/shared:nodes/Node';
import { createNode } from '~/modules/profile/shared:nodes/node.service';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { NodeForm } from '../shared:nodes/node.form';
import { getSectionForType } from '../shared:sections/section.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = toSaveDto(payload);
      const profile = await getProfile();

      const summarySection = await getSectionForType('summary', profile.id);

      const newNode = createNodeEntity(dto);
      await createNode(newNode, profile.id, summarySection.id);

      return json(
        {
          status: 'success',
          data: newNode,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = createRouteLoader({
  loader: async () => {
    return json({ status: 'success', tags: [] as Tag[] } as const);
  },
});

export default function CreateNodeRoute() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();

  if (loaderData.status === 'error') {
    return <div>Something went wrong.</div>;
  }

  if (actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  return (
    <NodeForm
      tags={loaderData.tags}
      error={
        actionData?.status === 'error' ? 'Something went wrong' : undefined
      }
    />
  );
}
