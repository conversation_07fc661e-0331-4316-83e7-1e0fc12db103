import { ActionFunctionArgs } from '@remix-run/node';
import { Form, json, useActionData, useOutletContext } from '@remix-run/react';
import { FC } from 'react';

import { AppNavigate } from '~/components/navigation/navigate';
import { RemixLink } from '~/design-system';
import { Node, toId } from '~/modules/profile/shared:nodes/Node';
import { deleteNode } from '~/modules/profile/shared:nodes/node.service';
import classNames from '~/utils/css';
import { createRouteAction } from '~/utils/route-actions';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params }) => {
      const nodeId = toId(params.nodeId);

      await deleteNode(nodeId);

      return json({
        status: 'success',
      } as const);
    },
  })(args);

const DeleteSummaryNode: FC = () => {
  const context = useOutletContext<{ node: Node } | undefined>();
  const actionData = useActionData<typeof action>();

  if (context?.node === undefined || actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  return (
    <dialog
      open
      className={classNames({
        'w-[400px] h-[400px] flex flex-col': true,
        'bg-white border border-gray-100 rounded-lg shadow-lg p-6': true,
      })}
    >
      <Form method="post">
        <input type="hidden" name="id" value={context?.node?.id} />

        <div className="flex-grow">Confirm delete?</div>

        <footer className="flex justify-end gap-lg items-center">
          <button>Delete</button>
          <RemixLink to="/profile">Cancel</RemixLink>
        </footer>
      </Form>
    </dialog>
  );
};

export default DeleteSummaryNode;
