import { Form, useFetcher } from '@remix-run/react';
import { FC, ReactNode } from 'react';
import { FaChevronDown, FaChevronUp, FaPencil, FaTrash } from 'react-icons/fa6';
import { RemixLink } from '~/design-system';

import { NativePopover } from '~/design-system/NativePopover';
import { Node } from '~/modules/profile/shared:nodes/Node';
import classNames from '~/utils/css';
import { jsonToFormData } from '~/utils/form-data';
import { AppSearchParams, Direction } from '~/utils/misc';

type Props = {
  nodeId: Node['id'];
  label: ReactNode;
  canMove: { prev: boolean; next: boolean };
};

export const NodeContextActions: FC<Props> = ({ nodeId, label, canMove }) => {
  const fetcher = useFetcher();

  return (
    <NativePopover
      renderTrigger={(props) => (
        <button type="button" {...props}>
          {label}
        </button>
      )}
      uniqueId={nodeId}
    >
      {({ close: closePopover }) => (
        <div className="flex items-center gap-md p-sm border-neutral-5 border bg-white rounded-md shadow-md">
          <RemixLink
            to={`/profile/summary/nodes/${nodeId}/edit`}
            preventScrollReset
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            <FaPencil
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </RemixLink>

          <RemixLink
            to={`/profile/summary/nodes/${nodeId}/delete`}
            preventScrollReset
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            {' '}
            <FaTrash
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </RemixLink>

          {canMove.next && (
            <Form
              method="post"
              action={`/profile/summary/nodes/${nodeId}/move?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    direction: Direction.Next,
                    id: nodeId,
                  }),
                  {
                    method: 'post',
                    action: `/profile/summary/nodes/${nodeId}/move`,
                  }
                );

                closePopover();
              }}
            >
              <input type="hidden" name="direction" value={Direction.Next} />

              <button type="submit">
                <FaChevronDown
                  className={classNames('text-neutral+3', {
                    'text-primary': false,
                  })}
                />
              </button>
            </Form>
          )}

          {canMove.prev && (
            <Form
              method="post"
              action={`/profile/summary/nodes/${nodeId}/move?${AppSearchParams.RedirectTo}=/profile`}
              className="flex"
              onSubmit={(event) => {
                event.preventDefault();

                fetcher.submit(
                  jsonToFormData({
                    direction: Direction.Prev,
                    id: nodeId,
                  }),
                  {
                    method: 'post',
                    action: `/profile/summary/nodes/${nodeId}/move`,
                  }
                );

                closePopover();
              }}
            >
              <input type="hidden" name="id" value={nodeId} />
              <input type="hidden" name="direction" value={Direction.Prev} />

              <button type="submit">
                <FaChevronUp
                  className={classNames('text-neutral+3', {
                    'text-primary': false,
                  })}
                />
              </button>
            </Form>
          )}
        </div>
      )}
    </NativePopover>
  );
};
