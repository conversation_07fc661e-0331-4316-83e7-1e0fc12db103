import { z } from 'zod';

import {
  DomainError,
  ErrorMessages,
  Numbers,
  StringOfLength,
  moveArrayItem,
  zodToDomainOrThrow,
} from '@awe/core';

import {
  Node,
  generateDefault,
  parser as nodeParser,
} from '~/modules/profile/shared:nodes/Node';

import { Direction } from '~/utils/misc';
import {
  Alignment,
  Gap,
  SectionType,
  generateSectionId,
  sectionBase,
} from '../../shared:sections/section.shared';

export const parser = {
  withNodes: sectionBase.merge(
    z.object({
      type: z.literal(SectionType.Summary),
      nodes: z.array(nodeParser),
    })
  ),
  base: sectionBase.merge(
    z.object({
      type: z.literal(SectionType.Summary),
    })
  ),
};

type SummaryWithNodes = z.infer<typeof parser.withNodes>;
type BaseSummary = z.infer<typeof parser.base>;

type Variants = 'base' | 'with_nodes';

export type Summary<T extends Variants = 'base'> = T extends 'base'
  ? BaseSummary
  : SummaryWithNodes;

export const update = (
  summary: Summary<'base'>,
  dto: SaveDto
): Summary<'base'> => {
  return {
    ...summary,
    alignment: dto.alignment,
    gap: dto.gap,
    title: dto.title,
    visible: dto.visible,
  };
};

export function toSummary(
  source: unknown,
  variant: 'with_nodes'
): Summary<'with_nodes'>;
export function toSummary(source: unknown, variant: 'base'): Summary<'base'>;
export function toSummary<T extends Variants>(
  source: unknown,
  variant: T
): Summary<T> {
  return (
    variant === 'base'
      ? zodToDomainOrThrow(parser.base, source)
      : zodToDomainOrThrow(parser.withNodes, source)
  ) as Summary<T>;
}

const saveDtoParser = parser.base
  .pick({
    title: true,
    visible: true,
    alignment: true,
    gap: true,
  })
  .brand('Profile_Summary_SaveDto');

export const toSaveDto = (source: unknown) =>
  zodToDomainOrThrow(saveDtoParser, source);

export type SaveDto = z.infer<typeof saveDtoParser>;

export const createDefaults = (
  order: Numbers.PositiveInteger,
  id = generateSectionId()
): Summary<'with_nodes'> => {
  const defaultNode = generateDefault(Numbers.toPositiveInteger(1));

  return {
    id,
    type: SectionType.Summary,
    visible: true,
    title: StringOfLength.generate('', 0, 50),
    alignment: Alignment.Center,
    gap: Gap.Small,
    nodes: [defaultNode],
    order,
  };
};

export const moveNode = (
  summary: Summary<'with_nodes'>,
  nodeId: Node['id'],
  direction: Direction
): Summary<'with_nodes'> => {
  const index = summary.nodes.findIndex((node) => node.id === nodeId);

  if (index === -1) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      message: 'Node not found: id: ' + nodeId,
    });
  }

  return {
    ...summary,
    nodes: moveArrayItem(
      summary.nodes,
      index,
      direction === 'next' ? index + 1 : index - 1
    ).map((elem, index) => ({
      ...elem,
      order: Numbers.toPositiveInteger(index + 1),
    })),
  };
};
