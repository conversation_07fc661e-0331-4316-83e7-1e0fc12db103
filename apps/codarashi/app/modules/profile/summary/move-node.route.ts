import { ActionFunctionArgs, json } from '@remix-run/node';

import { toId, toMoveDto } from '~/modules/profile/shared:nodes/Node';
import { createRouteAction } from '~/utils/route-actions';
import { updateNodesOrder } from './backend/summary.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params, payload }) => {
      const dto = toMoveDto(payload);
      const nodeId = toId(params.nodeId);

      const updatedSummary = await updateNodesOrder(nodeId, dto);

      return json({ status: 'success', data: updatedSummary }, {
        status: 201,
      } as const);
    },
  })(args);
