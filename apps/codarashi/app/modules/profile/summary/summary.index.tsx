import { Outlet, useParams } from '@remix-run/react';
import { FC } from 'react';

import { HorizontalStack, Typography, VerticalStack } from '~/design-system';
import cn from '~/utils/css';
import { useUrlNavigation } from '~/utils/navigation';
import { FormToggleLink } from '../components/form-toggle';
import { SectionTitle } from '../components/section-title';
import { VerticalDecoration } from '../components/v-decoration';
import { NodeContextActions } from './context-actions';
import { Summary } from './entities/Summary';

type Props = {
  summary: Summary<'with_nodes'>;
  isFirst: boolean;
  isLast: boolean;
};

export const SummarySection: FC<Props> = ({ summary, isFirst, isLast }) => {
  const { fullPath } = useUrlNavigation();
  const params = useParams();

  const isFormOpen =
    fullPath.endsWith('summary/nodes') || fullPath.endsWith('/edit');

  return (
    <HorizontalStack gap="sm+1" as="section">
      <VerticalDecoration />
      <VerticalStack className="grow gap-md">
        <HorizontalStack
          gap="lg"
          itemsAlign="center"
          className="px-sm"
          as="header"
        >
          <SectionTitle
            section={summary}
            actions={{
              canMoveNext: !isLast,
              canMovePrev: !isFirst,
              canDelete: false,
              canToggleVisibility: true,
            }}
            accessibleName="Summary section"
          >
            <Typography
              as="h2"
              size="h4"
              className={cn('text-neutral font-bold', {
                'line-through': summary.visible === false,
              })}
              data-section-visible={summary.visible}
            >
              {summary.title ? `${summary.title} (Summary)` : 'Summary'}
            </Typography>
          </SectionTitle>

          <FormToggleLink
            to={isFormOpen ? '/profile' : '/profile/summary/nodes'}
            opened={isFormOpen}
          />
        </HorizontalStack>

        {!isFormOpen && (
          <VerticalStack
            gap="md"
            itemsAlign={summary.alignment === 'center' ? 'center' : 'start'}
            data-items-alignment={summary.alignment}
            data-items-gap={summary.gap}
          >
            {summary.nodes.map((node, index) => (
              <div
                key={node.id}
                className="p-4 border border-solid border-stone-50 relative"
              >
                <NodeContextActions
                  nodeId={node.id}
                  label={node.value}
                  canMove={{
                    prev: index !== 0,
                    next: index !== summary.nodes.length - 1,
                  }}
                />
              </div>
            ))}
          </VerticalStack>
        )}

        {fullPath.includes('summary') && (
          <Outlet
            context={{
              node: summary.nodes.find((elem) => elem.id === params.nodeId),
              summary,
            }}
          />
        )}
      </VerticalStack>
    </HorizontalStack>
  );
};
