import { and, asc, eq } from 'drizzle-orm';

import { DomainError, ErrorMessages, UnexpectedError } from '@awe/core';
import { db } from '~/database.server';
import { Profile } from '../../entities/Profile';
import { profileNodes } from '../../shared:nodes/node.table';
import { profileSections } from '../../shared:sections/section.table';
import { Summary, toSummary } from '../entities/Summary';

export const update = async (summary: Summary<'base'>): Promise<void> => {
  try {
    await db
      .update(profileSections)
      .set({
        title: summary.title,
        visible: summary.visible,
        gap: summary.gap,
        alignment: summary.alignment,
        order: summary.order,
        updated_at: new Date(),
      })
      .where(eq(profileSections.id, summary.id));
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'SummaryRepository',
      method: update.name,
      summary,
    });
  }
};

export const getOneWithNodes = async (
  profileId: Profile['id']
): Promise<Summary<'with_nodes'>> => {
  const result = await db
    .select({
      summary: profileSections,
      node: profileNodes,
    })
    .from(profileSections)
    .leftJoin(profileNodes, eq(profileSections.id, profileNodes.section_id))
    .where(
      and(
        eq(profileSections.profile_id, profileId),
        eq(profileSections.type, 'summary')
      )
    )
    .orderBy(asc(profileNodes.order));

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'SummaryRepository',
      method: getOneWithNodes.name,
    });
  }

  return toSummary(
    {
      ...result[0].summary,
      nodes: result.map((res) => res.node).filter(Boolean),
    },
    'with_nodes'
  );
};

export const getForProfile = async (
  profileId: Profile['id']
): Promise<Summary<'base'>> => {
  try {
    const [result] = await db
      .select()
      .from(profileSections)
      .where(
        and(
          eq(profileSections.profile_id, profileId),
          eq(profileSections.type, 'summary')
        )
      );

    if (!result) {
      throw new UnexpectedError('Entity not found', {
        message: `Section not found: type: summary`,
      });
    }

    return toSummary(result, 'base');
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'SummaryRepository',
      method: getForProfile.name,
      profileId,
    });
  }
};
