import { exhaustive } from 'exhaustive';

import { UnexpectedError } from '@awe/core';
import { MoveDto, Node } from '~/modules/profile/shared:nodes/Node';
import { updateMultiple as updateMultipleNodes } from '~/modules/profile/shared:nodes/node.service';
import { Direction } from '~/utils/misc';
import { Profile } from '../../entities/Profile';
import { getProfile } from '../../profile.service';
import {
  changeOrder,
  QuickActionDto,
  toggleAlignment,
  toggleSpacing,
  toggleVisibility,
} from '../../shared:sections/Section';
import {
  getAllForProfile,
  updateSectionsOrder,
} from '../../shared:sections/section.service';
import {
  moveNode,
  SaveDto,
  Summary,
  update as updateEntity,
} from '../entities/Summary';
import * as repo from './summary.drizzle.repository';

export const updateNodesOrder = async (
  nodeId: Node['id'],
  dto: MoveDto
): Promise<Summary<'with_nodes'>> => {
  const profile = await getProfile();
  const summary = await repo.getOneWithNodes(profile.id);
  const updatedSummary = moveNode(summary, nodeId, dto.direction);

  await updateMultipleNodes(updatedSummary.nodes);

  return updatedSummary;
};

export const update = async (dto: SaveDto): Promise<Summary<'base'>> => {
  const profile = await getProfile();
  const summary = await repo.getOneWithNodes(profile.id);
  const updatedSummary = updateEntity(summary, dto);

  await repo.update(updatedSummary);

  return updatedSummary;
};

export const handleQuickAction = async (dto: QuickActionDto) => {
  const profile = await getProfile();
  const summary = await repo.getForProfile(profile.id);

  switch (dto.action) {
    case 'move-next':
    case 'move-prev': {
      const allSections = await getAllForProfile(profile.id);

      const updatedSections = changeOrder(
        allSections,
        summary.id,
        dto.action === 'move-next' ? Direction.Next : Direction.Prev
      );

      await updateSectionsOrder(updatedSections);

      break;
    }
    case 'toggle-visibility':
    case 'toggle-alignment':
    case 'toggle-spacing': {
      const updatedSummary = exhaustive(dto.action, {
        'toggle-visibility': () => toggleVisibility(summary),
        'toggle-alignment': () => toggleAlignment(summary),
        'toggle-spacing': () => toggleSpacing(summary),
      });

      await repo.update(updatedSummary);

      break;
    }
    default: {
      throw new UnexpectedError('Unknown action: ' + dto.action);
    }
  }

  return undefined;
};

export const getWithNodes = async (
  profileId: Profile['id']
): Promise<Summary<'with_nodes'>> => {
  return repo.getOneWithNodes(profileId);
};
