import { ActionFunctionArgs } from '@remix-run/node';
import {
  json,
  useActionData,
  useLoaderData,
  useOutletContext,
} from '@remix-run/react';

import { AppNavigate } from '~/components/navigation/navigate';
import {
  Node,
  toId,
  toSaveDto,
} from '~/modules/profile/shared:nodes/Node';
import { updateNode } from '~/modules/profile/shared:nodes/node.service';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { NodeForm } from '../shared:nodes/node.form';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params, payload }) => {
      const dto = toSaveDto(payload);
      const targetNodeId = toId(params.nodeId);

      const updatedNode = updateNode(dto, targetNodeId);

      return json(
        {
          status: 'success',
          data: updatedNode,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = createRouteLoader({
  loader: async () => {
    return json({ status: 'success', tags: [] as Tag[] } as const);
  },
});

export default function EditSummaryNodePage() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const context = useOutletContext<{ node: Node } | undefined>();

  if (loaderData?.status === 'error') {
    return <div>Something went wrong.</div>;
  }

  if (actionData?.status === 'success' || !context?.node) {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  return (
    <NodeForm
      tags={loaderData?.tags}
      error={
        actionData?.status === 'error' ? 'Something went wrong' : undefined
      }
      node={context?.node}
    />
  );
}
