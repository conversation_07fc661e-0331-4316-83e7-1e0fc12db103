import { ActionFunctionArgs } from '@remix-run/node';
import { json, useActionData, useOutletContext } from '@remix-run/react';

import { AppNavigate } from '~/components/navigation/navigate';
import { createRouteAction, createRouteLoader } from '~/utils/route-actions';
import { update as updateSummarySection } from './backend/summary.service';
import { SummaryForm } from './components/summary.form';
import { Summary, toSaveDto } from './entities/Summary';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = toSaveDto(payload);

      const updatedSummary = await updateSummarySection(dto);

      return json(
        {
          status: 'success',
          data: updatedSummary,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = createRouteLoader({
  loader: async () => {
    return json({ status: 'success' } as const);
  },
});

export default function EditSummaryPage() {
  const actionData = useActionData<typeof action>();
  const context = useOutletContext<{ summary?: Summary<'base'> }>();

  if (actionData?.status === 'success') {
    return <AppNavigate to="/profile" preventScrollReset />;
  }

  if (!context?.summary) {
    return <div>Summary not found.</div>;
  }

  return <SummaryForm summary={context.summary} />;
}
