import { ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/react';

import { createRouteAction } from '~/utils/route-actions';
import { toQuickActionDto } from '../shared:sections/Section';
import { handleQuickAction } from './backend/summary.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = toQuickActionDto(payload);

      await handleQuickAction(dto);

      return json(
        {
          status: 'success',
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);
