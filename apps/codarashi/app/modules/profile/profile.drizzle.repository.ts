import { asc, eq } from 'drizzle-orm';

import { DomainError, ErrorMessages, UnexpectedError } from '@awe/core';
import { db, DbOrTx } from '~/database.server';
import { UserId } from '~/domain-entities/User';
import { Profile, toProfile } from './entities/Profile';
import { profiles } from './profile.table';
import { profileSections } from './shared:sections/section.table';

export const createProfile = async (
  profile: Profile,
  tx: DbOrTx = db
): Promise<Profile> => {
  try {
    await tx.insert(profiles).values({
      id: profile.id,
      user_id: profile.user_id,
      email: profile.email,
      name: profile.name,
      title: profile.title,
      location: profile.location,
      linkedin: profile.linkedin,
      github: profile.github,
    });
    return profile;
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'ProfileRepository',
      method: createProfile.name,
      profile,
    });
  }
};

export const update = async (
  profile: Profile,
  tx: DbOrTx = db
): Promise<void> => {
  try {
    await tx
      .update(profiles)
      .set({
        name: profile.name,
        title: profile.title,
        location: profile.location,
        linkedin: profile.linkedin,
        github: profile.github,
        email: profile.email,
        updated_at: new Date(),
      })
      .where(eq(profiles.id, profile.id));
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'ProfileRepository',
      method: update.name,
      profile,
    });
  }
};

export const findByUserId = async (
  userId: UserId,
  tx: DbOrTx = db
): Promise<Profile | null> => {
  try {
    const result = await tx
      .select()
      .from(profiles)
      // .where(eq(profiles.user_id, userId))
      .limit(1);

    if (!result.length) return null;

    return toProfile({
      id: result[0].id,
      user_id: result[0].user_id,
      email: result[0].email,
      name: result[0].name,
      title: result[0].title,
      location: result[0].location,
      linkedin: result[0].linkedin,
      github: result[0].github,
    });
  } catch (err) {
    throw new UnexpectedError(err, {
      service: 'ProfileRepository',
      method: findByUserId.name,
      userId,
    });
  }
};

export const getWithSections = async (
  _userId: UserId,
  tx: DbOrTx = db
): Promise<Profile<'with_sections'>> => {
  const result = await tx
    .select({
      profile: profiles,
      section: profileSections,
    })
    .from(profiles)
    .leftJoin(profileSections, eq(profiles.id, profileSections.profile_id))
    .orderBy(asc(profileSections.order));

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'ProfileRepository',
      method: getWithSections.name,
    });
  }

  return toProfile(
    {
      ...result[0].profile,
      sections: result.map((res) => res.section).filter(Boolean),
    },
    'with_sections'
  );
};
