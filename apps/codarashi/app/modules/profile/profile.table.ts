import { sql } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';

export const profiles = sqliteTable('profiles', {
  _id: integer('_id').primaryKey({ autoIncrement: true }),

  id: text('id').unique().notNull(),
  user_id: text('user_id').notNull(),
  // .references(() => users.id),
  name: text('name').notNull(),
  email: text('email').notNull(),
  title: text('title').notNull(),
  location: text('location').notNull(),
  linkedin: text('linkedin').notNull(),
  github: text('github').notNull(),

  created_at: integer('created_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updated_at: integer('updated_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});
