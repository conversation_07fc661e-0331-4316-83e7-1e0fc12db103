import { ActionFunctionArgs } from '@remix-run/node';
import {
  json,
  Navigate,
  useActionData,
  useNavigation,
  useOutletContext,
} from '@remix-run/react';

import { Tag, toId, toSaveDto } from '~/modules/profile/shared:tags/Tag';
import { formDataToJson, getFormData } from '~/utils/form-data';
import { createRouteAction } from '~/utils/route-actions';
import { getProfile } from '../profile.service';
import { SaveTagForm } from './save-tag.form';
import { updateTag } from './tags.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ request, params }) => {
      const data = formDataToJson(await getFormData(request));

      const dto = toSaveDto(data);
      const tagId = toId(params.tagId);
      const profile = await getProfile();
      const saveResult = await updateTag(dto, tagId, profile.id);

      return json(
        { status: 'success', data: saveResult } as const,
        {
          status: 200,
        } as const
      );
    },
  })(args);

const UpdateTagRoute = () => {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const { tags, targetTag } = useOutletContext<{
    tags: Tag[];
    targetTag: Tag;
  }>();

  if (actionData?.status === 'success') {
    return <Navigate to="/profile/settings/tags" />;
  }

  return (
    <SaveTagForm
      error={
        actionData?.status === 'error' ? 'Something went wrong' : undefined
      }
      isLoading={navigation.state === 'submitting'}
      existingTags={tags
        .filter((t) => t.id !== targetTag.id)
        .map((tag) => tag.name.toLowerCase() as Tag['name'])}
      initialData={targetTag}
    />
  );
};

export default UpdateTagRoute;
