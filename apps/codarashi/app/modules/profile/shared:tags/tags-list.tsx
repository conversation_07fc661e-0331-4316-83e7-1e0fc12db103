import { Outlet, useParams } from '@remix-run/react';

import { FC } from 'react';
import { Typography } from '~/design-system';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import { useUrlNavigation } from '~/utils/navigation';
import { FormToggleLink } from '../components/form-toggle';
import { TagContextActions } from './context-actions';

type Props = {
  tags: Tag[];
};

export const TagsList: FC<Props> = ({ tags }) => {
  const params = useParams();
  const { fullPath } = useUrlNavigation();
  const isFormOpen = fullPath.endsWith('/create') || fullPath.endsWith('/edit');

  return (
    <div className="flex flex-col gap-lg">
      <header className="flex gap-lg">
        <Typography size="h4" as="h1" className="text-neural font-bold">
          Tags
        </Typography>

        <FormToggleLink
          to={
            isFormOpen
              ? '/profile/settings/tags'
              : '/profile/settings/tags/create'
          }
          opened={isFormOpen}
        />
      </header>

      {!isFormOpen && (
        <div className="flex flex-col gap-md items-baseline">
          {tags.length === 0 && <div>No tags</div>}
          {tags.map((tag) => (
            <TagContextActions label={tag.name} tagId={tag.id} key={tag.id} />
          ))}
        </div>
      )}
      <Outlet
        context={{ tags, targetTag: tags.find((t) => t.id === params.tagId) }}
      />
    </div>
  );
};
