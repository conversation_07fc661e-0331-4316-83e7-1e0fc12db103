import { eq } from 'drizzle-orm';

import { DomainError, ErrorMessages } from '@awe/core';
import { db } from '~/database.server';
import { Profile } from '../entities/Profile';
import { Tag } from './Tag';
import { profileTags } from './tags.table';

export const createTag = async (tag: Tag): Promise<Tag> => {
  await db.insert(profileTags).values(tag);
  return tag;
};

export const replace = async (updatedTag: Tag): Promise<void> => {
  const result = await db
    .update(profileTags)
    .set(updatedTag)
    .where(eq(profileTags.id, updatedTag.id))
    .returning();

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'TagsRepository',
      method: 'replace',
      tag: updatedTag,
    });
  }
};

export const findManyByProfileId = async (
  profileId: Profile['id']
): Promise<Tag[]> => {
  return db
    .select()
    .from(profileTags)
    .where(eq(profileTags.profile_id, profileId));
};

export const findDuplicates = async (
  profileId: Profile['id'],
  tagName: Tag['name']
): Promise<Tag | null> => {
  const result = await db
    .select()
    .from(profileTags)
    .where(
      eq(profileTags.profile_id, profileId) && eq(profileTags.name, tagName)
    )
    .limit(1);

  return result[0] ?? null;
};

export const deleteTag = async (tagId: Tag['id']): Promise<void> => {
  const result = await db
    .delete(profileTags)
    .where(eq(profileTags.id, tagId))
    .returning();

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'TagsRepository',
      method: 'deleteTag',
      tagId,
    });
  }
};

export const getById = async (tagId: Tag['id']): Promise<Tag> => {
  const result = await db
    .select()
    .from(profileTags)
    .where(eq(profileTags.id, tagId))
    .limit(1);

  if (!result.length) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      service: 'TagsRepository',
      method: 'getById',
      tagId,
    });
  }

  return result[0];
};
