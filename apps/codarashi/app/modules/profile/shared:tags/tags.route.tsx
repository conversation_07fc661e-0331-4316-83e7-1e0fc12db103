import { json, LoaderFunctionArgs } from '@remix-run/node';

import { useLoaderData } from '@remix-run/react';
import { createRouteLoader } from '~/utils/route-actions';
import { ProfileLayout } from '../components/layout';
import { getProfile } from '../profile.service';
import { TagsList } from './tags-list';
import { getAllForProfile } from './tags.service';

export const loader = (args: LoaderFunctionArgs) =>
  createRouteLoader({
    loader: async () => {
      const profile = await getProfile();
      const tags = await getAllForProfile(profile.id);

      return json({ status: 'success', tags } as const);
    },
  })(args);

export default function TagsPage() {
  const loaderData = useLoaderData<typeof loader>();

  if (loaderData.status === 'error') {
    return <div>Something went wrong.</div>;
  }

  return (
    <ProfileLayout>
      <TagsList tags={loaderData.tags} />
    </ProfileLayout>
  );
}
