import { sql } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { profiles } from '../profile.table';
import { Tag } from './Tag';
import { Profile } from '../entities/Profile';

export const profileTags = sqliteTable('profile-tags', {
  _id: integer('_id').primaryKey({ autoIncrement: true }),

  id: text('id').unique().notNull().$type<Tag['id']>(),
  profile_id: text('profile_id')
    .notNull()
    .$type<Profile['id']>()
    .references(() => profiles.id),
  name: text('name').notNull().$type<Tag['name']>(),

  created_at: integer('created_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updated_at: integer('updated_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});
