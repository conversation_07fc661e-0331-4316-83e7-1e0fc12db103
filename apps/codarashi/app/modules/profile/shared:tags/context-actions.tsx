import { FC, ReactNode } from 'react';
import { FaPencil, FaTrash } from 'react-icons/fa6';
import { RemixLink } from '~/design-system';

import { NativePopover } from '~/design-system/NativePopover';
import { Tag } from '~/modules/profile/shared:tags/Tag';
import classNames from '~/utils/css';

type Props = {
  tagId: Tag['id'];
  label: ReactNode;
};

export const TagContextActions: FC<Props> = ({ tagId, label }) => {
  return (
    <NativePopover
      renderTrigger={(props) => (
        <button type="button" {...props}>
          {label}
        </button>
      )}
      uniqueId={tagId}
    >
      {({ close: closePopover }) => (
        <div className="flex items-center gap-md p-sm border-neutral-5 border bg-white rounded-md shadow-md">
          <RemixLink
            to={`/profile/settings/tags/${tagId}/edit`}
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            <FaPencil
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </RemixLink>

          <RemixLink
            to={`/profile/settings/tags/${tagId}/delete`}
            onClick={() => {
              closePopover();
              return true;
            }}
          >
            {' '}
            <FaTrash
              className={classNames('text-neutral+3', {
                'text-primary': false,
              })}
            />
          </RemixLink>
        </div>
      )}
    </NativePopover>
  );
};
