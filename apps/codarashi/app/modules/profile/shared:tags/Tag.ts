import { z } from 'zod';

import { NanoId, StringOfLength, zodToDomainOrThrow } from '@awe/core';
import { Profile, id as profileId } from '~/modules/profile/entities/Profile';

const generateId = () => NanoId.generate('tag');

export const id = NanoId.parser('tag');

export const toId = (source: string | undefined) =>
  zodToDomainOrThrow(z.object({ id }), { id: source }).id;

const parser = z.object({
  id,
  profile_id: profileId,
  name: StringOfLength.parser(1, 50, 'tag_name'),
});

const saveDto = z.object({
  tag_name: parser.shape.name,
});

export const create = (
  dto: SaveDto,
  profileId: Profile['id'],
  id = generateId()
): Tag => {
  return {
    id,
    profile_id: profileId,
    name: dto.tag_name,
  };
};

export const update = (dto: SaveDto, tag: Tag): Tag => {
  return {
    ...tag,
    name: dto.tag_name,
  };
};

export const toSaveDto = (source: unknown) =>
  zodToDomainOrThrow(saveDto, source);

export type SaveDto = z.infer<typeof saveDto>;

export type Tag = z.infer<typeof parser>;
