import { Form, useSubmit } from '@remix-run/react';
import { FC, useId } from 'react';
import { useForm } from 'react-hook-form';

import {
  HorizontalStack,
  PrimaryButton,
  RemixLink,
  Typography,
} from '~/design-system';
import { TextField } from '~/design-system/Input';
import { SaveDto, Tag, toSaveDto } from '~/modules/profile/shared:tags/Tag';
import { jsonToFormData } from '~/utils/form-data';
import { useFormResolver } from '~/utils/form-resolver';
import { AppSearchParams } from '~/utils/misc';

type Props = {
  existingTags: Tag['name'][];
  initialData?: Pick<Tag, 'name'>;
  isLoading?: boolean;
  error?: string;
};

export const SaveTagForm: FC<Props> = ({
  existingTags,
  initialData,
  error,
  isLoading,
}) => {
  const submit = useSubmit();
  const form = useForm<SaveDto>({
    resolver: useFormResolver(toSaveDto),
    defaultValues: { tag_name: initialData?.name ?? '' },
  });

  const nameId = useId();
  const tagNameExists = existingTags.includes(form.watch('tag_name'));

  return (
    <Form
      onSubmit={form.handleSubmit((data) =>
        submit(jsonToFormData(data), { method: 'post', action: '' })
      )}
      method="post"
      action={`?${AppSearchParams.RedirectTo}=/profile/settings/tags`}
    >
      <Typography as="legend" size="h4" className="mb-lg">
        {initialData ? `Edit tag "${initialData.name}"` : 'Create tag'}
      </Typography>
      <div className="flex flex-col gap-md">
        {error && (
          <div role="status" className="text-red-600">
            {error}
          </div>
        )}

        <div className="flex flex-col gap-sm">
          <label htmlFor={nameId}>Tag name</label>
          <TextField type="text" {...form.register('tag_name')} id={nameId} />
          {form.getFieldState('tag_name').error?.message && (
            <div role="status" className="text-red-600">
              {form.getFieldState('tag_name').error?.message}
            </div>
          )}
          {tagNameExists && (
            <div role="status" className="text-red-600">
              Tag already exists
            </div>
          )}
        </div>

        <HorizontalStack gap="lg" itemsJustify="end" className="items-center">
          <RemixLink to="/profile/settings/tags" preventScrollReset>
            Cancel
          </RemixLink>

          <PrimaryButton disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save'}
          </PrimaryButton>
        </HorizontalStack>
      </div>
    </Form>
  );
};
