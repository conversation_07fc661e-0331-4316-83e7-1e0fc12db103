import { CustomError, DomainError, ErrorMessages } from '@awe/core';

import { Profile } from '../entities/Profile';
import { create, SaveDto, Tag, update as updateEntity } from './Tag';
import * as repo from './tags.drizzle.repository';

export const createTag = async (dto: SaveDto, profileId: Profile['id']) => {
  try {
    const newTag = create(dto, profileId);

    const duplicate = await repo.findDuplicates(profileId, newTag.name);

    if (duplicate) {
      throw new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: 'TagsService',
        method: createTag.name,
        dto,
      });
    }

    return await repo.createTag(newTag);
  } catch (err) {
    throw CustomError.toError(err).addContext({
      service: 'TagsService',
      method: createTag.name,
      dto,
    });
  }
};

export const updateTag = async (
  dto: SaveDto,
  tagId: Tag['id'],
  profileId: Profile['id']
): Promise<Tag> => {
  try {
    const duplicate = await repo.findDuplicates(profileId, dto.tag_name);

    if (duplicate && duplicate.id !== tagId) {
      throw new DomainError(ErrorMessages.EntityAlreadyExists, {
        service: 'TagsService',
        method: updateTag.name,
        dto,
        tagId: tagId,
      });
    }

    const existingTag = await repo.getById(tagId);
    const updatedTag = updateEntity(dto, existingTag);
    await repo.replace(updatedTag);

    return updatedTag;
  } catch (err) {
    throw CustomError.toError(err).addContext({
      service: 'TagsService',
      method: updateTag.name,
      dto,
      tagId: tagId,
    });
  }
};

export const getAllForProfile = async (profileId: Profile['id']) => {
  return await repo.findManyByProfileId(profileId).catch((err) => {
    throw CustomError.toError(err).addContext({
      service: 'TagsService',
      method: getAllForProfile.name,
      profileId,
    });
  });
};

export const deleteTagById = async (tagId: Tag['id']) => {
  return await repo.deleteTag(tagId).catch((err) => {
    throw CustomError.toError(err).addContext({
      service: 'TagsService',
      method: deleteTagById.name,
      tagId,
    });
  });
};
