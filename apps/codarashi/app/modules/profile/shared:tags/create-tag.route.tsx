import { ActionFunctionArgs } from '@remix-run/node';
import {
  json,
  Navigate,
  useActionData,
  useNavigation,
  useOutletContext,
} from '@remix-run/react';

import { CustomError, ErrorMessages } from '@awe/core';
import { match } from 'ts-pattern';
import { Tag, toSaveDto } from '~/modules/profile/shared:tags/Tag';
import { formDataToJson, getFormData } from '~/utils/form-data';
import { createRouteAction } from '~/utils/route-actions';
import { getProfile } from '../profile.service';
import { SaveTagForm } from './save-tag.form';
import { createTag } from './tags.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ request }) => {
      const data = formDataToJson(await getFormData(request));

      const dto = toSaveDto(data);
      const profile = await getProfile();
      const saveResult = await createTag(dto, profile.id);

      return json(
        { status: 'success', data: saveResult } as const,
        {
          status: 201,
        } as const
      );
    },
  })(args);

const CreateTagRoute = () => {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const { tags } = useOutletContext<{ tags: Tag[] }>();

  if (actionData?.status === 'success') {
    return <Navigate to="/profile/settings/tags" />;
  }

  const errorMessage = match(actionData?.status)
    .when(
      () => {
        return (
          actionData?.status === 'error' &&
          CustomError.isDomainError(
            actionData.error,
            ErrorMessages.EntityAlreadyExists
          )
        );
      },
      () => 'Tag already exists' as const
    )
    .when(
      () => actionData?.status === 'error',
      () => 'Something went wrong' as const
    )
    .otherwise(() => '' as const);

  return (
    <SaveTagForm
      error={errorMessage}
      isLoading={navigation.state === 'submitting'}
      existingTags={tags.map((tag) => tag.name.toLowerCase() as Tag['name'])}
    />
  );
};

export default CreateTagRoute;
