import { ActionFunctionArgs } from '@remix-run/node';
import {
  Form,
  json,
  Navigate,
  useActionData,
  useNavigation,
  useSubmit,
} from '@remix-run/react';
import { FC } from 'react';

import { PrimaryButton, RemixLink } from '~/design-system';
import { Dialog } from '~/design-system/Dialog';
import { toId } from '~/modules/profile/shared:tags/Tag';
import { AppSearchParams } from '~/utils/misc';
import { createRouteAction } from '~/utils/route-actions';
import { deleteTagById } from './tags.service';

export const action = (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ params }) => {
      const id = toId(params.tagId);

      const saveResult = await deleteTagById(id);

      return json(
        { status: 'success', data: saveResult } as const,
        {
          status: 201,
        } as const
      );
    },
  })(args);

const DeleteTagRoute: FC = () => {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();

  if (actionData?.status === 'success') {
    return <Navigate to="/profile/settings/tags" />;
  }

  return (
    <Dialog open mode="modal">
      <Form
        className="flex flex-col gap-lg p-md h-full"
        action={`?${AppSearchParams.RedirectTo}=/profile/settings/tags`}
        method="post"
        onSubmit={(event) => submit(event.currentTarget)}
      >
        <header>Delete tag?</header>

        <div className="flex justify-end items-end gap-md grow">
          <RemixLink to="/profile/settings/tags">Cancel</RemixLink>

          <PrimaryButton
            type="submit"
            disabled={navigation.state === 'submitting'}
          >
            {navigation.state === 'submitting' ? 'Deleting...' : 'Delete'}
          </PrimaryButton>
        </div>
      </Form>
    </Dialog>
  );
};

export default DeleteTagRoute;
