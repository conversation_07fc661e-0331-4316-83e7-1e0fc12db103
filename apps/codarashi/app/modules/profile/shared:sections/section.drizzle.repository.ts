import { eq } from 'drizzle-orm';

import { ErrorMessages, UnexpectedError } from '@awe/core';
import { db, DbOrTx } from '~/database.server';
import { Profile } from '../entities/Profile';
import { toBaseEntities, toEntity } from './Section';
import { BaseSection, SectionType } from './section.shared';
import { profileSections } from './section.table';

export const getProfileSections = async (
  profileId: Profile['id']
): Promise<BaseSection[]> => {
  try {
    const result = await db
      .select()
      .from(profileSections)
      .where(eq(profileSections.profile_id, profileId));

    return toBaseEntities(result);
  } catch (err) {
    throw new UnexpectedError(err);
  }
};

export const updateOrder = async (sections: BaseSection[]) => {
  try {
    await Promise.all(
      sections.map((section) =>
        db
          .update(profileSections)
          .set({ order: section.order })
          .where(eq(profileSections.id, section.id))
      )
    );
  } catch (err) {
    throw new UnexpectedError(err);
  }
};

export const createMany = async (
  sections: BaseSection[],
  profileId: Profile['id'],
  tx: DbOrTx = db
): Promise<void> => {
  try {
    await tx.insert(profileSections).values(
      sections.map((section) => ({
        id: section.id,
        type: section.type,
        order: section.order,
        title: section.title,
        visible: section.visible,
        alignment: section.alignment,
        gap: section.gap,
        profile_id: profileId,
      }))
    );
  } catch (err) {
    throw new UnexpectedError(err);
  }
};

export const getByType = async <T extends SectionType>(
  type: T,
  profileId: Profile['id']
): Promise<BaseSection<T>> => {
  try {
    const result = await db
      .select()
      .from(profileSections)
      .where(
        eq(profileSections.profile_id, profileId) &&
          eq(profileSections.type, type)
      )
      .limit(1);

    if (!result.length) {
      throw new UnexpectedError(ErrorMessages.EntityNotFound, {
        message: `Section not found: type: ${type}`,
      });
    }

    return toEntity({ ...result[0], nodes: [] });
  } catch (err) {
    if (err instanceof UnexpectedError) {
      throw err;
    }
    throw new UnexpectedError(err);
  }
};
