import { Numbers } from '@awe/core';
import { DbOrTx } from '~/database.server';
import { Profile } from '../entities/Profile';
import { createMultiple as createHeaderNodes } from '../header/backend/node.service';
import { createDefaults as createHeader } from '../header/entities/Header';
import { createNode } from '../shared:nodes/node.service';
import { createDefaults as createSummary } from '../summary/entities/Summary';
import * as repo from './section.drizzle.repository';
import { BaseSection, SectionType } from './section.shared';

export const getSectionForType = async <T extends SectionType>(
  type: T,
  profileId: Profile['id']
): Promise<BaseSection<T>> => await repo.getByType(type, profileId);

export const getAllForProfile = async (
  profileId: Profile['id']
): Promise<BaseSection[]> => {
  return repo.getProfileSections(profileId);
};

/* Should be ran within a transaction */
export const createDefaultSections = async (
  profile: Profile,
  tx: DbOrTx
): Promise<void> => {
  const header = createHeader(Numbers.toPositiveInteger(1));
  const summary = createSummary(Numbers.toPositiveInteger(2));

  await repo.createMany([summary, header], profile.id, tx);

  await createNode(summary.nodes[0], profile.id, summary.id, tx);
  await createHeaderNodes(header.nodes, profile.id, header.id, tx);
};

export const updateSectionsOrder = async (sections: BaseSection[]) => {
  await repo.updateOrder(sections);
};
