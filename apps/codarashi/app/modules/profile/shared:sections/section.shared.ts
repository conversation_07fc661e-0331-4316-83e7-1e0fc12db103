import { z } from 'zod';

import { NanoId, Numbers, StringOfLength, zodToDomainOrThrow } from '@awe/core';

export const SectionType = {
  Header: 'header',
  Summary: 'summary',
  Tech: 'tech',
  Skills: 'skills',
  Projects: 'projects',
  Jobs: 'jobs',
  Education: 'education',
  Languages: 'languages',
  Courses: 'courses',
  Contributions: 'contributions',
  Certifications: 'certifications',
  Custom: 'custom',
} as const;

export type SectionType = (typeof SectionType)[keyof typeof SectionType];

const sectionId = NanoId.parser('sctn');

export enum Alignment {
  Left = 'left',
  Center = 'center',
}

export enum Gap {
  Small = 'small',
  Medium = 'medium',
  Large = 'large',
}

export const toId = (source: unknown) =>
  zodToDomainOrThrow(z.object({ id: sectionId }), { id: source }).id;

export const generateSectionId = () => NanoId.generate('sctn');

export const sectionBase = z.object({
  id: sectionId,
  type: z.enum([
    SectionType.Header,
    SectionType.Summary,
    SectionType.Tech,
    SectionType.Skills,
    SectionType.Projects,
    SectionType.Jobs,
    SectionType.Education,
    SectionType.Languages,
    SectionType.Courses,
    SectionType.Contributions,
    SectionType.Certifications,
    SectionType.Custom,
  ]),
  title: StringOfLength.parser(0, 50).default(''),
  visible: z.boolean(),
  alignment: z.nativeEnum(Alignment),
  gap: z.nativeEnum(Gap),
  order: Numbers.positiveIntegerParser,
});

export type BaseSection<T extends SectionType = SectionType> = z.infer<
  typeof sectionBase
> & { type: T };

export enum QuickAction {
  MoveNext = 'move-next',
  MovePrev = 'move-prev',
  ToggleVisibility = 'toggle-visibility',
  ToggleAlignment = 'toggle-alignment',
  ToggleSpacing = 'toggle-spacing',
}
