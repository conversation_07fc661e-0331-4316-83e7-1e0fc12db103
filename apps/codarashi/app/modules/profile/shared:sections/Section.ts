import { z } from 'zod';

import {
  DomainError,
  ErrorMessages,
  moveArrayItem,
  Numbers,
  zodToDomainOrThrow,
} from '@awe/core';
import { Direction } from '~/utils/misc';
import {
  SaveDto as HeaderSaveDto,
  parser as parserHeader,
} from '../header/entities/Header';
import {
  parser as parserSummary,
  SaveDto as SummarySaveDto,
} from '../summary/entities/Summary';
import {
  Alignment,
  BaseSection,
  Gap,
  QuickAction,
  sectionBase,
  SectionType,
} from './section.shared';

export const parser = z.discriminatedUnion('type', [
  parserSummary.withNodes,
  parserHeader.withNodes,
]);

export const toEntity = <T extends SectionType>(source: unknown) =>
  zodToDomainOrThrow(parser, source) as Extract<ProfileSection, { type: T }>;

export type ProfileSection = z.infer<typeof parser>;

export const toBaseEntities = (source: unknown[]) =>
  zodToDomainOrThrow(z.object({ items: z.array(sectionBase) }), {
    items: source,
  }).items;

export type SaveDto = HeaderSaveDto | SummarySaveDto;

export const changeOrder = (
  sections: BaseSection[],
  sectionId: ProfileSection['id'],
  direction: Direction
): BaseSection[] => {
  const index = sections.findIndex((section) => section.id === sectionId);

  if (index === -1) {
    throw new DomainError(ErrorMessages.EntityNotFound, {
      message: 'Section not found: id: ' + sectionId,
    });
  }

  return moveArrayItem(
    sections,
    index,
    index + (direction === Direction.Next ? 1 : -1)
  ).map((elem, index) => ({
    ...elem,
    order: Numbers.toPositiveInteger(index + 1),
  }));
};

export const toggleVisibility = <T extends BaseSection>(section: T): T => {
  return {
    ...section,
    visible: !section.visible,
  };
};

export const toggleSpacing = <T extends BaseSection>(section: T): T => {
  const gaps = [Gap.Small, Gap.Medium, Gap.Large];
  const current = gaps.findIndex((gap) => gap === section.gap);

  return {
    ...section,
    gap: gaps.at(current + 1) ?? Gap.Small,
  };
};

export const toggleAlignment = <T extends BaseSection>(section: T): T => {
  return {
    ...section,
    alignment:
      section.alignment === Alignment.Center
        ? Alignment.Left
        : Alignment.Center,
  };
};

export const quickActionDto = z.object({
  action: z.nativeEnum(QuickAction),
});

export type QuickActionDto = z.infer<typeof quickActionDto>;

export const toQuickActionDto = (source: unknown) =>
  zodToDomainOrThrow(quickActionDto, source);
