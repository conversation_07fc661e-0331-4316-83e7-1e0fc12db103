import { sql } from 'drizzle-orm';
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core';

import { Profile } from '../entities/Profile';
import { profiles } from '../profile.table';
import { BaseSection, SectionType } from './section.shared';

export const profileSections = sqliteTable('profile-sections', {
  _id: integer('_id').primaryKey({ autoIncrement: true }),

  id: text('id').unique().notNull().$type<BaseSection['id']>(),
  profile_id: text('profile_id')
    .notNull()
    .$type<Profile['id']>()
    .references(() => profiles.id),
  type: text('type', { enum: ['header', 'summary'] })
    .$type<SectionType>()
    .notNull(),

  title: text('title').notNull().$type<BaseSection['title']>(),
  visible: integer('visible', { mode: 'boolean' })
    .notNull()
    .$type<BaseSection['visible']>(),
  gap: text('gap').notNull().$type<BaseSection['gap']>(),
  alignment: text('alignment').notNull().$type<BaseSection['alignment']>(),
  order: integer('order').notNull().$type<BaseSection['order']>(),

  created_at: integer('created_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updated_at: integer('updated_at', { mode: 'timestamp' })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});
