import { Await } from '@remix-run/react';
import { exhaustive } from 'exhaustive';
import { FC, Suspense } from 'react';

import { VerticalStack } from '~/design-system';
import { ProfileLayout } from './components/layout';
import { Profile } from './entities/Profile';
import { Header } from './header/entities/Header';
import { HeaderSection } from './header/header.index';
import { SectionType } from './shared:sections/section.shared';
import { Summary } from './summary/entities/Summary';
import { SummarySection } from './summary/summary.index';

type Props = {
  profile: Profile<'with_sections'>;
  headerSection: Promise<Header<'with_nodes'>>;
  summarySection: Promise<Summary<'with_nodes'>>;
};

export const ProfileIndex: FC<Props> = ({
  profile,
  headerSection,
  summarySection,
}) => {
  return (
    <ProfileLayout>
      <VerticalStack gap="xl" className="grow max-w-[80%]">
        {profile.sections.map((section, index) => {
          const isFirst = index === 0;
          const isLast = index === profile.sections.length - 1;

          return exhaustive(section.type, {
            [SectionType.Header]: () => (
              <Suspense
                fallback={<div>Loading header...</div>}
                key={section.id}
              >
                <Await resolve={headerSection}>
                  {(header) => (
                    <div role="region" aria-label="Header section">
                      <HeaderSection
                        profile={profile}
                        header={header}
                        isFirst={isFirst}
                        isLast={isLast}
                      />
                    </div>
                  )}
                </Await>
              </Suspense>
            ),
            [SectionType.Summary]: () => (
              <Suspense
                fallback={<div>Loading summary...</div>}
                key={section.id}
              >
                <Await resolve={summarySection}>
                  {(summary) => (
                    <div role="region" aria-label="Summary section">
                      <SummarySection
                        summary={summary}
                        isFirst={isFirst}
                        isLast={isLast}
                      />
                    </div>
                  )}
                </Await>
              </Suspense>
            ),
            [SectionType.Tech]: () => null,
            [SectionType.Skills]: () => null,
            [SectionType.Projects]: () => null,
            [SectionType.Jobs]: () => null,
            [SectionType.Courses]: () => null,
            [SectionType.Custom]: () => null,
            [SectionType.Contributions]: () => null,
            [SectionType.Languages]: () => null,
            [SectionType.Education]: () => null,
            [SectionType.Certifications]: () => null,
            _: () => null,
          });
        })}
      </VerticalStack>
    </ProfileLayout>
  );
};
