import { ActionFunctionArgs } from '@remix-run/node';
import { defer, json, useLoaderData } from '@remix-run/react';

import {
  getOrCreateProfile,
  updateProfile,
} from '~/modules/profile/profile.service';
import { createRouteAction } from '~/utils/route-actions';
import * as entity from './entities/Profile';
import { getWithNodes as getHeaderSection } from './header/backend/header.service';
import { ProfileIndex } from './profile.index';
import { getWithNodes as getSummarySection } from './summary/backend/summary.service';

export const action = async (args: ActionFunctionArgs) =>
  createRouteAction({
    handler: async ({ payload }) => {
      const dto = entity.toSaveDto(payload);

      const updatedProfile = await updateProfile(dto);

      return json(
        {
          status: 'success',
          data: updatedProfile,
        } as const,
        {
          status: 201,
        }
      );
    },
  })(args);

export const loader = async () => {
  const profile = await getOrCreateProfile();
  const headerSection = getHeaderSection(profile.id);
  const summarySection = getSummarySection(profile.id);

  return defer({
    profile,
    headerSection,
    summarySection,
  });
};

export default function ProfilePage() {
  const data = useLoaderData<typeof loader>();

  return (
    <div>
      <ProfileIndex
        profile={entity.toProfile(data.profile, 'with_sections')}
        headerSection={data.headerSection}
        summarySection={data.summarySection}
      />
    </div>
  );
}
