import { z } from 'zod';
import {
  NanoId,
  StringOfLength,
  withVersioning,
  Dates,
  Numbers,
} from '@awe/core';

export const id = NanoId.parser('app-stg');

export type ApplicationStageId = z.infer<typeof id>;

export enum Type {
  Interview = 'interview',
  Assessment = 'assessment',
  CodeChallenge = 'code-challenge',
  Task = 'task',
  Other = 'other',
}

export const parser = z
  .object({
    id,
    title: StringOfLength.parser(1, 50),
    type: z.nativeEnum(Type),
    notes: StringOfLength.parser(1, 5000).optional(),
    date_start: Dates.dateTimeParser,
    date_end: Dates.dateTimeParser.optional(),

    reminder: z
      .object({
        enabled: z.boolean(),
        hours_before: Numbers.positiveIntegerParser,
      })
      .optional(),
  })
  .merge(withVersioning);
