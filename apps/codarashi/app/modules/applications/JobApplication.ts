import { z } from 'zod';

import { NanoId, withVersioning } from '@awe/core';


export const id = NanoId.parser('job-app');

export type JobApplicationId = z.infer<typeof id>;

export enum Status {
  Draft = 'draft',
  Submitted = 'submitted',
  InProgress = 'in-progress',
  Accepted = 'accepted',
  Rejected = 'rejected',
  Archived = 'archived',
}

export const parser = z
  .object({
    id,

    status: z.nativeEnum(Status),
  })
  .merge(withVersioning);

export type JobApplication = z.infer<typeof parser>;
