import { GenericRecord } from '@awe/core';

export const formDataToJson = <T extends GenericRecord>(
  formData: FormData
): T => {
  const data = {} as Record<string, unknown>;

  formData.forEach((value, key) => {
    const existingValue = data[key];
    const parsedValue = safeParseJson(value);

    if (Array.isArray(existingValue)) {
      data[key] = [...existingValue, parsedValue];
      return;
    }

    if (data[key]) {
      data[key] = [data[key], parsedValue];
      return;
    }

    data[key] = parsedValue;
  });

  return data as T;
};

export const jsonToFormData = <T extends GenericRecord>(data: T): FormData => {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return;
    }
    formData.append(
      key,
      typeof value !== 'string' ? JSON.stringify(value) : value
    );
  });

  return formData;
};

const safeParseJson = (value: FormDataEntryValue) => {
  try {
    return JSON.parse(value.toString());
  } catch {
    return value;
  }
};

export async function getFormData(request: Request) {
  // Check if we already parsed and cached the formData
  if (!('_cachedFormData' in request)) {
    // TypeScript: Add our cache property to the Request type
    const req = request as Request & { _cachedFormData?: FormData };
    // Parse and cache formData
    req._cachedFormData = await request.formData().catch(() => new FormData());
  }

  // TypeScript: Assert the cache exists since we just added it
  return (request as Request & { _cachedFormData: FormData })._cachedFormData;
}
