import { useCallback } from 'react';
import { DeepMap, DeepPartial, FieldError } from 'react-hook-form';

import {
  CustomError,
  GenericRecord,
  ParsingError,
  UnexpectedError,
  isRecord,
  set,
} from '@awe/core';

export type FieldErrors<TFieldValues> = DeepMap<
  DeepPartial<TFieldValues>,
  FieldError
>;

const toFieldErros = <T extends object>(data: T) => {
  const result = {};

  Object.entries(data).forEach(([key, value]) => {
    if (isRecord(value)) {
      set(result, key, toFieldErros(value));
      return;
    }

    if (Array.isArray(value)) {
      set(result, key, {
        message: value[0],
        type: 'validation',
      });

      return;
    }

    set(result, key, {
      message: value,
      type: 'validation',
    });
  });

  return result as FieldErrors<T>;
};

export const useFormResolver = <T>(
  parse: (data: unknown) => T | ParsingError
) => {
  return useCallback(
    (data: GenericRecord) => {
      try {
        const parsed = parse(data);

        if (CustomError.isParsingError(parsed)) {
          const mappedErrors = toFieldErros(parsed.errors);

          return {
            values: {},
            errors: mappedErrors,
          };
        } else {
          return {
            values: parsed,
            errors: {},
          };
        }
      } catch (err) {
        if (err instanceof ParsingError) {
          const mappedErrors = toFieldErros(err.errors);

          return {
            values: {},
            errors: mappedErrors,
          };
        }

        throw new UnexpectedError(err, {
          message: "This should've been a parsing error",
        });
      }
    },
    [parse]
  );
};
