import { ActionFunctionArgs, redirect } from '@remix-run/node';
import { useLocation, useSearchParams } from '@remix-run/react';

import { CustomError } from '@awe/core';
// import { formDataToJson, getFormData } from './form-data';
import { AppSearchParams } from './misc';

export enum QueryParams {
  SummaryNodeForm = 'summary-node-form',
  FormState = 'form-state',
  FormErrors = 'form-errors',
}

export type ServerError<T extends Record<string, unknown>> = Record<
  keyof T,
  string[]
>;

export const useUrlNavigation = () => {
  const pathname = useLocation();
  const [searchParams] = useSearchParams();

  const fullPath = pathname.pathname + pathname.search + pathname.hash;

  const getSearchParam = (param: `${QueryParams}`) => {
    return searchParams.get(param);
  };

  const isParamActive = (param: `${QueryParams}`) => {
    return searchParams.has(param) && getSearchParam(param) === 'true';
  };

  const getFullPathWithToggle = (param: `${QueryParams}`) => {
    const newSearchParams = new URLSearchParams(searchParams);

    if (isParamActive(param)) {
      newSearchParams.delete(param);
    } else {
      newSearchParams.set(param, 'true');
    }

    return pathname.pathname + '?' + newSearchParams.toString() + pathname.hash;
  };

  const getFormState = <T>(): T | undefined => {
    const state = searchParams.get(QueryParams.FormState);
    return state ? JSON.parse(state) : undefined;
  };

  const getFormErrors = <T extends Record<string, string[]>>():
    | Record<keyof T, string[]>
    | undefined => {
    const errors = searchParams.get(QueryParams.FormErrors);
    return errors ? JSON.parse(errors) : undefined;
  };

  return {
    fullPath,
    getSearchParam,
    isParamActive,
    getFullPathWithToggle,
    getFormState,
    getFormErrors,
  };
};

export const redirectIfNeeded = async (request: Request, error?: unknown) => {
  const { searchParams } = new URL(request.url);
  const redirectTo = searchParams.get(AppSearchParams.RedirectTo);

  try {
    if (redirectTo) {
      // const formState = formDataToJson(await request.formData());
      searchParams.delete(AppSearchParams.RedirectTo);

      if (error) {
        searchParams.set(
          AppSearchParams.ErrorState,
          encodeURIComponent(JSON.stringify(error))
        );
      }

      /*       searchParams.set(
        AppSearchParams.FormState,
        encodeURIComponent(JSON.stringify(formState))
      ); */

      throw redirect(redirectTo + '?' + searchParams.toString());
    }

    return undefined;
  } catch (err) {
    throw redirect(redirectTo + '?' + searchParams.toString());
  }
};

export const composeOptionalRedirect = <T>(
  handler: (args: ActionFunctionArgs) => T
) => {
  return async (args: ActionFunctionArgs) => {
    const { request } = args;
    const { searchParams } = new URL(request.url);
    const redirectTo = searchParams.get(AppSearchParams.RedirectTo);

    try {
      const response = await handler(args);

      if (redirectTo) {
        // const formState = formDataToJson(await getFormData(request));
        searchParams.delete(AppSearchParams.RedirectTo);

        /*         searchParams.set(
          AppSearchParams.FormState,
          encodeURIComponent(JSON.stringify(formState))
        ); */

        return redirect(redirectTo + '?' + searchParams.toString());
      }

      return response;
    } catch (err) {
      if (redirectTo) {
        const error = CustomError.toError(err);
        searchParams.delete(AppSearchParams.RedirectTo);
        searchParams.set(
          AppSearchParams.ErrorState,
          encodeURIComponent(JSON.stringify(error))
        );

        return redirect(redirectTo + '?' + searchParams.toString());
      }

      throw err;
    }
  };
};
