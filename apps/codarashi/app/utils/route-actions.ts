import { ActionFunctionArgs, json, LoaderFunctionArgs } from '@remix-run/node';

import { GenericRecord } from '@awe/core';
import {
  composeGenericErrorHandling,
  withGenericErrorHandling,
} from './api-responses';
import { formDataToJson, getFormData } from './form-data';
import { composeOptionalRedirect } from './navigation';

/**
 * Web standards only allow GET or POST for form submits in case JS is not enabled
 * and we want to support both hence we'll just use POST for every mutation.
 */
export const assertPostOnly = (request: ActionFunctionArgs['request']) => {
  if (request.method !== 'POST') {
    throw json(
      {
        error: `invalid method: ${request.method}, only POST is allowed`,
      } as const,
      {
        status: 405,
      }
    );
  }
};

const withPostOnly = <T>(handler: (args: ActionFunctionArgs) => T) => {
  return async (args: ActionFunctionArgs) => {
    assertPostOnly(args.request);

    return handler(args);
  };
};

export type ActionHandler<T> = (args: ActionFunctionArgs) => T;

type ActionMiddleware<T, R> = (
  handler: ActionHandler<T>
) => ActionHandler<T | R>;

type GenericErrors = ReturnType<typeof withGenericErrorHandling>;

type ActionHandlerParams<T, R> = {
  handler: (args: ActionFunctionArgs & { payload: GenericRecord }) => T;
  interceptors?: [ActionMiddleware<T, R>, ...ActionMiddleware<T, R>[]];
};

export function createRouteAction<T>(params: {
  handler: (args: ActionFunctionArgs & { payload: GenericRecord }) => T;
}): ActionHandler<T | GenericErrors>;

export function createRouteAction<T, R>(params: {
  handler: (args: ActionFunctionArgs & { payload: GenericRecord }) => T;
  interceptors: [ActionMiddleware<T, R>, ...ActionMiddleware<T, R>[]];
}): ActionHandler<T | GenericErrors | R>;

export function createRouteAction<T, R>(params: ActionHandlerParams<T, R>) {
  const { handler, interceptors } = params;

  const allMiddlewares = [
    withPostOnly,
    composeOptionalRedirect,
    composeGenericErrorHandling,
    ...(interceptors ?? []),
  ];

  const newHandler = allMiddlewares.reduce(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (acc, fn) => fn(acc as any) as any,
    handler
  );

  const withPayload = async (args: ActionFunctionArgs) => {
    const data = formDataToJson(await getFormData(args.request));

    return newHandler({ ...args, payload: data });
  };

  return withPayload;
}

export type LoaderHandler<T> = (args: LoaderFunctionArgs) => T;

type LoaderMiddleware<T, R> = (
  handler: LoaderHandler<T>
) => LoaderHandler<T | R>;

export function createRouteLoader<T>(params: {
  loader: LoaderHandler<T>;
}): LoaderHandler<T | GenericErrors>;

export function createRouteLoader<T, R>(params: {
  loader: LoaderHandler<T>;
  interceptors: [LoaderMiddleware<T, R>, ...LoaderMiddleware<T, R>[]];
}): LoaderHandler<T | GenericErrors | R>;

export function createRouteLoader<T, R>(params: {
  loader: LoaderHandler<T>;
  interceptors?: [LoaderMiddleware<T, R>, ...LoaderMiddleware<T, R>[]];
}) {
  const { loader, interceptors } = params;

  const allMiddlewares = [composeGenericErrorHandling, ...(interceptors ?? [])];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const newLoader = allMiddlewares.reduce((acc, fn) => fn(acc) as any, loader);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return newLoader as any;
}
