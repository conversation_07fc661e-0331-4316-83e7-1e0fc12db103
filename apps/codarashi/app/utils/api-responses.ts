import { json } from '@remix-run/react';
import { exhaustive } from 'exhaustive';

import { CustomError } from '@awe/core';
import { ActionFunctionArgs, LoaderFunctionArgs } from '@remix-run/node';
import { formDataToJson, getFormData } from './form-data';
import { <PERSON><PERSON><PERSON><PERSON>, LoaderHandler } from './route-actions';

export const withGenericErrorHandling = async (
  request: Request,
  err: unknown
) => {
  const error = CustomError.toError(err);
  const formState = formDataToJson(await getFormData(request));

  console.log(error);

  return exhaustive(error.type, {
    ParsingError: () =>
      json(
        {
          status: 'error',
          formState,
          error,
        } as const,
        { status: 400 }
      ),
    DomainError: () =>
      json(
        {
          status: 'error',
          formState,
          error,
        } as const,
        { status: 400 }
      ),
    UnexpectedError: () =>
      json(
        {
          status: 'error',
          formState,
          error,
        } as const,
        { status: 500 }
      ),
  });
};

export const composeGenericErrorHandling = <T>(
  handler: ActionHandler<T> | LoaderHandler<T>
) => {
  return async (args: ActionFunctionArgs | LoaderFunctionArgs) => {
    try {
      const response = await handler(args);

      return response;
    } catch (err) {
      return withGenericErrorHandling(args.request, err);
    }
  };
};
