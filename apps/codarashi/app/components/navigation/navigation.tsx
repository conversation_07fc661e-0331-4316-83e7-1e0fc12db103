import { FC } from 'react';
import { VscAccount } from 'react-icons/vsc';

import { HorizontalStack } from '../../design-system';
import { NavLink } from './link';

export const MainNavigation: FC = () => {
  return (
    <HorizontalStack gap="lg" itemsJustify="space-between" itemsAlign="center">
      <HorizontalStack gap="lg">
        <NavLink to="/">Dashboard</NavLink>
        <NavLink to="/profile">Profile</NavLink>
        <NavLink to="/applications">Applications</NavLink>
      </HorizontalStack>

      <HorizontalStack gap="md" itemsAlign="center">
        <VscAccount size="30px" className="text-neutral" />
        <NavLink to="/account">Account</NavLink>
      </HorizontalStack>
    </HorizontalStack>
  );
};
