import { Navigate, useNavigate } from '@remix-run/react';
import { FC, useEffect } from 'react';
import { NavigateOptions } from 'react-router';

type Props = NavigateOptions & { to: string };

/**
 * The hook based approach is recommended on the client and it also allows to
 * disable scrollRestoration which we want in the majority of times. When JS
 * is not enabled we'll fallback to the Navigate component - the useEffect hook
 * won't do anything.
 */
export const AppNavigate: FC<Props> = (props) => {
  const navigate = useNavigate();

  useEffect(() => {
    navigate(props.to, props);
  }, []);

  return typeof window === 'undefined' ? <Navigate to={props.to} /> : null;
};
