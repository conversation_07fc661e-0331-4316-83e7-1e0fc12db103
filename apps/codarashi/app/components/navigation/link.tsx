import { FC, PropsWithChildren } from 'react';

import { theme } from '~/design-system/theme';
import classNames from '~/utils/css';
import { RemixNavLink, Typography, css } from '../../design-system';

export const NavLink: FC<PropsWithChildren<{ to: string }>> = ({
  children,
  to,
}) => {
  return (
    <RemixNavLink
      to={to}
      className={({ isActive }) => {
        return classNames('no-underline p-sm pl-md pr-xl block relative', {
          'text-neutral': !isActive,
          'hover:text-neutral-2': !isActive,
          'text-primary': isActive,
          'font-bold': isActive,
        });
      }}
      style={({ isActive }) =>
        isActive
          ? css({
              backgroundColor: theme.colors.primary,
              clipPath: 'polygon(0 0, 75% 0, 100% 50%, 75% 100%, 0% 100%)',
            })
          : undefined
      }
    >
      {({ isActive }) => {
        return (
          <>
            {isActive && (
              <>
                <div
                  className="w-full h-full absolute bg-on-primary inset-0"
                  style={css({
                    clipPath:
                      'polygon(2px 2px, calc(75% - 1px) 2px, calc(100% - 2px) calc(50% + 1px), calc(100% - 2px) calc(50% - 1px), calc(75% + 0px) calc(100% - 2px), 2px calc(100% - 2px))',
                  })}
                />
                {/* fixes the arrow's tip */}
                <div
                  className="bg-primary"
                  style={css({
                    position: 'absolute',
                    width: '3px',
                    height: '4px',
                    left: 'calc(100% - 4px)',
                    top: 'calc(50% - 2px)',
                  })}
                />
              </>
            )}

            <Typography size="h3" as="span" serif className="relative z-10">
              {children}
            </Typography>
          </>
        );
      }}
    </RemixNavLink>
  );
};
