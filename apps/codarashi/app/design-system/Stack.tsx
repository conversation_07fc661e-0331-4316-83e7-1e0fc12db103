import classNames from '~/utils/css';

import { Box, Props as BoxProps, SupportedTags } from './Box';

type Gap =
  | 'none'
  | 'xs'
  | 'sm'
  | 'sm+1'
  | 'md'
  | 'md+1'
  | 'lg'
  | 'lg+1'
  | 'xl'
  | '2xl'
  | 'huge';

type ItemsAlign = 'start' | 'center' | 'end';
type ItemsJustify = 'start' | 'center' | 'end' | 'space-between';

type Props<T extends SupportedTags> = {
  gap?: Gap;
  itemsAlign?: ItemsAlign;
  itemsJustify?: ItemsJustify;
} & BoxProps<T>;

const Stack = <T extends SupportedTags>({
  gap,
  itemsAlign,
  itemsJustify,
  children,
  className,
  direction,
  as,
  ref,
  ...rest
}: Props<T> & { direction: 'row' | 'column' }) => {
  return (
    <Box
      as={as}
      ref={ref}
      className={classNames(
        'flex',
        {
          'flex-col': direction === 'column',
          'items-start': itemsAlign === 'start',
          'items-center': itemsAlign === 'center',
          'items-end': itemsAlign === 'end',
          'justify-start': itemsJustify === 'start',
          'justify-center': itemsJustify === 'center',
          'justify-end': itemsJustify === 'end',
          'justify-between': itemsJustify === 'space-between',
          'gap-0': gap === 'none',
          'gap-xs': gap === 'xs',
          'gap-sm': gap === 'sm',
          'gap-sm+1': gap === 'sm+1',
          'gap-md': gap === 'md',
          'gap-md+1': gap === 'md+1',
          'gap-lg': gap === 'lg',
          'gap-lg+1': gap === 'lg+1',
          'gap-xl': gap === 'xl',
          'gap-2xl': gap === '2xl',
          'gap-huge': gap === 'huge',
        },
        className
      )}
      {...rest}
    >
      {children}
    </Box>
  );
};

export const HorizontalStack = <T extends SupportedTags>(props: Props<T>) => (
  <Stack {...props} direction="row" ref={props.ref} />
);

export const VerticalStack = <T extends SupportedTags>(props: Props<T>) => (
  <Stack {...props} direction="column" ref={props.ref} />
);
