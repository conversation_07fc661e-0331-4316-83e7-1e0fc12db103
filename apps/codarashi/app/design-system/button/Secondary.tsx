import {
  AllHTMLAttributes,
  ForwardedRef,
  PropsWithChildren,
  forwardRef,
} from 'react';

import classNames from '~/utils/css';
import { Box } from '../Box';
import { css } from '../css-hooks';

type SupportedElements = HTMLButtonElement | HTMLAnchorElement;

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  'style' | 'href' | 'className' | 'onChange' | 'onClick'
>;
type Props = PropsWithChildren<{
  as?: 'button' | 'a';
  color?: 'primary' | 'secondary' | 'error' | 'neutral';
  ref?: ForwardedRef<SupportedElements>;
}> &
  NativeHtmlAttributes;

export const SecondaryButton = forwardRef<SupportedElements, Props>(
  (
    {
      onClick,
      children,
      className,
      as = 'button',
      color = 'secondary',
      ...rest
    },
    ref
  ) => {
    return (
      <Box
        {...rest}
        as={as}
        className={classNames(
          'border',
          'border-solid',
          'py-sm+1 px-md',
          'rounded-md',
          {
            'border-secondary': color === 'secondary',
            'hover:bg-secondary+2': color === 'secondary',
            'active:bg-secondary+4': color === 'secondary',
            'hover:text-on-primary': color === 'secondary',
          },
          className
        )}
        ref={ref}
        onClick={onClick}
        style={css({
          transition: '.05s background-color ease-in-out',
          willChange: 'background-color',
        })}
      >
        {children}
      </Box>
    );
  }
);
