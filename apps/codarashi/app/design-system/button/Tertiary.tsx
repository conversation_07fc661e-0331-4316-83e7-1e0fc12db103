import {
  AllHTMLAttributes,
  ForwardedRef,
  PropsWithChildren,
  forwardRef,
} from 'react';

import classNames from '~/utils/css';
import { Box } from '../Box';
import { css } from '../css-hooks';

type SupportedElements = HTMLButtonElement | HTMLAnchorElement;

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  'style' | 'href' | 'className' | 'onChange'
>;
type Props = PropsWithChildren<{
  as?: 'button' | 'a';
  color?: 'primary' | 'secondary' | 'error' | 'neutral';
  ref?: ForwardedRef<SupportedElements>;
}> &
  NativeHtmlAttributes;

export const TertiaryButton = forwardRef<SupportedElements, Props>(
  ({ children, className, as = 'button', color = 'primary', ...rest }, ref) => {
    return (
      <Box
        {...rest}
        as={as}
        className={classNames(
          'py-xs px-sm hover:underline underline-offset-2',
          {
            'text-primary-light': color === 'primary',
            'hover:text-primary': color === 'primary',
          },
          className
        )}
        ref={ref}
        style={css({
          transition: '.1s color ease-in-out',
          willChange: 'color',
        })}
      >
        {children}
      </Box>
    );
  }
);
