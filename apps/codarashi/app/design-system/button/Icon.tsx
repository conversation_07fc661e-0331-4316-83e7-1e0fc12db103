import { AnchorHTMLAttributes, ComponentProps, forwardRef } from 'react';

import classNames from '~/utils/css';
import { RemixLink } from '../RemixLink';

export const IconButton = forwardRef<
  HTMLButtonElement,
  ComponentProps<'button'>
>(({ children, className, onClick, ...rest }, ref) => {
  return (
    <button
      {...rest}
      className={classNames(
        'flex items-center justify-center',
        'border-none',
        'outline-primary',
        'p-sm',
        'rounded-full',
        'hover:bg-tertiary-2',
        'transition-all duration-300',
        className
      )}
      ref={ref}
      onClick={onClick}
    >
      {children}
    </button>
  );
});

export const IconLink = forwardRef<
  typeof RemixLink,
  ComponentProps<typeof RemixLink>
>(({ children, className, to, ...rest }, ref) => {
  return (
    <RemixLink
      {...rest}
      className={classNames(
        'flex items-center justify-center',
        'border-none',
        'outline-primary',
        'p-sm',
        'rounded-full',
        'hover:bg-tertiary-2',
        'transition-all duration-300',
        className
      )}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ref={ref as any}
      to={to}
    >
      {children}
    </RemixLink>
  );
});

export const IconLinkExternal = forwardRef<
  typeof RemixLink,
  AnchorHTMLAttributes<HTMLAnchorElement>
>(({ children, className, href, ...rest }, ref) => {
  return (
    <a
      {...rest}
      className={classNames(
        'flex items-center justify-center',
        'border-none',
        'outline-primary',
        'p-sm',
        'rounded-full',
        'hover:bg-tertiary-2',
        'transition-all duration-300',
        className
      )}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ref={ref as any}
      href={href}
    >
      {children}
    </a>
  );
});
