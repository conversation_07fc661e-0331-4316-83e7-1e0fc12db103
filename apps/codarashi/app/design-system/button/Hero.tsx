import {
  AllHTMLAttributes,
  ForwardedRef,
  PropsWithChildren,
  forwardRef,
} from 'react';

import classNames from '~/utils/css';
import { Box } from '../Box';
import { css } from '../css-hooks';

type SupportedElements = HTMLButtonElement | HTMLAnchorElement;

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  'style' | 'href' | 'className' | 'onChange'
>;
type Props = PropsWithChildren<{
  as?: 'button' | 'a';
  color?: 'primary' | 'secondary' | 'error' | 'neutral';
  ref?: ForwardedRef<SupportedElements>;
}> &
  NativeHtmlAttributes;

export const HeroButton = forwardRef<SupportedElements, Props>(
  ({ children, className, as = 'button', color = 'primary', ...rest }, ref) => {
    return (
      <Box
        {...rest}
        as={as}
        className={classNames(
          'border',
          'border-solid',
          'py-sm pl-md pr-2xl',
          'outline-4',
          {
            'bg-primary-light': color === 'primary',
            'hover:bg-primary': color === 'primary',
            'text-color-on-primary': color === 'primary',
          },
          className
        )}
        ref={ref}
        style={css({
          clipPath: 'polygon(0% 0%, 0% 100%, 80% 100%, 100% 0%)',
          transition: '.05s all linear',
        })}
      >
        {children}
      </Box>
    );
  }
);
