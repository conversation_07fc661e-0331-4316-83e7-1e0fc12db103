import {
  AllHTMLAttributes,
  ForwardedRef,
  PropsWithChildren,
  forwardRef,
} from 'react';

import classNames from '~/utils/css';
import { Box } from '../Box';
import { css } from '../css-hooks';

type SupportedElements = HTMLButtonElement | HTMLAnchorElement;

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  | 'style'
  | 'href'
  | 'className'
  | 'onChange'
  | 'name'
  | 'value'
  | 'type'
  | 'disabled'
>;
type Props = PropsWithChildren<{
  as?: 'button' | 'a';
  color?: 'primary' | 'secondary' | 'error' | 'neutral';
  ref?: ForwardedRef<SupportedElements>;
}> &
  NativeHtmlAttributes;

export const PrimaryButton = forwardRef<SupportedElements, Props>(
  ({ children, className, as = 'button', color = 'primary', ...rest }, ref) => {
    return (
      <Box
        {...rest}
        as={as}
        className={classNames(
          'border',
          'border-solid',
          'py-sm+1 px-lg',
          'rounded-md',
          {
            'bg-primary': color === 'primary',
            'hover:bg-primary+2': color === 'primary',
            'active:bg-primary+4': color === 'primary',
            'text-on-primary': color === 'primary',
          },
          className
        )}
        ref={ref}
        style={css({
          transition: '.05s background-color ease-in-out',
          willChange: 'background-color',
        })}
      >
        {children}
      </Box>
    );
  }
);
