import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useFocus,
  useHover,
  useInteractions,
  FloatingFocusManager,
  FloatingPortal,
  FloatingArrow,
  arrow,
  Placement,
} from '@floating-ui/react';
import { FC, PropsWithChildren, useEffect, useRef, useState } from 'react';

type RenderProps = {
  ref: (node: HTMLElement | null) => void;
  getProps: () => Record<string, unknown>;
};

type Interactions = Partial<{
  click: boolean;
  dismiss: boolean;
  hover: boolean;
  focus: boolean;
}>;

type Props = PropsWithChildren<{
  renderTrigger: (props: RenderProps) => React.ReactElement;
  interactions: Interactions;
  placement?: Placement;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  modal?: boolean;
  arrow?: boolean;
}>;

export const Popover: FC<Props> = ({
  renderTrigger,
  interactions,
  isOpen: open,
  onOpenChange,
  placement = 'top',
  arrow: showArrow = true,
  children,
  modal = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(!!open);
  }, [open]);

  const arrowRef = useRef(null);
  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (isOpened) => {
      setIsOpen(isOpened);
      onOpenChange?.(isOpened);
    },
    placement,
    middleware: [
      offset(10),
      flip(),
      shift(),
      showArrow &&
        arrow({
          element: arrowRef,
        }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const click = useClick(context, {
    enabled: !!interactions.click,
  });
  const dismiss = useDismiss(context, {
    enabled: !!interactions.dismiss,
  });
  const role = useRole(context);
  const hover = useHover(context, {
    enabled: !!interactions.hover,
    delay: 300,
  });
  const focus = useFocus(context, {
    enabled: !!interactions.focus,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
    hover,
    focus,
    role,
  ]);

  return (
    <>
      {renderTrigger({ ref: refs.setReference, getProps: getReferenceProps })}
      {isOpen && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={modal}>
            <div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
            >
              {showArrow && <FloatingArrow ref={arrowRef} context={context} />}
              {children}
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}
    </>
  );
};
