import React, { ReactNode, forwardRef, useId } from 'react';

import classNames from '~/utils/css';
import { VerticalStack } from './Stack';

type Props = {
  error?: ReactNode;
  className?: string;
  description?: ReactNode;
} & React.HTMLProps<HTMLInputElement>;

export const TextField = forwardRef<HTMLInputElement, Props>((props, ref) => {
  const id = useId();
  const { label } = props;

  return (
    <VerticalStack gap="sm">
      <label
        className={classNames({
          'text-danger': !!props.error,
        })}
        htmlFor={id}
      >
        {label}
      </label>
      <input
        id={id}
        ref={ref}
        className={classNames(
          'p-2 border-neutral-3 border-solid border focus-visible:outline-primary rounded',
          {
            'border-danger': !!props.error,
          },
          props.className
        )}
        {...props}
      />
      {props.description && <div>{props.description}</div>}
      {props.error && <div>{props.error}</div>}
    </VerticalStack>
  );
});
