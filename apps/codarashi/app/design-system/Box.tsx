import { AllHTMLAttributes, ElementType, Ref } from 'react';

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  'className' | 'style' | 'onClick' | 'role'
>;

type SupportedElementsMap = Pick<
  HTMLElementTagNameMap,
  | 'a'
  | 'button'
  | 'div'
  | 'main'
  | 'header'
  | 'section'
  | 'p'
  | 'span'
  | 'label'
  | 'legend'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
>;

type SupportedElements = SupportedElementsMap[keyof SupportedElementsMap];

export type SupportedTags = keyof SupportedElementsMap;

export type Props<T extends SupportedTags = 'div'> = {
  as?: T;
  children?: React.ReactNode;
  ref?: Ref<SupportedElementsMap[T]>;
} & NativeHtmlAttributes;

export const Box = <T extends SupportedTags>({
  children,
  className,
  as,
  ref,
  ...rest
}: Props<T>) => {
  const Tag = (as || 'div') as ElementType;

  return (
    <Tag className={className} {...rest} ref={ref}>
      {children}
    </Tag>
  );
};
