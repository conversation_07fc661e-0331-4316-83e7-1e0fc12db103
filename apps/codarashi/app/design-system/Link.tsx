import { ComponentPropsWithRef, PropsWithChildren, forwardRef } from 'react';

import classNames from '~/utils/css';

type Props = ComponentPropsWithRef<'a'> & PropsWithChildren;

export const linkStyling = 'underline underline-offset-2';

export const Link = forwardRef<HTMLAnchorElement, Props>((props, ref) => {
  return (
    <a
      {...props}
      ref={ref}
      className={classNames(linkStyling, props.className)}
    >
      {props.children}
    </a>
  );
});
