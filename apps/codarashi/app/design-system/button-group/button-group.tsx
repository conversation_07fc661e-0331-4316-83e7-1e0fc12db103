import {
  PropsWithChildren,
  createContext,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import {
  AriaRadioGroupProps,
  AriaRadioProps,
  VisuallyHidden,
  mergeProps,
  useFocusRing,
  useRadio,
  useRadioGroup,
} from 'react-aria';
import { FaCheck } from 'react-icons/fa';
import { useRadioGroupState } from 'react-stately';
import invariant from 'tiny-invariant';

import classNames from '~/utils/css';
import { HorizontalStack } from '../Stack';

const ButtonContext = createContext<ReturnType<
  typeof useRadioGroupState
> | null>(null);

type Props<T> = Omit<AriaRadioGroupProps, 'onChange' | 'value'> &
  PropsWithChildren<{
    label: string;
    value: T;
    onChange: (newValue: T) => void;
  }>;

const Container = <T extends string>({
  children,
  label,
  onChange,
  value,
  ...rest
}: Props<T>) => {
  const state = useRadioGroupState(rest);

  useEffect(() => {
    state.setSelectedValue(value);
  }, [value]);

  useEffect(() => {
    if (value !== state.selectedValue) {
      onChange(state.selectedValue as T);
    }
  }, [state.selectedValue]);

  const { radioGroupProps } = useRadioGroup(
    {
      ...rest,
      label,
      orientation: 'horizontal',
    },
    state
  );

  return (
    <ButtonContext.Provider value={state}>
      <HorizontalStack {...radioGroupProps} className="gap-sm">
        {children}{' '}
      </HorizontalStack>
    </ButtonContext.Provider>
  );
};

type RadioProps = AriaRadioProps;

const Button = forwardRef<{ focus: () => void }, RadioProps>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const state = useContext(ButtonContext);

  invariant(state, 'ButtonGroup must be used within a ButtonGroup.Container');

  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
  }));

  const { inputProps } = useRadio(props, state, inputRef);
  const { focusProps, isFocusVisible } = useFocusRing();
  const isSelected = state?.selectedValue === props.value;

  return (
    <label>
      <VisuallyHidden>
        <input {...mergeProps(inputProps, focusProps)} ref={inputRef} />
      </VisuallyHidden>
      <HorizontalStack
        itemsAlign="center"
        className={classNames(
          'py-sm px-lg gap-sm border border-solid border-secondary rounded-md',
          {
            'bg-secondary': isSelected,
            'text-on-primary': isSelected,
            'pl-sm+1': isSelected,
            'border-secondary+5': isFocusVisible,
            'scale-110': isFocusVisible,
          }
        )}
        role="button"
      >
        {isSelected && <FaCheck className="text-on-primary" />}
        {props.children}
      </HorizontalStack>
    </label>
  );
});

export const ButtonGroup = {
  Container,
  Button,
};
