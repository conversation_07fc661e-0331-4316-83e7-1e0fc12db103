import { AllHTMLAttributes, PropsWithChildren } from 'react';

import classNames from '~/utils/css';
import { Box, Props as BoxProps } from './Box';

type NativeHtmlAttributes = Pick<
  AllHTMLAttributes<SupportedElements>,
  'className' | 'style' | 'contentEditable' | 'onInput' | 'role'
>;

type SupportedElementsMap = Pick<
  HTMLElementTagNameMap,
  'label' | 'p' | 'span' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'legend'
>;

type SupportedElements = SupportedElementsMap[keyof SupportedElementsMap];
type SupportedTags = keyof SupportedElementsMap;

type Props<T extends SupportedTags> = PropsWithChildren<{
  as?: T;
  size?: SupportedTags;
  className?: string;
  serif?: boolean;
}> &
  BoxProps<T> &
  NativeHtmlAttributes;

export const Typography = <T extends SupportedTags>({
  children,
  size,
  as = 'span' as T,
  className,
  serif,
  ref,
  ...rest
}: Props<T>) => {
  const sizeOverride = size ?? as;

  return (
    <Box
      {...rest}
      as={as}
      ref={ref}
      className={classNames(
        {
          'text-4xl': sizeOverride === 'h1',
          'text-3xl': sizeOverride === 'h2',
          'text-2xl': sizeOverride === 'h3',
          'text-xl': sizeOverride === 'h4',
          'text-lg': sizeOverride === 'h5',
          'font-serif': !!serif,
        },
        className
      )}
    >
      {children}
    </Box>
  );
};
