import {
  forwardRef,
  HTMLAttributes,
  ReactNode,
  useImperativeHandle,
  useRef,
} from 'react';

import './NativePopover.css';

type TriggerProps = {
  popovertarget: string;
  id: string;
  style: HTMLAttributes<HTMLElement>['style'];
  className: string;
};

type ControlProps = {
  close: () => void;
  open: () => void;
};

type Props = {
  children: ReactNode | ((props: ControlProps) => ReactNode);
  renderTrigger: (props: TriggerProps) => ReactNode;
  uniqueId: string;
};

export const NativePopover = forwardRef<ControlProps, Props>(
  ({ children, renderTrigger, uniqueId }, ref) => {
    const popoverRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(ref, () => ({
      close: () => {
        return popoverRef.current?.hidePopover();
      },
      open: () => {
        return popoverRef.current?.showPopover();
      },
    }));

    return (
      <>
        {renderTrigger({
          popovertarget: `awe-popover_${uniqueId}`,
          /* @ts-expect-error-line */
          style: { '--anchor-name': `--anchor_${uniqueId}` },
          className: 'awe-anchor',
          id: `awe-anchor_${uniqueId}`,
        })}

        <div
          popover=""
          id={`awe-popover_${uniqueId}`}
          className="awe-popover"
          ref={popoverRef}
          style={{
            /* @ts-expect-error-line */
            '--position-anchor': `--anchor_${uniqueId}`,
          }}
          role="menu"
          aria-labelledby={`awe-anchor_${uniqueId}`}
        >
          {typeof children === 'function'
            ? children({
                close: () => {
                  if (typeof popoverRef.current?.hidePopover === 'function') {
                    popoverRef.current?.hidePopover();
                  }
                },
                open: () => {
                  if (typeof popoverRef.current?.showPopover === 'function') {
                    popoverRef.current?.showPopover();
                  }
                },
              })
            : children}
        </div>
      </>
    );
  }
);
