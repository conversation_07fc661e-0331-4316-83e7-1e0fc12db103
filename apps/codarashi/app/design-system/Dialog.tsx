import {
  forwardRef,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';

import classNames from '~/utils/css';

type ControlProps<T = unknown> = {
  openDialog?: (modal: boolean) => void;
  closeDialog?: () => T;
  isOpen: boolean;
};

type Props = {
  mode: 'dialog' | 'modal';
  open?: boolean;
  children: ReactNode | ((props: ControlProps) => ReactNode);
};

export const Dialog = forwardRef<ControlProps, Props>((props, ref) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  const controlProps = {
    openDialog: (isModal: boolean) =>
      isModal ? dialogRef.current?.showModal() : dialogRef.current?.show(),
    closeDialog: () => dialogRef.current?.close(),
    isOpen: !!dialogRef.current?.open,
  } as const;

  useImperativeHandle(ref, () => controlProps);

  useEffect(() => {
    if (props.mode === 'dialog') {
      props.open ? controlProps.openDialog(true) : controlProps.closeDialog();
    }
  }, [props.open]);

  return (
    <dialog
      /*  use prop based opening only for dialog mode, modal will throw error */
      open={props.mode === 'dialog' ? props.open : undefined}
      data-open={props.open ? true : undefined}
      ref={dialogRef}
      className={classNames({
        'w-[400px] h-[400px] flex flex-col': true,
        'bg-white border border-gray-100 rounded-lg shadow-lg p-6': true,
      })}
    >
      {typeof props.children === 'function'
        ? props.children(controlProps)
        : props.children}
    </dialog>
  );
});
