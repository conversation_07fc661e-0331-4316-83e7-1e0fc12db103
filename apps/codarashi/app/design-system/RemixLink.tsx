import { Link, LinkProps, NavLink, NavLinkProps } from '@remix-run/react';
import { forwardRef } from 'react';

import classNames from '~/utils/css';
import { linkStyling } from './Link';

export const RemixLink = forwardRef<typeof Link, LinkProps>((props, ref) => {
  return (
    <Link
      {...props}
      className={classNames(linkStyling, props.className)}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ref={ref as any}
    >
      {props.children}
    </Link>
  );
});

export const RemixNavLink = forwardRef<typeof NavLink, NavLinkProps>(
  (props, ref) => {
    return (
      <NavLink
        {...props}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={ref as any}
        className={(args) => {
          return classNames(
            linkStyling,
            typeof props.className === 'function'
              ? props.className(args)
              : props.className
          );
        }}
      >
        {props.children}
      </NavLink>
    );
  }
);
