import { GenericRecord } from '@awe/core';
import {
  arrow,
  autoUpdate,
  flip,
  FloatingArrow,
  FloatingPortal,
  offset,
  Placement,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useFocus,
  useHover,
  useInteractions,
  useListNavigation,
  useRole,
} from '@floating-ui/react';
import { useEffect, useRef, useState } from 'react';

type Props<T extends GenericRecord | string> = {
  renderTrigger: (props: TriggerRenderProps) => React.ReactElement;
  items: T[];
  renderItem: (data: ItemRenderProps<T>) => React.ReactElement;
  interactions: Interactions;
  className?: string;
  placement?: Placement;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  modal?: boolean;
  showArrow?: boolean;
};

export const Menu = <T extends GenericRecord | string>({
  renderTrigger,
  interactions,
  isOpen: open,
  onOpenChange,
  className,
  placement = 'top',
  showArrow = true,
  items,
  renderItem,
}: Props<T>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState<null | number>(0);

  useEffect(() => {
    setIsOpen(!!open);
  }, [open]);

  const arrowRef = useRef(null);
  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (isOpened) => {
      setIsOpen(isOpened);
      onOpenChange?.(isOpened);
    },
    placement,
    middleware: [
      offset(10),
      flip(),
      shift(),
      showArrow &&
        arrow({
          element: arrowRef,
        }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const listRef = useRef<Array<HTMLDivElement | null>>([]);

  const listNavigation = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
    virtual: true,
    orientation: 'horizontal',
    openOnArrowKeyDown: false,
  });

  const click = useClick(context, {
    enabled: !!interactions.click,
  });
  const dismiss = useDismiss(context, {
    enabled: !!interactions.dismiss,
  });
  const role = useRole(context);
  const hover = useHover(context, {
    enabled: !!interactions.hover,
    delay: 300,
  });
  const focus = useFocus(context, {
    enabled: !!interactions.focus,
  });

  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions(
    [click, dismiss, hover, focus, role, listNavigation]
  );

  return (
    <>
      {renderTrigger({ ref: refs.setReference, getProps: getReferenceProps })}
      {isOpen && (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className={className}
          >
            {showArrow && <FloatingArrow ref={arrowRef} context={context} />}

            {items.map((item, index) => (
              <div
                key={index}
                tabIndex={activeIndex === index ? 0 : -1}
                ref={(node) => {
                  listRef.current[index] = node;
                }}
                {...getItemProps()}
                className="outline-primary"
              >
                {renderItem({ data: item, active: activeIndex === index })}
              </div>
            ))}
          </div>
        </FloatingPortal>
      )}
    </>
  );
};

type ItemRenderProps<T> = {
  data: T;
  active: boolean;
};

type TriggerRenderProps = {
  ref: (node: HTMLElement | null) => void;
  getProps: () => Record<string, unknown>;
};

type Interactions = Partial<{
  click: boolean;
  dismiss: boolean;
  hover: boolean;
  focus: boolean;
}>;
