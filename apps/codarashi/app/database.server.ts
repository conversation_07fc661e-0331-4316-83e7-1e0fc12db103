import { Client } from '@libsql/client';
import { LibSQLDatabase } from 'drizzle-orm/libsql';

import { serverEnv } from './env.server';
import * as schema from './schema';
import { createDb } from './utils/database';

let _db:
  | (LibSQLDatabase<typeof schema> & {
      $client: Client;
    })
  | null = null;

const createRealDb = () => {
  if (_db) {
    return _db;
  }

  _db = createDb(serverEnv.DB_HOST_TURSO);

  return _db;
};

export const db = createRealDb();

export type DbOrTx = Omit<LibSQLDatabase<typeof schema>, 'batch' | '$client'>;

export const cleanDatabase = async () => {
  // Get all tables except SQLite system tables and Drizzle migration tables
  const tables = await db.$client.execute(`
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE '__drizzle_%';
  `);

  // Disable foreign key checks temporarily
  await db.$client.execute('PRAGMA foreign_keys=OFF;');

  try {
    // Delete data from each table
    for (const { name } of tables.rows) {
      await db.$client.execute(`DELETE FROM "${name}";`);
    }
  } finally {
    // Re-enable foreign key checks
    await db.$client.execute('PRAGMA foreign_keys=ON;');
  }
};
