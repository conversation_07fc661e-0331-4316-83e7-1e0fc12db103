import { TextField } from '~/design-system/Input';
import {
  HeroButton,
  HorizontalStack,
  Link,
  PrimaryButton,
  RemixLink,
  RemixNavLink,
  SecondaryButton,
  TertiaryButton,
  Typography,
} from '../../design-system';

export const loader = async () => {
  return {};
};

export default function Index() {
  return (
    <HorizontalStack className="m-lg flex-wrap" gap="md">
      <HeroButton>Hero button</HeroButton>

      <PrimaryButton>Primary button</PrimaryButton>

      <SecondaryButton>Secondary button</SecondaryButton>

      <TertiaryButton>Tertiary button</TertiaryButton>

      <Typography as="h1">Heading 1</Typography>

      <Typography as="h1" serif>
        Heading 1 serif
      </Typography>

      <Typography as="h1" size="h5">
        Small Heading 1
      </Typography>

      <Typography as="h2">Heading 2</Typography>

      <Typography as="h3">Heading 3</Typography>

      <Typography as="h4">Heading 4</Typography>

      <Typography as="h5">Heading 5</Typography>

      <Typography as="p">Regular</Typography>

      <Link href="https://abv.bg">Link</Link>

      <RemixLink to="/pesho">Remix link</RemixLink>

      <RemixNavLink to="/pesho">Remix Nav link</RemixNavLink>

      <TextField label="Content" />
    </HorizontalStack>
  );
}
