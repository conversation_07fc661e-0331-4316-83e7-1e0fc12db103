import { StringOfLength } from '@awe/core';
import { z } from 'zod';

export enum Type {
  Web = 'web',
  Mobile = 'mobile',
  DevOps = 'devops',
  DataScience = 'data-science',
  AI = 'ai',
  Infrasructure = 'infrasructure',
  Cybersecurity = 'cybersecurity',
  Blockchain = 'blockchain',
  Game = 'game',
  ARVR = 'ar-vr',
  IoT = 'iot',
  Desktop = 'desktop',
  Embedded = 'embedded',

  Other = 'other',
}

const baseParser = z.object({
  name: z.union([
    z.literal(Type.Web),
    z.literal(Type.Mobile),
    z.literal(Type.DevOps),
    z.literal(Type.DataScience),
    z.literal(Type.AI),
    z.literal(Type.Infrasructure),
    z.literal(Type.Cybersecurity),
    z.literal(Type.Blockchain),
    z.literal(Type.Game),
    z.literal(Type.ARVR),
    z.literal(Type.IoT),
    z.literal(Type.Desktop),
    z.literal(Type.Embedded),
  ]),
});

const otherParser = z.object({
  name: z.literal(Type.Other),
  value: StringOfLength.parser(1, 50),
});

export const parser = z.union([baseParser, otherParser]);

export type Specialization = z.infer<typeof parser>;
