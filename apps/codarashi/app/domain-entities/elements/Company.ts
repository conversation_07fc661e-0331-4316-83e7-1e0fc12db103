import { z } from 'zod';

import { NanoId, Numbers, StringOfLength } from '@awe/core';
import { parser as industryParser } from './Industry';

export const id = NanoId.parser('cmp');

export type CompanyId = z.infer<typeof id>;

export const parser = z.object({
  id,
  name: StringOfLength.parser(1, 50),
  url: StringOfLength.parser(1, 50),

  industry: industryParser,
  employee_count: Numbers.positiveIntegerParser,
});
