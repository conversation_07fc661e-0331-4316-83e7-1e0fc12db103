import { z } from 'zod';

import { Dates, NanoId, StringOfLength } from '@awe/core';
import { id as tagParser } from '../../modules/profile/shared:tags/Tag';
import { parser as blockParser } from '../Block';

export const id = NanoId.parser('cstm');

export type CustomEntryId = z.infer<typeof id>;

export const parser = z.object({
  id,
  title: StringOfLength.parser(1, 50),
  date_start: Dates.dateParser.optional(),
  date_end: Dates.dateParser.optional(),
  blocks: z.array(blockParser),
  tags: z.array(tagParser),
});

export type CustomEntry = z.infer<typeof parser>;
