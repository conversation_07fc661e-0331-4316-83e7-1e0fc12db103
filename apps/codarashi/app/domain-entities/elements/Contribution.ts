import { z } from 'zod';

import { CustomUrl, Dates, NanoId, StringOfLength } from '@awe/core';
import { id as tagParser } from '../../modules/profile/shared:tags/Tag';
import { parser as blockParser } from '../Block';

export const id = NanoId.parser('cntrb');

export type ContributionId = z.infer<typeof id>;

export const parser = z.object({
  id,
  title: StringOfLength.parser(1, 50),
  url: CustomUrl.parser(),
  date_start: Dates.dateParser,
  date_end: Dates.dateParser.optional(),
  blocks: z.array(blockParser),
  tags: z.array(tagParser),
});

export type Contribution = z.infer<typeof parser>;
