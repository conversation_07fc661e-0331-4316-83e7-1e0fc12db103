import { z } from 'zod';

import { StringOfLength } from '@awe/core';

export enum Type {
  FinTech = 'fin-tech',
  HealthTech = 'health-tech',
  EduTech = 'edu-tech',
  ECommerce = 'e-commerce',
  Cybersecurity = 'cybersecurity',
  Gaming = 'gaming',
  CloudComputing = 'cloud-computing',
  DataAnalytics = 'data-analytics',
  AIAndMachineLearning = 'ai-and-machine-learning',
  IoT = 'iot',
  Blockchain = 'blockchain',
  Automotive = 'automotive',
  Energy = 'energy',
  AgricultureTech = 'agriculture-tech',
  RealEstateTech = 'real-estate-tech',
  ManufacturingTech = 'manufacturing-tech',
  MediaAndEntertainment = 'media-and-entertainment',
  SocialMedia = 'social-media',
  LogisticsAndSupplyChain = 'logistics-and-supply-chain',
  HumanResourcesTech = 'human-resources-tech',
  EnvironmentalTech = 'environmental-tech',
  Aerospace = 'aerospace',
  DefenseTech = 'defense-tech',
  Telecommunications = 'telecommunications',
  RetailTech = 'retail-tech',
  SportsTech = 'sports-tech',
  TourismAndTravelTech = 'tourism-and-travel-tech',
  FoodTech = 'food-tech',
  LegalTech = 'legal-tech',
  InsuranceTech = 'insurance-tech',

  Other = 'other',
}

const baseParser = z.object({
  name: z.enum([
    Type.FinTech,
    Type.HealthTech,
    Type.EduTech,
    Type.ECommerce,
    Type.Cybersecurity,
    Type.Gaming,
    Type.CloudComputing,
    Type.DataAnalytics,
    Type.AIAndMachineLearning,
    Type.IoT,
    Type.Blockchain,
    Type.Automotive,
    Type.Energy,
    Type.AgricultureTech,
    Type.RealEstateTech,
    Type.ManufacturingTech,
    Type.MediaAndEntertainment,
    Type.SocialMedia,
    Type.LogisticsAndSupplyChain,
    Type.HumanResourcesTech,
    Type.EnvironmentalTech,
    Type.Aerospace,
    Type.DefenseTech,
    Type.Telecommunications,
    Type.RetailTech,
    Type.SportsTech,
    Type.TourismAndTravelTech,
    Type.FoodTech,
    Type.LegalTech,
    Type.InsuranceTech,
  ]),
});

const otherParser = z.object({
  name: z.literal(Type.Other),
  value: StringOfLength.parser(1, 50),
});

export const parser = z.union([baseParser, otherParser]);

export type Industry = z.infer<typeof parser>;
