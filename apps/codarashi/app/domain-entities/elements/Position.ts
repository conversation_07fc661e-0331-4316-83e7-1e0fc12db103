import { z } from 'zod';

import { Dates, NanoId, StringOfLength } from '@awe/core';
import { id as techId } from './Tech';
import { id as skillId } from './Skill';
import { id as projectId } from './Project';
import { id as companyId } from './Company';
import { parser as blockParser } from '../Block';
import { parser as seniority } from './Seniority';
import { parser as specializationParser } from './Specialization';

export const id = NanoId.parser('pos');

export type PositionId = z.infer<typeof id>;

export const parser = z.object({
  id,
  title: StringOfLength.parser(1, 50),
  seniority,
  company: companyId,
  specializations: z.array(specializationParser),
  location: StringOfLength.parser(1, 50).optional(),
  date_start: Dates.dateParser,
  date_end: Dates.dateParser.optional(),
  tech: z.array(techId),
  skills: z.array(skillId),
  projects: z.array(projectId),
  blocks: z.array(blockParser),
});
