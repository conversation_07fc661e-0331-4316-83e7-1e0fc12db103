import { z } from 'zod';

import { NanoId, StringOfLength } from '@awe/core';
import { id as tagParser } from '../../modules/profile/shared:tags/Tag';
import { parser as blockParser } from '../Block';

export const id = NanoId.parser('prj');

export type ProjectId = z.infer<typeof id>;

export const parser = z.object({
  id,
  name: StringOfLength.parser(1, 50),
  blocks: z.array(blockParser),
  tags: z.array(tagParser),
});
