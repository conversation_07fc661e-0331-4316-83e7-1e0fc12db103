import { z } from 'zod';

import { StringOfLength } from '@awe/core';

export enum Type {
  Junior = 'junior',
  Middle = 'middle',
  Senior = 'senior',
  Lead = 'lead',
  Cofounder = 'cofounder',
  CTO = 'cto',
  Staff = 'staff',
  Principal = 'principal',
  Architect = 'architect',
  Manager = 'manager',
  Other = 'other',
}

const baseParser = z.object({
  name: z.union([
    z.literal(Type.Junior),
    z.literal(Type.Middle),
    z.literal(Type.Senior),
    z.literal(Type.Lead),
    z.literal(Type.Cofounder),
    z.literal(Type.CTO),
    z.literal(Type.Staff),
    z.literal(Type.Principal),
    z.literal(Type.Architect),
    z.literal(Type.Manager),
  ]),
});

const otherParser = z.object({
  name: z.literal(Type.Other),
  value: StringOfLength.parser(1, 50),
});

export const parser = z.union([baseParser, otherParser]);

export type PersonSeniority = z.infer<typeof parser>;
