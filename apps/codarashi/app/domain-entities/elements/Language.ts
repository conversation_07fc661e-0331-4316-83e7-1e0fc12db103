import { z } from 'zod';

import { NanoId, StringOfLength } from '@awe/core';
import { id as tagParser } from '../../modules/profile/shared:tags/Tag';

export const id = NanoId.parser('lang');

export type LanguageId = z.infer<typeof id>;

export enum Type {
  Both = 'both',
  Spoken = 'spoken',
  Written = 'written',
}

export const parser = z.object({
  id,
  name: StringOfLength.parser(1, 50),
  type: z.nativeEnum(Type),
  tags: z.array(tagParser),
});

export type Course = z.infer<typeof parser>;
