import { z } from 'zod';

import { NanoId, StringOfLength } from '@awe/core';
import * as Tag from '../modules/profile/shared:tags/Tag';

const id = NanoId.parser('sctn_blk');

export const generateId = () => NanoId.generate('sctn_blk');

export type BlockId = z.infer<typeof id>;

export enum BlockType {
  Paragraph = 'paragraph',
  Bullet = 'bullet',
  Dash = 'dash',
}

export const parser = z.object({
  id,
  type: z.nativeEnum(BlockType),
  value: StringOfLength.parser(1, 500),

  tags: z.array(Tag.id),
});
