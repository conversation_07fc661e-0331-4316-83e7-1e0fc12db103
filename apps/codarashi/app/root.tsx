import type { MetaFunction, LinksFunction } from '@remix-run/node';
import { cssBundleHref } from '@remix-run/css-bundle';
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from '@remix-run/react';

import '@fontsource/merriweather/400.css';
import '@fontsource/merriweather/700.css';
import '@fontsource-variable/jetbrains-mono/index.css';

import globalStyles from './global.css';
import { styleSheet } from './design-system/css-hooks';
import { AppContainer } from './components/app-container';

export const meta: MetaFunction = () => [
  {
    charset: 'utf-8',
    title: 'Codarashi',
    viewport: 'width=device-width,initial-scale=1',
  },
];

export const links: LinksFunction = () => [
  ...(cssBundleHref ? [{ rel: 'stylesheet', href: cssBundleHref }] : []),
  { rel: 'stylesheet', href: globalStyles as string },
];

export default function App() {
  return (
    <html lang="en">
      <head>
        <Meta />
        <Links />
        <style dangerouslySetInnerHTML={{ __html: styleSheet() }} />
      </head>
      <body className="text-regular text-base">
        <AppContainer>
          <Outlet />
        </AppContainer>

        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
