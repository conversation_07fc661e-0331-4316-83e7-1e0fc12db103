{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2019"
    ],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "target": "ES2019",
    "strict": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "paths": {
      "~/*": [
        "./apps/codarashi/app/*",
      ],
      "@awe/core": [
        "libs/core/src/index.ts"
      ],
    },
  },
  "include": [],
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ]
}