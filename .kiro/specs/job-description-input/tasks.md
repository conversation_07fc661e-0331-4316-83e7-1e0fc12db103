# Implementation Plan

- [x] 1. Extract resume logic into ResumeSection component

  - Create `apps/codashi/app/components/ResumeSection.tsx` with existing resume upload and processing logic
  - Move PDF upload handling, file validation, and ProfileExtractionFlow integration from home.tsx
  - Implement `onProfileReady` callback prop to communicate with parent component
  - _Requirements: 4.2, 4.3_

- [x] 2. Create JobExtractionFlow component for progress reporting

  - Create `apps/codashi/app/components/JobExtractionFlow.tsx` following ProfileExtractionFlow patterns
  - Implement streaming job extraction progress with section status indicators
  - Handle job extraction stream events (sections_identified, extraction_result, final_result)
  - Add error handling and loading states with consistent styling
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 3. Create JobSection component for job description input

  - Create `apps/codashi/app/components/JobSection.tsx` with dual input methods (PDF upload and text paste)
  - Implement PDF file upload with validation and text extraction using existing `extractTextWithFallback`
  - Add textarea for plain text input with proper validation
  - Integrate JobExtractionFlow component for progress reporting
  - Implement API call to `/api/job-extraction` with FormData
  - Add error handling for invalid inputs and API failures
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 4. Create JobDisplay component for extracted job data

  - Create `apps/codashi/app/components/JobDisplay.tsx` to show stringified job data
  - Format JobDraft data as readable JSON with proper indentation
  - Add error handling for malformed job data
  - Style component to match existing design patterns
  - _Requirements: 3.1, 3.2, 3.4, 3.5_

- [x] 5. Update home.tsx to integrate new components
  - Import and integrate ResumeSection and JobSection components
  - Update state management to handle both resume and job data
  - Modify layout to accommodate both resume and job sections in left column
  - Update right column to display both ResumePdfPreview and JobDisplay when available
  - Update page title and meta description to reflect dual functionality
  - Remove unused code and imports after component extraction
  - _Requirements: 3.3, 4.1, 4.4, 4.5_
