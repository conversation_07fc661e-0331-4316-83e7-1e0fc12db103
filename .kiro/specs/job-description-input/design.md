# Design Document

## Overview

This feature adds job description input capabilities to the existing resume parser application. The design follows the established patterns for PDF processing, AI extraction, and progress reporting while maintaining clean separation of concerns through dedicated components. The implementation leverages the existing job extraction API and creates reusable components for both resume and job processing workflows.

## Architecture

### Component Structure

```
apps/codashi/app/routes/home.tsx (main orchestrator)
├── ResumeSection.tsx (extracted resume logic)
├── JobSection.tsx (new job processing logic)
└── components/
    ├── profile.flow.tsx (existing resume progress)
    └── job.flow.tsx (new job progress component)
```

### Data Flow

1. **Input Phase**: User uploads PDF or pastes text in JobSection
2. **Processing Phase**: Text extraction (if PDF) → API call to `/api/job-extraction`
3. **Progress Phase**: JobExtractionFlow component handles streaming updates
4. **Display Phase**: Structured job data displayed as formatted JSON

### API Integration

- Leverages existing `/api/job-extraction.api.ts` endpoint
- Follows same streaming pattern as resume extraction
- Uses FormData for consistent API interface

## Components and Interfaces

### ResumeSection Component

```typescript
interface ResumeSectionProps {
  onProfileReady: (profile: Resume) => void;
}

// Extracted from existing home.tsx logic
// Handles PDF upload, text extraction, and ProfileExtractionFlow
```

### JobSection Component

```typescript
interface JobSectionProps {
  onJobReady: (job: JobDraft) => void;
}

// New component for job description input
// Supports both PDF upload and text paste
// Integrates with JobExtractionFlow for progress
```

### JobExtractionFlow Component

```typescript
interface JobExtractionFlowProps {
  jobText: string;
  onJobReady: (job: JobDraft) => void;
}

// Mirrors ProfileExtractionFlow pattern
// Handles streaming job extraction progress
// Displays section-by-section extraction status
```

### Home Page Layout

```typescript
// Updated home.tsx structure
export default function Home() {
  const [profile, setProfile] = useState<Resume | null>(null);
  const [job, setJob] = useState<JobDraft | null>(null);

  return (
    <main className="grid-container p-4 full-height">
      <section className="grid-span-7 flex flex-col gap-8">
        <h1>Resume & Job Parser</h1>
        <ResumeSection onProfileReady={setProfile} />
        <JobSection onJobReady={setJob} />
      </section>

      <section className="grid-span-5">
        {profile && <ResumePdfPreview profile={profile} />}
        {job && <JobDisplay job={job} />}
      </section>
    </main>
  );
}
```

## Data Models

### Job Input Types

```typescript
type JobInputMethod = 'pdf' | 'text';

interface JobInput {
  method: JobInputMethod;
  content: string; // extracted text or pasted text
  fileName?: string; // for PDF uploads
}
```

### Job Display Format

```typescript
interface JobDisplayProps {
  job: JobDraft;
}

// Displays stringified JSON with proper formatting
// Includes error handling for malformed data
```

### Stream Event Types

```typescript
// Reuses existing JobExtractionStreamEvent from job.ai-extractor.ts
type JobExtractionStreamEvent = { type: 'sections_identified'; sections: JobSection[] } | { type: 'extraction_result'; section: string; data: Partial<JobDraft> } | { type: 'final_result'; job: JobDraft };
```

## Error Handling

### Input Validation

- PDF file type validation (application/pdf)
- Text content validation (non-empty, reasonable length)
- File size limits for PDF uploads
- Error messages with consistent styling

### Extraction Errors

- Network timeout handling
- API error response handling
- Malformed stream data handling
- Graceful degradation for partial failures

### User Feedback

- Loading states during processing
- Progress indicators for long operations
- Clear error messages with actionable guidance
- Success confirmations

## Testing Strategy

Testing will be handled separately and is not part of this implementation scope.

## Implementation Notes

### PDF Processing

- Reuses existing `extractTextWithFallback` function
- Maintains consistent error handling patterns
- Supports same file validation logic

### State Management

- Uses React useState for component-level state
- Maintains separation between resume and job state
- Follows existing patterns from ProfileExtractionFlow

### Styling Consistency

- Reuses existing CSS classes and patterns
- Maintains grid layout structure
- Follows established design system

### Performance Considerations

- Lazy loading of extraction components
- Proper cleanup of streaming connections
- Memory management for large PDF files
- Debounced text input for paste functionality
