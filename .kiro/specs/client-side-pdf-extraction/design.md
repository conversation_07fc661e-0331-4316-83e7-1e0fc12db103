# Design Document

## Overview

This design refactors the PDF text extraction functionality in ResumeSection and JobSection components to process files directly in the browser using client-side JavaScript. The refactoring will eliminate server actions for PDF processing while maintaining the same user experience and functionality.

## Architecture

### Current Architecture

- PDF files are uploaded via React Router forms
- Server actions (resumeAction, jobAction) handle PDF processing
- Text extraction happens on the server using extractTextWithFallback
- Results are returned via actionData and displayed in extraction flows

### New Architecture

- PDF files are processed directly in file input change handlers
- Client-side state management replaces server action data
- Text extraction happens in the browser using extractTextWithFallback
- Results are stored in component state and displayed immediately

## Components and Interfaces

### ResumeSection Component Changes

**State Management:**

```typescript
interface ResumeState {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
}
```

**Key Methods:**

- `handleFileChange`: Process PDF file directly, extract text, update state
- `resetState`: Clear current state when new file is selected

### JobSection Component Changes

**State Management:**

```typescript
interface JobState {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
  inputMethod: 'pdf' | 'text';
  textInput: string;
}
```

**Key Methods:**

- `handleFileChange`: Process PDF file directly for PDF input method
- `handleTextSubmit`: Handle text input submission (unchanged logic)
- `resetState`: Clear current state when switching input methods or selecting new files

### Shared Processing Logic

Both components will use similar PDF processing logic:

1. Validate file type and size
2. Show loading state
3. Extract text using extractTextWithFallback
4. Handle success/error states
5. Display appropriate UI based on state

## Data Models

### Component State Models

```typescript
// ResumeSection state
type ResumeProcessingState = {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
};

// JobSection state (extends existing state)
type JobProcessingState = {
  text: string | null;
  error: string | null;
  isProcessing: boolean;
  inputMethod: 'pdf' | 'text';
  textInput: string;
};
```

### File Processing Flow

```typescript
type FileProcessingResult = {
  success: boolean;
  text?: string;
  error?: string;
};
```

## Error Handling

### Client-Side Error Categories

1. **File Validation Errors**

   - Invalid file type (not PDF)
   - Empty file
   - File too large

2. **Processing Errors**

   - PDF parsing failures
   - Empty text extraction
   - Browser compatibility issues

3. **Network/Resource Errors**
   - Memory limitations
   - Browser security restrictions

### Error Display Strategy

- Use existing error display components (red background divs)
- Provide specific, actionable error messages
- Clear errors when new files are selected
- Maintain error message consistency with current implementation

## Implementation Approach

### Phase 1: ResumeSection Refactoring

1. Add state management for processing
2. Refactor handleFileChange to process files directly
3. Remove server action dependencies
4. Update error handling and display

### Phase 2: JobSection Refactoring

1. Add state management for PDF processing
2. Refactor PDF handling in handleFileChange
3. Maintain existing text input functionality
4. Remove PDF-related server action code
5. Update error handling and display

### Phase 3: Cleanup

1. Remove unused imports (React Router action-related)
2. Remove unused server action functions
3. Update any route configurations if needed
4. Verify all functionality works as expected
