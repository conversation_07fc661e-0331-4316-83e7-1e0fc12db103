# Design Document

## Overview

The skill matching feature will provide intelligent analysis of resume variations against job postings to identify direct skill matches and transferable skills with confidence ratings. The system will consolidate skills from multiple resume variations, perform efficient AI-powered similarity analysis, and return structured results that help understand skill alignment.

The feature will be implemented as a new module in `libs/codashi-core/src/skill-matching/` and will integrate with the existing LangChain + Mistral AI infrastructure used throughout the codebase.

## Architecture

### Core Components

1. **Skill Extractor**: Consolidates and normalizes skills from multiple resume variations
2. **Direct Match Analyzer**: Performs exact and synonym-based matching
3. **Transferable Skill Analyzer**: Uses AI reasoning to identify similar skills with confidence ratings
4. **Result Aggregator**: Combines results into a structured output format

### Data Flow

```mermaid
graph TD
    A[Resume Variations + Job] --> B[Skill Extractor]
    B --> C[Consolidated Skills]
    C --> D[Direct Match Analyzer]
    C --> E[Transferable Skill Analyzer]
    D --> F[Direct Matches]
    E --> G[Transferable Skills with Confidence]
    F --> H[Result Aggregator]
    G --> H
    H --> I[Skill Match Analysis]
```

### Integration Points

- **LangChain Integration**: Uses `BaseChatModel`, `StructuredOutputParser`, `ChatPromptTemplate`, and `RunnableSequence` following existing patterns
- **Type System**: Integrates with existing `Resume` and `Job` types from `entities/`
- **Error Handling**: Follows existing error handling patterns with graceful degradation

## Components and Interfaces

### Main Function Interface

```typescript
export interface SkillMatchAnalysis {
  directMatches: DirectSkillMatch[];
  transferableSkills: TransferableSkillMatch[];
  summary: {
    totalJobSkills: number;
    directMatchCount: number;
    transferableMatchCount: number;
    coveragePercentage: number;
  };
}

export interface DirectSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  matchType: 'exact' | 'synonym' | 'keyword';
  sourceResume?: number; // Optional for introspection
}

export interface TransferableSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  confidenceRating: 1 | 2 | 3;
  reasoning: string;
  sourceResume?: number; // Optional for introspection
}

export async function analyzeSkillMatch(resumeVariations: Resume[], job: Job, model: BaseChatModel, options?: SkillMatchOptions): Promise<SkillMatchAnalysis>;
```

### Supporting Interfaces

```typescript
export interface SkillMatchOptions {
  includeSourceResume?: boolean; // For introspection
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
}

interface ConsolidatedSkill {
  name: string;
  level?: string;
  keywords: string[];
  sourceResumes: number[]; // Track which resumes contributed this skill
}
```

### Internal Components

#### 1. Skill Consolidator

```typescript
class SkillConsolidator {
  consolidate(resumeVariations: Resume[]): ConsolidatedSkill[];
  private normalizeSkillName(skillName: string): string;
  private mergeSimilarSkills(skills: ConsolidatedSkill[]): ConsolidatedSkill[];
}
```

#### 2. Direct Match Analyzer

```typescript
class DirectMatchAnalyzer {
  constructor(private model: BaseChatModel);

  async findDirectMatches(consolidatedSkills: ConsolidatedSkill[], jobSkills: Job['skills']): Promise<DirectSkillMatch[]>;

  private isExactMatch(resumeSkill: string, jobSkill: string): boolean;
  private async isSynonymMatch(resumeSkill: string, jobSkill: string): Promise<boolean>;
  private isKeywordMatch(resumeSkill: ConsolidatedSkill, jobSkill: Job['skills'][0]): boolean;
}
```

#### 3. Transferable Skill Analyzer

```typescript
class TransferableSkillAnalyzer {
  constructor(private model: BaseChatModel);

  async analyzeTransferableSkills(unmatchedResumeSkills: ConsolidatedSkill[], unmatchedJobSkills: Job['skills'], options: SkillMatchOptions): Promise<TransferableSkillMatch[]>;

  private createTransferabilityPrompt(): ChatPromptTemplate;
  private batchSkillComparisons(skills: SkillComparison[]): SkillComparison[][];
}
```

## Data Models

### AI-Powered Synonym Detection

Instead of maintaining a static synonym dictionary, the system will use AI to detect skill synonyms dynamically. This approach provides better coverage and adapts to new technologies and naming conventions automatically.

```typescript
const synonymDetectionSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      isSynonym: z.boolean(),
      reasoning: z.string().optional(),
    })
  ),
});
```

### AI Prompt Schema

```typescript
const transferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});
```

## Error Handling

### Graceful Degradation Strategy

1. **AI Model Unavailable**: Fall back to direct matching only
2. **Partial AI Failures**: Return partial results with warnings
3. **Invalid Input**: Validate inputs and provide clear error messages
4. **Timeout Handling**: Implement reasonable timeouts for AI calls

### Error Types

```typescript
export class SkillMatchError extends Error {
  constructor(message: string, public readonly code: 'AI_UNAVAILABLE' | 'TIMEOUT' | 'INVALID_INPUT', public readonly partialResults?: Partial<SkillMatchAnalysis>) {
    super(message);
  }
}
```

## Testing Strategy

**Note**: Comprehensive testing strategy and AI evaluation framework will be researched and implemented as a separate feature. For initial implementation, focus on basic functionality validation and manual testing with sample data.

## Performance Considerations

### Optimization Strategies

1. **Skill Deduplication**: Remove duplicate skills before AI analysis
2. **Batch AI Requests**: Group similar skill comparisons together
3. **Caching**: Cache AI results for identical skill pairs
4. **Early Termination**: Stop processing when confidence thresholds are met

### Expected Performance

- **Direct Matching**: < 100ms for typical resume/job combinations
- **AI Analysis**: < 10 seconds for 20-30 skill comparisons
- **Total Processing**: < 30 seconds for 5-10 resume variations

### Scalability Considerations

- Implement request queuing for high-volume usage
- Consider async processing for large batches
- Monitor AI token usage and implement rate limiting
