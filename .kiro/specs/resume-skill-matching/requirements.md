# Requirements Document

## Introduction

This feature will provide intelligent skill matching between resume variations and job postings using AI reasoning. The system will analyze multiple resume variations for the same person against a single job posting to create a comprehensive skill match analysis. It will identify direct skill matches and similar/transferable skills with confidence ratings, helping candidates understand their overall skill alignment and areas where related experience can be leveraged.

## Requirements

### Requirement 1

**User Story:** As a candidate or recruiter, I want to analyze multiple resume variations for the same person against a job posting to get a comprehensive view of direct skill matches, so that I can understand the overall technical fit.

#### Acceptance Criteria

1. WHEN I provide a list of Resume variations and a Job object THEN the system SHALL return a consolidated list of direct skill matches
2. WHEN analyzing skills THEN the system SHALL compare skills from all resume variations against job required skills
3. WHEN a skill name matches exactly (case-insensitive) THEN the system SHALL classify it as a direct match
4. WHEN skill names are synonymous (e.g., "Vue" vs "Vue.js", "JavaScript" vs "JS", "Node" vs "Node.js") THEN the system SHALL classify them as direct matches
5. WHEN skill keywords from any resume match job skill names or keywords THEN the system SHALL classify it as a direct match
6. WHEN consolidating matches THEN the system SHALL optionally indicate which resume variation contributed each skill for introspection purposes

### Requirement 2

**User Story:** As a candidate or recruiter, I want to identify transferable skills between resume variations and job requirements with confidence ratings, so that I can understand how closely related experience aligns with job needs.

#### Acceptance Criteria

1. WHEN analyzing skills THEN the system SHALL use AI reasoning to identify similar/transferable skills
2. WHEN a resume skill is not a direct match BUT is conceptually similar to a job requirement THEN the system SHALL classify it as a transferable skill
3. WHEN identifying transferable skills THEN the system SHALL assign a transferability confidence rating from 1-3 where 3 indicates near-perfect transferability
4. WHEN providing transferable skills THEN the system SHALL include reasoning for why the skills are considered similar
5. WHEN skills are in the same technology family (e.g., React vs Vue, MySQL vs PostgreSQL) THEN the system SHALL typically assign them a confidence rating of 2-3
6. WHEN skills are conceptually related but require significant learning (e.g., Java vs Python) THEN the system SHALL assign them a confidence rating of 1-2

### Requirement 3

**User Story:** As a developer using the codashi-core library, I want a well-typed function interface that integrates with existing Resume and Job types, so that I can easily incorporate skill matching into my applications.

#### Acceptance Criteria

1. WHEN calling the skill matching function THEN it SHALL accept an array of Resume variations and a single Job object
2. WHEN the function executes THEN it SHALL return a consolidated structured result with direct matches and transferable skills with confidence ratings
3. WHEN using the function THEN it SHALL integrate with the existing BaseChatModel from LangChain for AI reasoning
4. WHEN returning results THEN the system SHALL provide source resume information for introspection but focus on consolidated skill analysis
5. WHEN errors occur THEN the system SHALL provide meaningful error messages and handle failures gracefully

### Requirement 4

**User Story:** As a system integrator, I want the skill matching to work with the existing AI infrastructure (Mistral + LangChain), so that it maintains consistency with other AI-powered features.

#### Acceptance Criteria

1. WHEN performing AI reasoning THEN the system SHALL use the provided BaseChatModel (Mistral)
2. WHEN making AI calls THEN the system SHALL use LangChain's structured output parsing for consistent results
3. WHEN processing multiple resumes THEN the system SHALL optimize AI calls to avoid unnecessary requests
4. WHEN the AI model is unavailable THEN the system SHALL fall back to basic string matching for direct matches

### Requirement 5

**User Story:** As a performance-conscious developer, I want the skill matching to be efficient when processing multiple resume variations, so that it can handle reasonable numbers of variations without timeout issues.

#### Acceptance Criteria

1. WHEN processing multiple resume variations THEN the system SHALL consolidate skills before AI analysis to avoid redundant processing
2. WHEN analyzing transferable skills THEN the system SHALL avoid redundant AI calls for identical skill comparisons
3. WHEN the function completes THEN it SHALL return results within reasonable time limits (under 30 seconds for 5-10 resume variations)
4. WHEN processing variations THEN the system SHALL optimize by deduplicating skills across resumes before AI analysis
