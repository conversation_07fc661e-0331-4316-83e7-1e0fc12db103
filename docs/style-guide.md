I use snake_case for fields in JSON schemas

I have my own custom error classes - <PERSON><PERSON><PERSON>rror, DomainError, UnexpectedError. I like to put a lot in their "context" property when instantiating them. DomainError is about business logic errors, UnexpectedError is about unexpected errors, ParsingError is about parsing errors.

I prefer to use `type` instead of `interface`.

In React component files I use the following structure:

```tsx
const SomeComponentName: FC<Props> = ({}) =>
```

I use the name `<PERSON><PERSON>` or even inline them inside `FC` if they're small.

I generally try to use the browser's capabilities as much as possible without compromising the UI and UX.

I use plain modern css for styling, I like semantic names for classes but I also find good use for generic class names like gap-2 or flex-col.

I try to use a very very high level of typescript strictness, with zod parsing, sometimes branded types for special fields I may be passing as arguments to functions. I would try to infer types when possible and keep noise minimal.

I like to have `entity` files where the parsers live along with methods to manipulate the entity, serving as a centralized source of truth for the entity.

I like to have repository, service and http layers.

I don't mind breaking DRY for the sake of simplicity - readability is more important to me.

Having good test coverage is every important for me. My tool of choice is vitest when possible. I don't like to mock too much, I prefer to test the real deal as much as possible, for example by creating an in-memory database if possible.
