{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "CommonJS", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@awe/codashi-core": ["libs/codashi-core/src/index.ts"], "@awe/codashi-ui": ["libs/codashi-ui/src/index.ts"], "@awe/core": ["libs/core/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}