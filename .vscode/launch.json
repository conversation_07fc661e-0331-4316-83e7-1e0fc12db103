{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug App",
      "type": "node-terminal",
      "request": "launch",
      "command": "npx nx run codarashi:serve",
      "cwd": "${workspaceFolder}"
    },
    {
      "command": "./apps/codarashi-hq/node_modules/.bin/astro dev",
      "name": "Coda HQ",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug All Codashi-Core Tests",
      "autoAttachChildProcesses": true,
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/nx",
      "args": ["test", "codashi-core", "--watch=false"],
      "smartStep": true,
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}"
    }
  ]
}
