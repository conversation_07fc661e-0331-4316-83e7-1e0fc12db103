{"name": "codashi-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/codashi-ui/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/libs/codashi-ui"}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "configDir": "libs/codashi-ui/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/codashi-ui", "configDir": "libs/codashi-ui/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "static-storybook": {"executor": "@nx/web:file-server", "dependsOn": ["build-storybook"], "options": {"buildTarget": "codashi-ui:build-storybook", "staticFilePath": "dist/storybook/codashi-ui", "spa": true}, "configurations": {"ci": {"buildTarget": "codashi-ui:build-storybook:ci"}}}}}