import { type FC, type InputHTMLAttributes, forwardRef } from 'react';
import './input.css';

export type InputProps = InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  error?: string;
  className?: string;
  inputClassName?: string;
};

const Input: FC<InputProps> = forwardRef<HTMLInputElement, InputProps>(
  (
    { className = '', inputClassName = '', label, error, id, ...props },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;

    const inputClasses = ['input', error ? 'input-error' : '', inputClassName]
      .filter(Boolean)
      .join(' ');

    const wrapperClasses = ['input-wrapper', className]
      .filter(Boolean)
      .join(' ');

    return (
      <div className={wrapperClasses}>
        {label && (
          <label htmlFor={inputId} className="input-label">
            {label}
          </label>
        )}
        <input id={inputId} className={inputClasses} ref={ref} {...props} />
        {error && <p className="input-error-message">{error}</p>}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
