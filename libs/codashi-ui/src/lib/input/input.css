/* Input Base Styles */
.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--label-color, #374151);
}

.input {
  display: flex;
  height: 40px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid var(--border-color, #d1d5db);
  background-color: var(--input-bg, white);
  padding: 0 12px;
  font-size: 14px;
  color: var(--text-color, #111827);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--focus-border-color, #4f46e5);
  box-shadow: 0 0 0 2px var(--focus-ring-color, rgba(79, 70, 229, 0.2));
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--disabled-bg, #f3f4f6);
}

.input::placeholder {
  color: var(--placeholder-color, #9ca3af);
}

/* Error State */
.input-error {
  border-color: var(--error-color, #ef4444);
}

.input-error:focus {
  box-shadow: 0 0 0 2px var(--error-ring-color, rgba(239, 68, 68, 0.2));
}

.input-error-message {
  font-size: 12px;
  color: var(--error-color, #ef4444);
  margin: 0;
}
