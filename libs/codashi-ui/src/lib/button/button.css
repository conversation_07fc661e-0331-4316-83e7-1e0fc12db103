/* Button Base Styles */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  outline: none;
  border: none;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.button:focus-visible {
  outline: 2px solid var(--focus-ring-color, #4f46e5);
  outline-offset: 2px;
}

/* But<PERSON> Variants */
.button-default {
  background-color: var(--primary-color, #4f46e5);
  color: var(--primary-foreground, white);
}

.button-default:hover:not(:disabled) {
  background-color: var(--primary-hover, #4338ca);
}

.button-destructive {
  background-color: var(--destructive-color, #ef4444);
  color: var(--destructive-foreground, white);
}

.button-destructive:hover:not(:disabled) {
  background-color: var(--destructive-hover, #dc2626);
}

.button-outline {
  background-color: transparent;
  border: 1px solid var(--border-color, #d1d5db);
  color: var(--text-color, #374151);
}

.button-outline:hover:not(:disabled) {
  background-color: var(--accent-color, #f3f4f6);
  color: var(--accent-foreground, #111827);
}

.button-secondary {
  background-color: var(--secondary-color, #9ca3af);
  color: var(--secondary-foreground, white);
}

.button-secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover, #6b7280);
}

.button-ghost {
  background-color: transparent;
  color: var(--text-color, #374151);
}

.button-ghost:hover:not(:disabled) {
  background-color: var(--accent-color, #f3f4f6);
  color: var(--accent-foreground, #111827);
}

.button-link {
  background-color: transparent;
  color: var(--primary-color, #4f46e5);
  text-decoration: none;
  padding: 0;
  height: auto !important;
}

.button-link:hover:not(:disabled) {
  text-decoration: underline;
  text-underline-offset: 4px;
}

/* Button Sizes */
.button-default {
  height: 40px;
  padding: 8px 16px;
}

.button-sm {
  height: 36px;
  padding: 6px 12px;
  font-size: 12px;
}

.button-lg {
  height: 44px;
  padding: 10px 32px;
  font-size: 16px;
}

.button-icon {
  height: 40px;
  width: 40px;
  padding: 0;
}

/* Loading State */
.button-loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-spinner {
  animation: spin 1s linear infinite;
  height: 16px;
  width: 16px;
  margin-right: 8px;
}

.button-spinner-track {
  opacity: 0.25;
}

.button-spinner-path {
  opacity: 0.75;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
