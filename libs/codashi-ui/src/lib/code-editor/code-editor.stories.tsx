// libs/codashi-ui/src/lib/YamlEditor/YamlEditor.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { yamlToResume, YamlValidationError } from '@awe/codashi-core';
import { CodeEditor } from './code-editor';

const meta: Meta<typeof CodeEditor> = {
  component: CodeEditor,
  title: 'Components/CodeEditor',
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof CodeEditor>;

// We need to create a wrapper component for the interactive story
// since we need to maintain state for the YAML content
const YamlEditorWithState = () => {
  const [yamlContent, setYamlContent] = useState(
    `basics:
  name: John <PERSON>
  email: <EMAIL>
  profiles:
    - network: GitHub
      username: johndoe
      url: https://github.com/johndoe
    - network: LinkedIn
      username: johndoe
      url: https://linkedin.com/in/johndoe
work:
  - name: Company A
    position: Senior Developer
    start_date: "2020-01-01"
    end_date: "2023-01-01"
    highlights:
      - Led team of 5 developers
      - Implemented CI/CD pipeline
  - name: Company B
    position: Developer
    start_date: "2018-01-01"
    end_date: "2020-01-01"
`
  );

  const [diagnostics, setDiagnostics] = useState<YamlValidationError[]>([]);

  return (
    <div style={{ width: '600px' }}>
      <CodeEditor
        value={yamlContent}
        diagnostics={diagnostics}
        onChange={(value) => {
          const parsed = yamlToResume(value);

          setDiagnostics(parsed.success ? [] : parsed.errors);

          console.log(parsed);
          setYamlContent(value);
        }}
      />
    </div>
  );
};

export const Default: Story = {
  render: () => <YamlEditorWithState />,
};
