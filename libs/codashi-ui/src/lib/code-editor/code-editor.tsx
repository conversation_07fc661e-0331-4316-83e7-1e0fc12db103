import { linter } from '@codemirror/lint';
import { EditorView } from '@codemirror/view';
import { langs } from '@uiw/codemirror-extensions-langs';
import { dracula } from '@uiw/codemirror-theme-dracula';
import CodeMirror from '@uiw/react-codemirror';
import React from 'react';

import { type YamlValidationError } from '@awe/codashi-core';

type Props = {
  value: string;
  onChange: (value: string) => void;
  diagnostics: YamlValidationError[];
  height?: string;
};

export const CodeEditor: React.FC<Props> = ({
  value,
  onChange,
  diagnostics,
  height = '600px',
}) => {
  return (
    <CodeMirror
      value={value}
      height={height}
      extensions={[
        // basicSetup(),
        linter(() =>
          diagnostics.map((item) => ({
            from: item.type === 'node' ? item.fromPosition : 0,
            to: item.type === 'node' ? item.toPosition : value.length,
            message: item.message,
            severity: 'error',
          }))
        ),
        langs.yaml(),
        EditorView.lineWrapping,
      ]}
      theme={dracula}
      onChange={(val) => onChange(val)}
    />
  );
};
