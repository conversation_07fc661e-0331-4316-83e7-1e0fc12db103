import { describe, expect, it } from 'vitest';

import { formDataToJson, jsonToFormData } from './form-data';

// Helper to create a FormData object with test values
function createTestFormData(entries: [string, string][]): FormData {
  const formData = new FormData();
  for (const [key, value] of entries) {
    formData.append(key, value);
  }
  return formData;
}

describe('formDataToJson', () => {
  it('should convert flat FormData to an object', () => {
    const formData = createTestFormData([
      ['name', 'John'],
      ['age', '30'],
      ['isActive', 'true'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      name: '<PERSON>',
      age: '30',
      isActive: 'true',
    });
  });

  it('should handle multiple values with the same key as arrays', () => {
    const formData = createTestFormData([
      ['skill', 'JavaScript'],
      ['skill', 'TypeScript'],
      ['skill', 'React'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      skill: ['JavaScript', 'TypeScript', 'React'],
    });
  });

  it('should handle nested objects with dot notation', () => {
    const formData = createTestFormData([
      ['user.name', 'John'],
      ['user.profile.age', '30'],
      ['user.profile.preferences.theme', 'dark'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      user: {
        name: 'John',
        profile: {
          age: '30',
          preferences: {
            theme: 'dark',
          },
        },
      },
    });
  });

  it('should handle arrays with numeric indices in dot notation', () => {
    const formData = createTestFormData([
      ['skills.0', 'JavaScript'],
      ['skills.1', 'TypeScript'],
      ['skills.2', 'React'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      skills: ['JavaScript', 'TypeScript', 'React'],
    });
  });

  it('should handle multiple values with the same dotted key as arrays', () => {
    const formData = createTestFormData([
      ['skill.specializations', 'frontend'],
      ['skill.specializations', 'backend'],
      ['skill.specializations', 'devops'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      skill: {
        specializations: ['frontend', 'backend', 'devops'],
      },
    });
  });

  it('should handle mixed nested objects and arrays', () => {
    const formData = createTestFormData([
      ['user.name', 'John'],
      ['user.skills.0', 'JavaScript'],
      ['user.skills.1', 'TypeScript'],
      ['user.address.city', 'New York'],
      ['user.address.zip', '10001'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      user: {
        name: 'John',
        skills: ['JavaScript', 'TypeScript'],
        address: {
          city: 'New York',
          zip: '10001',
        },
      },
    });
  });

  it('should handle deeply nested arrays and objects', () => {
    const formData = createTestFormData([
      ['projects.0.name', 'Project A'],
      ['projects.0.skills.0', 'JavaScript'],
      ['projects.0.skills.1', 'React'],
      ['projects.1.name', 'Project B'],
      ['projects.1.skills.0', 'TypeScript'],
      ['projects.1.skills.1', 'Node.js'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      projects: [
        {
          name: 'Project A',
          skills: ['JavaScript', 'React'],
        },
        {
          name: 'Project B',
          skills: ['TypeScript', 'Node.js'],
        },
      ],
    });
  });

  it('should preserve original values without type conversion', () => {
    const formData = createTestFormData([
      ['name', 'John'],
      ['age', '30'],
      ['isActive', 'true'],
    ]);

    const result = formDataToJson(formData);

    expect(result).toEqual({
      name: 'John',
      age: '30',
      isActive: 'true',
    });
  });
});

describe('jsonToFormData', () => {
  it('should convert a flat object to FormData', () => {
    const obj = {
      name: 'John',
      age: 30,
      isActive: true,
    };

    const result = jsonToFormData(obj);

    // Convert FormData to object for easier assertion
    const entries: Record<string, string> = {};
    result.forEach((value, key) => {
      entries[key] = value as string;
    });

    expect(entries).toEqual({
      name: 'John',
      age: '30',
      isActive: 'true',
    });
  });

  it('should handle nested objects', () => {
    const obj = {
      user: {
        name: 'John',
        profile: {
          age: 30,
        },
      },
    };

    const result = jsonToFormData(obj);

    // Convert FormData to object for easier assertion
    const entries: Record<string, string> = {};
    result.forEach((value, key) => {
      entries[key] = value as string;
    });

    expect(entries).toEqual({
      user: '{"name":"John","profile":{"age":30}}',
    });
  });

  it('should handle arrays', () => {
    const obj = {
      skills: ['JavaScript', 'TypeScript', 'React'],
    };

    const result = jsonToFormData(obj);

    // Convert FormData to object for easier assertion
    const entries: Record<string, string> = {};
    result.forEach((value, key) => {
      entries[key] = value as string;
    });

    expect(entries).toEqual({
      skills: '["JavaScript","TypeScript","React"]',
    });
  });

  it('should skip undefined and null values', () => {
    const obj = {
      name: 'John',
      description: undefined,
      bio: null,
    };

    const result = jsonToFormData(obj);

    // Convert FormData to object for easier assertion
    const entries: Record<string, string> = {};
    result.forEach((value, key) => {
      entries[key] = value as string;
    });

    expect(entries).toEqual({
      name: 'John',
    });
    expect(Object.keys(entries)).not.toContain('description');
    expect(Object.keys(entries)).not.toContain('bio');
  });
});
