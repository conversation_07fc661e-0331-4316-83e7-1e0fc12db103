import { Document, Page, PDFViewer } from '@react-pdf/renderer';
import { type FC } from 'react';
import { match } from 'ts-pattern';

import { type Resume } from '@awe/codashi-core';
import { PdfResumeAwards } from './sections/awards';
import { PdfResumeCertificates } from './sections/certificates';
import { PdfResumeEducation } from './sections/education';
import { PdfResumeHeader } from './sections/header';
import { PdfResumeInterests } from './sections/interests';
import { PdfResumeLanguages } from './sections/languages';
import { PdfResumeProjects } from './sections/projects';
import { PdfResumePublications } from './sections/publications';
import { PdfResumeReferences } from './sections/references';
import { PdfResumeSkills } from './sections/skills';
import { PdfResumeVolunteer } from './sections/volunteer';
import { PdfResumeWork } from './sections/work';
import { theme } from './theme';

type Props = {
  profile: Resume;
  height?: number | string;
};

export const ResumePdfPreview: FC<Props> = ({ profile, height = '100%' }) => {
  return (
    <div style={{ width: '100%', height }}>
      <PDFViewer style={{ width: '100%', height }}>
        <Document>
          <Page
            style={{
              padding: 10,
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              fontSize: 12,
              color: theme.colors.text.primary,
            }}
          >
            <PdfResumeHeader section={profile.header} />

            {profile.sections.map((section, index) => {
              if (!section.items.length) return null;

              return match(section)
                .with({ name: 'work' }, (section) => (
                  <PdfResumeWork
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'volunteer' }, (section) => (
                  <PdfResumeVolunteer
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'education' }, (section) => (
                  <PdfResumeEducation
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'awards' }, (section) => (
                  <PdfResumeAwards
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'certificates' }, (section) => (
                  <PdfResumeCertificates
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'publications' }, (section) => (
                  <PdfResumePublications
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'skills' }, (section) => (
                  <PdfResumeSkills
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'languages' }, (section) => (
                  <PdfResumeLanguages
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'interests' }, (section) => (
                  <PdfResumeInterests
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'references' }, (section) => (
                  <PdfResumeReferences
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .with({ name: 'projects' }, (section) => (
                  <PdfResumeProjects
                    section={section}
                    key={`${section.name}-${index}`}
                  />
                ))
                .exhaustive();
            })}
          </Page>
        </Document>
      </PDFViewer>
    </div>
  );
};
