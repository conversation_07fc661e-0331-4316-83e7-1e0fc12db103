import { Text, View } from '@react-pdf/renderer';
import React from 'react';
import { match } from 'ts-pattern';

import { type Resume } from '@awe/codashi-core';
import { theme } from '../theme';

type Props = {
  section: NonNullable<Resume['header']>;
};

export const PdfResumeHeader: React.FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        padding: 10,
        display: 'flex',
        flexDirection: 'row',
        gap: 6,
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {section.items.map((item, index) => {
        return match(item)
          .with({ name: 'name' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.lg,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {item.value}
            </Text>
          ))
          .with({ name: 'title' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value}
            </Text>
          ))
          .with({ name: 'email' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
                color: theme.colors.text.link,
              }}
            >
              {item.value}
            </Text>
          ))
          .with({ name: 'phone' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value}
            </Text>
          ))
          .with({ name: 'url' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.sm,
              }}
            >
              {item.value}
            </Text>
          ))
          .with({ name: 'location' }, (item) => {
            if (!item.value) {
              return null;
            }

            return (
              <Text
                key={`${item.name}-${index}`}
                style={{
                  fontSize: 12,
                }}
              >
                {[
                  item.value.address,
                  item.value.city,
                  item.value.region,
                  item.value.country_code,
                  item.value.postal_code,
                ]
                  .filter(Boolean)
                  .join(', ')}
              </Text>
            );
          })
          .with({ name: 'profile' }, (item) => {
            if (!item.value) {
              return null;
            }

            return (
              <View key={`${item.name}-${index}`} style={{ marginTop: 8 }}>
                <Text style={{ fontSize: 12 }} key={`${item.name}-${index}`}>
                  {item.value.network ? `${item.value.network}: ` : ''}
                  {item.value.username || ''}
                  {item.value.url ? ` (${item.value.url})` : ''}
                </Text>
              </View>
            );
          })
          .with({ name: 'summary' }, (item) => (
            <Text
              key={`${item.name}-${index}`}
              style={{
                fontSize: theme.typography.fontSizes.xs,
                marginTop: 16,
                marginBottom: 16,
                textAlign: 'center',
                lineHeight: 1.2,
              }}
            >
              {item.value}
            </Text>
          ))
          .exhaustive();
      })}
    </View>
  );
};
