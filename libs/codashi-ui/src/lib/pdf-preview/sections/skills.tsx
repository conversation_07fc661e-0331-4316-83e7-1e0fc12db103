import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { Text, View } from '@react-pdf/renderer';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'skills'>>;
};

export const PdfResumeSkills: FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        gap: 12,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
        flexWrap: 'wrap',
      }}
    >
      <SectionTitle>Skills</SectionTitle>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: 6,
          flexWrap: 'wrap',
        }}
      >
        {section.items.map((skill, idx) => (
          <Text
            key={`${skill.name}-${idx}`}
            style={{ fontSize: theme.typography.fontSizes.sm }}
          >
            {skill.name}
            {idx !== section.items.length - 1 && ','}
          </Text>
        ))}
      </View>
    </View>
  );
};
