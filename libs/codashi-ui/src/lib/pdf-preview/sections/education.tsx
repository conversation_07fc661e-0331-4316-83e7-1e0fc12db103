import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { DateRange } from '../components/date-range';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'education'>>;
};

export const PdfResumeEducation: FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
        flexWrap: 'wrap',
      }}
    >
      <SectionTitle>Education</SectionTitle>

      {section.items.map((item, idx) => {
        return (
          <View
            key={idx}
            style={{
              gap: 12,
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 4,
                width: '100%',
              }}
            >
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.md,
                    fontWeight: theme.typography.fontWeights.bold,
                  }}
                >
                  {item.institution}
                </Text>
                <DateRange
                  startDate={item.start_date}
                  endDate={item.end_date}
                  format={idx < 2 ? 'long' : 'short'}
                />
              </View>

              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: 8,
                  alignItems: 'center',
                }}
              >
                {item.study_type && (
                  <Text
                    style={{
                      fontSize: theme.typography.fontSizes.sm,
                      fontStyle: 'italic',
                    }}
                  >
                    {item.study_type}
                  </Text>
                )}
                {item.area && (
                  <>
                    {item.study_type && (
                      <Text
                        style={{
                          fontSize: theme.typography.fontSizes.sm,
                          fontStyle: 'italic',
                        }}
                      >
                        in
                      </Text>
                    )}
                    <Text
                      style={{
                        fontSize: theme.typography.fontSizes.sm,
                        fontStyle: 'italic',
                      }}
                    >
                      {item.area}
                    </Text>
                  </>
                )}
              </View>

              {item.score && (
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.xs,
                    marginTop: 2,
                  }}
                >
                  GPA: {item.score}
                </Text>
              )}

              {item.courses?.length ? (
                <View
                  style={{
                    marginTop: 6,
                    paddingLeft: 12,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 4,
                  }}
                >
                  <Text
                    style={{
                      fontSize: theme.typography.fontSizes.xs,
                      fontWeight: theme.typography.fontWeights.bold,
                    }}
                  >
                    Relevant Coursework:
                  </Text>
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      gap: 4,
                    }}
                  >
                    {item.courses.map((course, i) => (
                      <Text
                        key={i}
                        style={{
                          fontSize: theme.typography.fontSizes.xs,
                        }}
                      >
                        {course}
                        {i !== (item.courses?.length ?? 0) - 1 && ','}
                      </Text>
                    ))}
                  </View>
                </View>
              ) : null}
            </View>
          </View>
        );
      })}
    </View>
  );
};
