import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'interests'>>;
};

export const PdfResumeInterests: FC<Props> = ({ section }) => {
  if (!section.items.length) return null;
  return (
    <View
      style={{
        gap: 12,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
      }}
    >
      <SectionTitle>Interests</SectionTitle>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: 6,
          flexWrap: 'wrap',
        }}
      >
        {section.items.map((interest, idx) => (
          <View
            key={`${interest.name}-${idx}`}
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              width: '100%',
            }}
          >
            <Text
              style={{
                fontSize: theme.typography.fontSizes.sm,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {interest.name}
            </Text>
            {interest.keywords && interest.keywords.length > 0 && (
              <Text style={{ fontSize: theme.typography.fontSizes.sm }}>
                {` — ${interest.keywords.join(', ')}`}
              </Text>
            )}
          </View>
        ))}
      </View>
    </View>
  );
};
