import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'languages'>>;
};

export const PdfResumeLanguages: FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        gap: 12,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
      }}
    >
      <SectionTitle>Languages</SectionTitle>
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: 6,
          width: '100%',
        }}
      >
        {section.items.map((lang, idx) => (
          <View
            key={`${lang.language}-${idx}`}
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <Text
              style={{
                fontSize: theme.typography.fontSizes.sm,
                fontWeight: theme.typography.fontWeights.bold,
              }}
            >
              {lang.language}
            </Text>

            <Text style={{ fontSize: theme.typography.fontSizes.sm }}>
              {lang.fluency ? ` — ${lang.fluency}` : ''}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};
