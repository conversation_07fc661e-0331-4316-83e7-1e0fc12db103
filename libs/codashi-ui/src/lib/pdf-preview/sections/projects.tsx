import { Link, Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { DateRange } from '../components/date-range';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'projects'>>;
};

export const PdfResumeProjects: FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 10,
        flexWrap: 'wrap',
      }}
    >
      <SectionTitle>Projects</SectionTitle>
      {section.items.map((project, idx) => (
        <View
          key={idx}
          style={{
            gap: 10,
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 4,
              width: '100%',
            }}
          >
            {project.url ? (
              <Link
                src={project.url}
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  fontWeight: theme.typography.fontWeights.bold,
                  color: theme.colors.text.primary,
                  textDecoration: 'underline',
                }}
              >
                {project.name}
              </Link>
            ) : (
              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  fontWeight: theme.typography.fontWeights.bold,
                }}
              >
                {project.name}
              </Text>
            )}

            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                gap: 4,
              }}
            >
              {(project.entity || project.type) && (
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.xs,
                    color: theme.colors.text.subtle,
                    fontStyle: 'italic',
                  }}
                >
                  {project.entity ? `(${project.entity}` : '('}
                  {project.entity && project.type ? ', ' : ''}
                  {project.type}
                  {')'}
                </Text>
              )}

              <DateRange
                startDate={project.start_date ?? null}
                endDate={project.end_date ?? null}
              />
            </View>
          </View>
          {project.description && (
            <Text
              style={{
                fontSize: theme.typography.fontSizes.xs,
                color: theme.colors.text.primary,
                marginTop: 2,
              }}
            >
              {project.description}
            </Text>
          )}
          {!!project.highlights?.length && (
            <View
              style={{
                marginTop: 4,
                paddingLeft: 12,
                display: 'flex',
                flexDirection: 'column',
                gap: 4,
              }}
            >
              {project.highlights.map((highlight, i) => (
                <View
                  key={i}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: 4,
                  }}
                >
                  <Text>•</Text>
                  <Text
                    style={{
                      fontSize: theme.typography.fontSizes.xs,
                      flex: 1,
                    }}
                  >
                    {highlight}
                  </Text>
                </View>
              ))}
            </View>
          )}
          {!!project.keywords?.length && (
            <Text
              style={{
                fontSize: theme.typography.fontSizes.xs,
                color: theme.colors.text.subtle,
                marginTop: 2,
              }}
            >
              Technologies: {project.keywords.filter(Boolean).join(', ')}
            </Text>
          )}
          {!!project.roles?.length && (
            <Text
              style={{
                fontSize: theme.typography.fontSizes.xs,
                color: theme.colors.text.subtle,
                marginTop: 2,
              }}
            >
              Role{project.roles.length > 1 ? 's' : ''}:{' '}
              {project.roles.filter(Boolean).join(', ')}
            </Text>
          )}
        </View>
      ))}
    </View>
  );
};
