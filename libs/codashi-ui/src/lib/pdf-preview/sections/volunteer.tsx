import { Text, View } from '@react-pdf/renderer';
import { type FC } from 'react';

import { type ResumeSection } from '@awe/codashi-core';
import { DateRange } from '../components/date-range';
import { SectionTitle } from '../components/section-title';
import { theme } from '../theme';

type Props = {
  section: NonNullable<ResumeSection<'volunteer'>>;
};

export const PdfResumeVolunteer: FC<Props> = ({ section }) => {
  return (
    <View
      style={{
        gap: 16,
        display: 'flex',
        flexDirection: 'column',
        padding: 12,
        flexWrap: 'wrap',
      }}
    >
      <SectionTitle>Volunteer Experience</SectionTitle>

      {section.items.map((item, idx) => {
        return (
          <View
            key={idx}
            style={{ gap: 12, display: 'flex', flexDirection: 'column' }}
          >
            <View
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 4,
                width: '100%',
              }}
            >
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.md,
                    fontWeight: theme.typography.fontWeights.bold,
                  }}
                >
                  {item.organization}
                </Text>
                <DateRange
                  startDate={item.start_date}
                  endDate={item.end_date}
                  format={idx < 2 ? 'long' : 'short'}
                />
              </View>

              <Text
                style={{
                  fontSize: theme.typography.fontSizes.sm,
                  fontStyle: 'italic',
                }}
              >
                {item.position}
              </Text>

              {item.summary && (
                <Text
                  style={{
                    fontSize: theme.typography.fontSizes.xs,
                    marginTop: 4,
                  }}
                >
                  {item.summary}
                </Text>
              )}

              {item.highlights?.length ? (
                <View
                  style={{
                    marginTop: 6,
                    paddingLeft: 12,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 4,
                  }}
                >
                  {item.highlights.map((highlight, i) => (
                    <View
                      key={i}
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: 4,
                      }}
                    >
                      <Text>•</Text>
                      <Text
                        style={{
                          fontSize: theme.typography.fontSizes.xs,
                          flex: 1,
                        }}
                      >
                        {highlight}
                      </Text>
                    </View>
                  ))}
                </View>
              ) : null}
            </View>
          </View>
        );
      })}
    </View>
  );
};
