import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ResumePdfPreview } from '../resume-pdf.preview';
import { exampleProfile } from './story.data';

const meta: Meta<typeof ResumePdfPreview> = {
  component: ResumePdfPreview,
  title: 'PDF/ResumePdfPreview',
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof ResumePdfPreview>;

export const Default: Story = {
  args: {
    profile: exampleProfile,
    height: '900px',
  },
};
