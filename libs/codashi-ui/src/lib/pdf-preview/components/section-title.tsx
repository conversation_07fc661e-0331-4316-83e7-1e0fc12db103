import { Text, View } from '@react-pdf/renderer';
import { type FC, type ReactNode } from 'react';

import { theme } from '../theme';

type Props = {
  children: ReactNode;
};

export const SectionTitle: FC<Props> = ({ children }) => (
  <View
    style={{
      borderBottom: `1px solid ${theme.colors.border.primary}`,
      paddingBottom: 2,
      width: '100%',
    }}
  >
    <Text
      style={{
        fontSize: theme.typography.fontSizes.lg,
        fontWeight: theme.typography.fontWeights.bold,
        width: '100%',
        textAlign: 'left',
        color: theme.colors.primary,
      }}
    >
      {children}
    </Text>
  </View>
);
