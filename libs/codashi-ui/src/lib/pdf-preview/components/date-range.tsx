import { Temporal } from '@js-temporal/polyfill';
import { Text } from '@react-pdf/renderer';
import { type FC } from 'react';

import { theme } from '../theme';

type Props = {
  startDate: string | null;
  endDate: string | null;
  format?: 'short' | 'long';
};

export const DateRange: FC<Props> = ({
  startDate,
  endDate,
  format = 'short',
}) => {
  let formattedStartDate = 'N/A';
  let formattedEndDate = 'Present';

  if (startDate) {
    const startDateObj = Temporal.PlainDate.from(startDate);
    formattedStartDate =
      format === 'short'
        ? startDateObj.year.toString()
        : `${startDateObj.toLocaleString('en-US', { month: 'short' })} ${
            startDateObj.year
          }`;
  }

  if (endDate) {
    const endDateObj = Temporal.PlainDate.from(endDate);
    formattedEndDate =
      format === 'short'
        ? endDateObj.year.toString()
        : `${endDateObj.toLocaleString('en-US', { month: 'short' })} ${
            endDateObj.year
          }`;
  }

  return (
    <Text
      style={{
        fontSize: theme.typography.fontSizes.xs,
        color: theme.colors.text.subtle,
      }}
    >
      {formattedStartDate} - {formattedEndDate}
    </Text>
  );
};
