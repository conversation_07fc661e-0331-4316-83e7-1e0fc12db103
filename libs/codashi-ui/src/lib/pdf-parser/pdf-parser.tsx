import { extractTextWithFallback } from '@awe/codashi-core';
import { useCallback, useState, type ChangeEvent, type FC } from 'react';

import { Button } from '../button/button';

type Props = {
  onTextExtracted?: (text: string) => void;
};

export const PDFParser: FC<Props> = ({ onTextExtracted }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [extractedText, setExtractedText] = useState('');
  const [fileName, setFileName] = useState<string>('');
  const [processingTimeSeconds, setProcessingTimeSeconds] = useState<
    number | null
  >(null);

  const handleFileChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      if (file.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        return;
      }

      setFileName(file.name);
      setIsLoading(true);
      setError(null);
      setProcessingTimeSeconds(null);

      const startTime = performance.now();

      try {
        const { content: text } = await extractTextWithFallback(file);
        const endTime = performance.now();
        const timeInSeconds = ((endTime - startTime) / 1000).toFixed(2);
        setProcessingTimeSeconds(parseFloat(timeInSeconds));
        setExtractedText(text);
        onTextExtracted?.(text);
      } catch (err) {
        console.error('Error extracting text from PDF:', err);
        setError(
          'Failed to extract text from PDF. The PDF might be image-based.'
        );
      } finally {
        setIsLoading(false);
      }
    },
    [onTextExtracted]
  );

  return (
    <div className="flex flex-col gap-4 max-w-3xl">
      <div className="flex items-center gap-4">
        <label className="relative">
          <Button variant="outline" disabled={isLoading}>
            <span>Choose PDF file</span>
          </Button>
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isLoading}
          />
        </label>
        {fileName && (
          <span className="text-sm text-gray-600 truncate max-w-xs">
            {fileName}
          </span>
        )}
      </div>

      {isLoading && (
        <div className="text-sm text-gray-500">Extracting text...</div>
      )}

      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 rounded-md">
          {error}
        </div>
      )}

      {extractedText && (
        <div className="mt-4">
          <h3 className="mb-2 text-sm font-medium text-gray-700">
            Extracted Text ({extractedText.length} characters)
            {processingTimeSeconds !== null && (
              <span className="ml-2 text-gray-500">
                - Processed in {processingTimeSeconds} seconds
              </span>
            )}
          </h3>
          <pre className="p-4 text-sm bg-gray-50 rounded-md overflow-auto max-h-96">
            {extractedText}
          </pre>
        </div>
      )}
    </div>
  );
};
