import type { Meta, StoryObj } from '@storybook/react';
import { PDFParser } from './pdf-parser';

const meta = {
  title: 'Components/PDF Parser',
  component: PDFParser,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PDFParser>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onTextExtracted: (text) => {
      console.log('Extracted text:', text);
    },
  },
};

export const WithInitialText: Story = {
  args: {
    onTextExtracted: (text) => {
      console.log('Extracted text:', text);
    },
  },
  render: (args) => (
    <div className="w-[800px] p-6 bg-white rounded-lg shadow">
      <h2 className="mb-4 text-xl font-semibold">PDF Text Extractor</h2>
      <p className="mb-6 text-gray-600">
        Upload a PDF file to extract its text content. The extracted text will
        be displayed below.
      </p>
      <PDFParser {...args} />
    </div>
  ),
};
