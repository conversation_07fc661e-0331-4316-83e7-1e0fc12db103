{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": ["node", "react", "react-dom"], "target": "es2020"}, "include": ["src/**/*.ts"], "exclude": ["vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx"]}