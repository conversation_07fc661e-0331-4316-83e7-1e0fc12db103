{"name": "codashi-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/codashi-core/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/libs/codashi-core"}}, "test:watch": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/libs/codashi-core", "watch": true}}}}