{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "ESNext", "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}