### Resume vs Profile

For now we're not storing any "profile" as a unified, global piece of data for an engineer. The reason behind this is simplicity - it's extra work to keep it in sync with every change within a resume, deciding when to remove, add, edit different entries can be challenging.

Instead we'll be treating existing resumes and their sum as the "profile" with the possibility for users to create "template" ones should they wish so. Examples may be "my front end focused profile" vs "my generalist profile" or "my senior profile" vs "my lead-aspiring profile" and so on.

### But how would completions and suggestions work?

We'll be aware which section the cursor is on and try to search the resumes for suggestions within the same section, priority can be date-based or user based (templates, tags). Out in the real world this may prove to be a performance challenge - we'll have to see for ourselves. Local-first might be a possible answer so we can search other locally stored resumes more efficiently.

Some local/remote LLM help may be needed as a shortcut instead of algorithmic solutions to de-dupe very similar suggestions taken from different resumes (e.g. one word diff in a bullet point)

### Resources

https://github.com/jsonresume/jsonresume-fake/tree/master/resumes - collection of fake resumes
