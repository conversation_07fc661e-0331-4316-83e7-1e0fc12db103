# Profile Initialization with LangGraph

This module provides a LangGraph-based orchestration flow for extracting profile information from resume text. It combines zero-shot extraction with confidence-based user input requests.

## Overview

The profile initialization process works as follows:

1. **Zero-Shot Extraction**: The system first attempts to extract profile data from resume text using the `extractProfile` function from the AI module.

2. **Confidence Analysis**: The system analyzes the confidence scores of the extracted data, with special focus on critical fields like name, email, and title.

3. **User Input (if needed)**: If any critical fields have low confidence scores (below 0.7), the system requests manual input from the user to verify or correct these fields.

4. **Profile Merging**: The system merges the AI-extracted profile with any user inputs, prioritizing user-provided data as high-confidence entries.

## Key Components

### State Management

The flow maintains a state object that tracks:

- The input resume text
- The extracted profile data
- Whether user input is required
- Which fields need verification
- User inputs for low-confidence fields
- Error messages
- Authentication credentials
- Completion status

### Node Functions

1. `zeroShotExtractionNode`: Calls the AI model to extract profile information
2. `confidenceAnalysisNode`: Analyzes confidence scores and identifies fields requiring user input
3. `userInputNode`: Prompts for user input on low-confidence fields
4. `mergeProfileNode`: Combines AI extraction with user inputs
5. `routeNextStep`: Decision function to determine the next step based on confidence analysis

## Usage

```typescript
// Initialize the agent with resume text and credentials
const stream = await profileInitAgent.stream({
  resumeText: 'John Doe\nSoftware Engineer\<EMAIL>...',
  cloudflareAccountId: 'your-account-id',
  cloudflareApiToken: 'your-api-token',
});

// Process events from the agent
for await (const event of stream) {
  if (event.event === 'on_node_end') {
    // Check if user input is needed at the confidence analysis stage
    if (event.name === 'confidenceAnalysis' && event.state.userInputRequired) {
      // Collect user input for fields in event.state.fieldsToVerify
      // and update the agent state
    }
  }
}

// Get the final profile
const finalProfile = stream.state.profile;
```

## Integration Points

- **UI Integration**: The `userInputNode` is designed to be integrated with a UI component that can collect user input for fields that need verification.
- **Database Integration**: After the profile is fully extracted and verified, it can be saved to a database or used for further processing.
