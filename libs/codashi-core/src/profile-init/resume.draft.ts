import { zodToDomainOrThrow } from '@awe/core';
import z from 'zod';

const evaluation = z.object({
  ai_confidence: z
    .number()
    .min(0)
    .max(1)
    .describe("Confidence score (0-1) of the extracted data's accuracy"),
  human_feedback: z
    .discriminatedUnion('type', [
      z.object({
        type: z.literal('confirmation'),
        value: z.enum(['yes', 'no']),
      }),
      z.object({
        type: z.literal('edit'),
        value: z.string(),
      }),
    ])
    .describe('Human feedback on the extracted data')
    .nullable(),
});

export const workItem = z.object({
  name: z.string().describe('Company or organization name').nullable(),
  position: z.string().describe('Job title or role').nullable(),
  url: z.string().url().nullable().describe('Company website URL'),
  start_date: z.coerce.date().nullable().describe('Start date in ISO format'),
  end_date: z.coerce
    .date()
    .nullable()
    .describe('End date in ISO format (if applicable)'),
  summary: z.string().nullable().describe('Job summary'),
  highlights: z
    .array(z.string())
    .default([])
    .describe('Key achievements or responsibilities'),
  location: z.string().nullable().describe('Job location'),
  description: z.string().nullable().describe('Detailed job description'),
});

export const profileDraft = z.object({
  name: z
    .array(
      z
        .object({
          value: z.string().describe('Full name of the person'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of possible name variations with confidence scores'),

  title: z
    .array(
      z
        .object({
          value: z.string().describe('Professional title or role'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of professional titles with confidence scores'),

  email: z
    .array(
      z
        .object({
          value: z.string().email().describe('Valid email address'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of email addresses with confidence scores'),

  phone: z
    .array(
      z
        .object({
          value: z.string().describe('Phone number'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of phone numbers with confidence scores'),

  url: z
    .array(
      z
        .object({
          value: z.string().url().describe('Personal website or portfolio URL'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of URLs with confidence scores'),

  location: z
    .array(
      z
        .object({
          value: z.object({
            address: z.string().nullable(),
            postal_code: z.string().nullable(),
            city: z.string().nullable(),
            country_code: z.string().nullable(),
            region: z.string().nullable(),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of location objects with confidence scores'),

  summary: z
    .array(
      z
        .object({
          value: z.string().describe('Professional summary or bio'),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of summary paragraphs with confidence scores'),

  profiles: z
    .array(
      z
        .object({
          value: z.object({
            network: z.string().nullable(),
            username: z.string().nullable(),
            url: z.string().url().nullable(),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of social profiles with confidence scores'),

  education: z
    .array(
      z
        .object({
          value: z.object({
            institution: z
              .string()
              .nullable()
              .describe('Name of the educational institution'),
            area: z.string().nullable().describe('Field of study'),
            study_type: z
              .string()
              .nullable()
              .describe('Type of degree or certification'),
            start_date: z.coerce
              .date()
              .nullable()
              .describe('Start date in ISO format'),
            end_date: z.coerce
              .date()
              .nullable()
              .describe('End date in ISO format (if completed)'),
            score: z.string().nullable().describe('GPA or other score'),
            courses: z
              .array(z.string())
              .default([])
              .describe('Relevant courses taken'),
            url: z.string().url().nullable().describe('URL to the education'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of education history entries with confidence scores'),

  work: z
    .array(
      z
        .object({
          value: workItem,
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of work experience entries with confidence scores'),

  volunteer: z
    .array(
      z
        .object({
          value: z.object({
            organization: z.string().describe('Organization name').nullable(),
            position: z
              .string()
              .describe('Volunteer role or position')
              .nullable(),
            url: z
              .string()
              .url()
              .nullable()
              .describe('Organization website URL'),
            start_date: z.coerce
              .date()
              .nullable()
              .describe('Start date in ISO format'),
            end_date: z.coerce
              .date()
              .nullable()
              .describe('End date in ISO format (if applicable)'),
            summary: z
              .string()
              .nullable()
              .describe('Summary of volunteer work'),
            highlights: z
              .array(z.string())
              .default([])
              .describe('Key contributions or achievements'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of volunteer experience entries with confidence scores'),

  awards: z
    .array(
      z
        .object({
          value: z.object({
            title: z.string().describe('Name of the award').nullable(),
            date: z.coerce
              .date()
              .nullable()
              .describe('Date the award was received'),
            awarder: z
              .string()
              .nullable()
              .describe('Organization that granted the award'),
            summary: z.string().nullable().describe('Description of the award'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of awards with confidence scores'),

  certificates: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Name of the certificate').nullable(),
            date: z.coerce
              .date()
              .nullable()
              .describe('Date the certificate was issued'),
            url: z.string().url().nullable().describe('URL to the certificate'),
            issuer: z
              .string()
              .nullable()
              .describe('Organization that issued the certificate'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of certificates with confidence scores'),

  publications: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Title of the publication').nullable(),
            publisher: z.string().nullable().describe('Publisher name'),
            release_date: z.coerce
              .date()
              .nullable()
              .describe('Publication date'),
            url: z.string().url().nullable().describe('URL to the publication'),
            summary: z.string().nullable().describe('Abstract or summary'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of publications with confidence scores'),

  skills: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Skill name'),
            level: z
              .string()
              .nullable()
              .describe(
                'Proficiency level (e.g., Beginner, Intermediate, Expert)'
              ),
            keywords: z
              .array(z.string())
              .default([])
              .describe('Related technologies or sub-skills'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of skills with confidence scores'),

  languages: z
    .array(
      z
        .object({
          value: z.object({
            language: z.string().describe('Language name').nullable(),
            fluency: z
              .string()
              .nullable()
              .describe('Fluency level (e.g., Native, Fluent, Basic)'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of languages with fluency levels and confidence scores'),

  interests: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Interest name').nullable(),
            keywords: z
              .array(z.string())
              .default([])
              .describe('Related sub-interests or activities'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of personal interests with confidence scores'),

  references: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Reference name').nullable(),
            reference: z
              .string()
              .describe('Reference statement or description')
              .nullable(),
            email: z.string().email().nullable().describe('Reference email'),
            phone: z.string().nullable().describe('Reference phone'),
            position: z.string().nullable().describe('Reference position'),
            company: z.string().nullable().describe('Reference company'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of professional references with confidence scores'),

  // Projects
  projects: z
    .array(
      z
        .object({
          value: z.object({
            name: z.string().describe('Project name').nullable(),
            description: z.string().nullable().describe('Project description'),
            highlights: z
              .array(z.string())
              .default([])
              .describe('Key features or achievements'),
            keywords: z
              .array(z.string())
              .default([])
              .describe('Technologies used'),
            start_date: z.coerce
              .date()
              .nullable()
              .describe('Start date in ISO format'),
            end_date: z.coerce
              .date()
              .nullable()
              .describe('End date in ISO format (if completed)'),
            url: z
              .string()
              .url()
              .nullable()
              .describe('Project URL or repository'),
            roles: z
              .array(z.string())
              .default([])
              .describe('Roles in the project'),
            entity: z.string().nullable().describe('Organization or company'),
            type: z
              .string()
              .nullable()
              .describe('Project type (e.g., Open Source, Academic)'),
          }),
        })
        .merge(evaluation)
    )
    .default([])
    .describe('Array of projects with confidence scores'),
});

export type ProfileDraft = z.infer<typeof profileDraft>;

export const toDraft = (input: unknown) =>
  zodToDomainOrThrow(profileDraft, input);

/*
  Resume Extraction Implementation Strategy
  ======================================
  
  1. Hybrid Extraction Approach
     -------------------------
     - Start with 1-shot extraction for speed
     - Fall back to step-by-step for missing/low-confidence fields
     - Validate and repair the extracted data
  
  2. Implementation Steps
     -------------------
     a. One-shot Extraction (extractOneShot):
        - Single LLM call to extract all fields
        - Include schema and examples in the prompt
        - Request confidence scoring for each field
  
     b. Validation & Confidence Check:
        - Validate against Zod schema
        - Check confidence thresholds:
          * High (>0.9): Accept
          * Medium (0.7-0.9): Flag for review
          * Low (<0.7): Trigger step-by-step
  
     c. Step-by-step Fallback (extractStepByStep):
        - Extract one section at a time
        - Focus on critical fields first (name, email, work)
        - Merge results with 1-shot attempt
  
  3. Critical Fields (require high confidence):
     - name
     - email
     - work experience
     - education
  
  4. Error Handling:
     - Log all validation errors
     - Provide fallback values where possible
     - Track extraction metrics for continuous improvement
  
  5. Optimization Opportunities:
     - Cache common patterns
     - Parallelize independent sections
     - Implement retry logic for transient failures
  
  Example Implementation:
  ---------------------
  async function extractResumeWithFallback(resumeText: string) {
      // 1. Try one-shot extraction
      const oneShotResult = await extractOneShot(resumeText);
      
      // 2. Validate and check confidence
      const validation = extractedProfile.safeParse(oneShotResult);
      if (validation.success && hasHighConfidence(oneShotResult)) {
          return oneShotResult;
      }
      
      // 3. Fall back to step-by-step for missing/low-confidence fields
      return extractStepByStep(resumeText, validation.error);
  }
  
  function hasHighConfidence(result: any) {
      const criticalFields = ['name', 'email', 'work'];
      return criticalFields.every(field => 
          result[field]?.length > 0 && 
          result[field].every((item: any) => item.confidence >= 0.8)
      );
  }
  */

/*
Recommendations for Schema Improvements:
======================================

1. Skills Section Enhancements:
   - Add category (e.g., 'Frontend', 'Backend', 'DevOps')
   - Include years_of_experience field
   - Add last_used date field
   - Add proficiency level (Beginner to Expert)
   - Example:
     ```typescript
     skills: z.array(
       z.object({
         value: z.object({
           name: z.string(),
           category: z.enum(['Frontend', 'Backend', 'DevOps', 'Other']),
           years_of_experience: z.number().min(0).nullable(),
           last_used: z.coerce.date().nullable(),
           level: z.enum(['Beginner', 'Intermediate', 'Advanced', 'Expert']).nullable()
         })
       })
     )
     ```

2. Projects Section:
   - Add repository URL field (GitHub/GitLab)
   - Include tech_stack as a structured array
   - Add team_size and role fields
   - Add challenges_solved field
   - Add project_impact metrics

3. Work Experience:
   - Add technologies_used array
   - Include team_size and role within team
   - Separate achievements from responsibilities
   - Add metrics/impact of work
   - Add project_highlights for key projects

4. Education:
   - Add gpa field with validation
   - Include thesis_title for higher education
   - Add relevant_coursework array
   - Add honors/awards specific to education

5. Additional Sections to Consider:
   - Open Source Contributions
   - Technical Certifications (with verification URL)
   - Technical Writing/Publications
   - Conference Talks/Workshops
   - Patents
   - Side Projects
   - Hackathon Participation
   - Technical Blog/Content Creation

6. Validation Improvements:
   - URL validation for GitHub, LinkedIn, etc.
   - Date range validation (end_date > start_date)
   - Email domain validation
   - Duplicate detection for entries

7. Performance Considerations:
   - Add indexes for frequently queried fields
   - Consider search optimization for text fields
   - Add pagination support for large arrays
   - Consider denormalization for read performance

8. Localization Support:
   - Add language field for multilingual support
   - Consider timezone handling for dates
   - Support for international phone numbers
   - Address formatting for different regions

9. Privacy & Compliance:
   - Add data sensitivity level
   - Include consent management fields
   - Add data retention policy information
   - Support for right to be forgotten

10. Integration Hooks:
    - Add external_system_ids for integration
    - Include sync timestamps
    - Add versioning support
    - Include change history

These enhancements would make the schema more comprehensive for software engineering profiles
while maintaining flexibility and performance.
*/
