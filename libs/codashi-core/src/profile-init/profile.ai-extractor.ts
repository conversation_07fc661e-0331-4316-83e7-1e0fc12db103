import { BaseChatModel } from '@langchain/core/language_models/chat_models.js';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import {
  RunnableMap,
  RunnableParallel,
  RunnableSequence,
} from '@langchain/core/runnables';
import { z } from 'zod';

import { UnexpectedError } from '@awe/core';

import { type ProfileDraft, profileDraft, toDraft } from './resume.draft.js';

export const extractProfileV2Stream = async function* (
  resumeText: string,
  model: BaseChatModel
): AsyncGenerator<ProfileExtractionStreamEvent> {
  // 1. Identify sections
  const sectionIdentifier = createSectionIdentifier(model);
  const { sections } = await sectionIdentifier.invoke({ resumeText });
  yield { type: 'sections_identified', sections };

  // 2. Set up parallel extraction
  const parallelExtraction: Record<string, RunnableSequence | RunnableMap> = {};

  for (const section of sections) {
    if (section.name === 'work') {
      parallelExtraction[section.name] = createWorkSectionsExtractorV2(model);
    } else {
      parallelExtraction[section.name] = createSimpleSectionExtractor(
        section.name,
        model
      );
    }
  }
  const extractionPipeline = RunnableParallel.from(parallelExtraction);

  const sectionInputs = sections.reduce((acc, s) => {
    acc[s.name] = { content: s.content };
    return acc;
  }, {} as Record<Section['name'], Pick<Section, 'content'>>);

  // 3. Stream results and merge
  const fullProfile: Partial<ProfileDraft> = {};
  const stream = await extractionPipeline.stream(sectionInputs);

  for await (const chunk of stream) {
    // The chunk is an object with a single key, e.g., { work: { ... } }
    const sectionName = Object.keys(chunk)[0];

    if (!sectionName) continue;

    const sectionData = chunk[sectionName] as Partial<ProfileDraft>;

    Object.assign(fullProfile, sectionData);

    yield {
      type: 'extraction_result',
      section: sectionName,
      data: sectionData,
    };
  }

  yield { type: 'final_result', profile: toDraft(fullProfile) };
};

const simpleSections = z.object({
  name: z.enum([
    'education',
    'skills',
    'personal_info',
    'summary',
    'projects',
    'awards',
    'publications',
    'references',
    'other',
    'volunteer',
    'languages',
    'profiles',
    'interests',
    'certificates',
  ]),
  content: z.string(),
});

const workSection = z.object({
  name: z.literal('work'),
  content: z.string(),
});

const sectionSchema = z.discriminatedUnion('name', [
  simpleSections,
  workSection,
]);

type SimpleSection = z.infer<typeof simpleSections>;
type WorkSection = z.infer<typeof workSection>;
type Section = SimpleSection | WorkSection;

const identifiedSectionsSchema = z.object({
  sections: z.array(sectionSchema),
});

const createSectionIdentifier = (model: BaseChatModel) => {
  const parser = StructuredOutputParser.fromZodSchema(identifiedSectionsSchema);

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert resume parser. Your task is to identify and segment the following resume text into logical sections.

Possible sections are: ${simpleSections.shape.name.options.join(', ')},${
      workSection.shape.name.value
    }.

Resume Text:
{resumeText}

{format_instructions}

Make sure not to get confused and treat separate work entries as separate sections, they should be a part
of the same "work" section.

Respond ONLY with the structured JSON output.`
  );

  return RunnableSequence.from([
    {
      resumeText: (input: { resumeText: string }) => input.resumeText,
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

const createWorkEntrySplitter = (model: BaseChatModel) => {
  const splitterPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert resume parser. Split the following text into an array of individual work experience entries. 
Return ONLY a JSON array of strings, each string representing one work experience entry (no explanations).

Section Text:
{work_content}`
  );
  const arrayParser = StructuredOutputParser.fromZodSchema(
    z.array(z.string()).transform((entries) =>
      entries.map((data, index) => {
        return `${index + 1}: ${data}`;
      })
    )
  );
  return RunnableSequence.from([
    {
      work_content: (input: Record<'work', Pick<WorkSection, 'content'>>) =>
        input.work.content,
    },
    splitterPrompt,
    model,
    arrayParser,
  ]);
};

const createWorkSectionsExtractorV2 = (model: BaseChatModel) => {
  const entryPrompt = ChatPromptTemplate.fromTemplate(
    `You are an expert resume parser. Extract all work experience entries from the provided text.

{work_entries}

---

{format_instructions}
Respond ONLY with the structured JSON output.`
  );
  const entryParser = StructuredOutputParser.fromZodSchema(
    profileDraft.pick({ work: true })
  );
  const extractor = RunnableSequence.from([
    {
      work_entries: (input: { work_entries: string }) => input.work_entries,
      format_instructions: () => entryParser.getFormatInstructions(),
    },
    entryPrompt,
    model,
    entryParser,
  ]);

  // The main sequence: split with model, extract each, aggregate
  return RunnableSequence.from([
    // Step 1: split the work section into entries using a model call
    async (input: Record<'work', Pick<WorkSection, 'content'>>) => {
      const splitter = createWorkEntrySplitter(model);
      const entries = await splitter.invoke(input);

      return entries;
    },
    // Step 2: extract each entry
    async (entries: string[]) => {
      const result = await extractor.invoke({
        work_entries: entries.join('\n'),
      });

      return result;
    },
    // Step 3: aggregate into the expected output
    (entries) => entries,
  ]);
};

const createSimpleSectionExtractor = (
  sectionName: SimpleSection['name'],
  model: BaseChatModel
) => {
  // Define a schema for each section to keep prompts focused
  const sectionSchemas = {
    personal_info: profileDraft.pick({
      name: true,
      title: true,
      location: true,
      email: true,
      phone: true,
      url: true,
    }),
    summary: profileDraft.pick({ summary: true }),
    education: profileDraft.pick({ education: true }),
    skills: profileDraft.pick({ skills: true }),
    projects: profileDraft.pick({ projects: true }),
    awards: profileDraft.pick({ awards: true }),
    publications: profileDraft.pick({ publications: true }),
    references: profileDraft.pick({ references: true }),
    volunteer: profileDraft.pick({ volunteer: true }),
    languages: profileDraft.pick({ languages: true }),
    profiles: profileDraft.pick({ profiles: true }),
    interests: profileDraft.pick({ interests: true }),
    certificates: profileDraft.pick({ certificates: true }),
    other: z.object({}), // No specific extraction for 'other'
  };

  const schema = sectionSchemas[sectionName];

  if (!schema) {
    throw new UnexpectedError(`No schema found for section: ${sectionName}`);
  }

  const parser = StructuredOutputParser.fromZodSchema(schema);

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert resume parser. Extract information for the "{section}" section based on the text provided.

Section Text:
{section_content}

{format_instructions}

Respond ONLY with the structured JSON output.`
  );

  return RunnableSequence.from([
    {
      section: () => sectionName,
      section_content: (
        input: Record<Section['name'], Pick<SimpleSection, 'content'>>
      ) => {
        return input[sectionName].content;
      },
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

export type ProfileExtractionStreamEvent =
  | { type: 'sections_identified'; sections: Section[] }
  | { type: 'extraction_result'; section: string; data: Partial<ProfileDraft> }
  | { type: 'final_result'; profile: ProfileDraft };
