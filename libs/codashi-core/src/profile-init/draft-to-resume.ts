import { Primitive } from '@awe/core';
import { resume, Resume } from '../entities/resume';
import { ProfileDraft } from './resume.draft';

const CONFIDENCE_THRESHOLD = 0.65;

export const draftToResume = (draft: ProfileDraft): Resume => {
  // Location: extract .value and map nulls to undefined for Resume type
  const bestLocationEntry =
    draft.location && draft.location.length > 0
      ? draft.location.reduce((a, b) =>
          a.ai_confidence > b.ai_confidence ? a : b
        )
      : undefined;

  const location =
    bestLocationEntry && bestLocationEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? {
          address: bestLocationEntry.value.address ?? PLACEHOLDERS.address,
          postal_code:
            bestLocationEntry.value.postal_code ?? PLACEHOLDERS.postalCode,
          city: bestLocationEntry.value.city ?? PLACEHOLDERS.city,
          country_code:
            bestLocationEntry.value.country_code ?? PLACEHOLDERS.countryCode,
          region: bestLocationEntry.value.region ?? PLACEHOLDERS.region,
        }
      : null;

  const work =
    draft.work.map((w) => ({
      name: w.value.name ?? PLACEHOLDERS.workCompany,
      position: w.value.position ?? PLACEHOLDERS.workPosition,
      url: w.value.url,
      start_date: w.value.start_date?.toISOString() ?? PLACEHOLDERS.startDate,
      end_date: w.value.end_date?.toISOString() ?? PLACEHOLDERS.endDate,
      summary: w.value.summary ?? PLACEHOLDERS.workSummary,
      highlights: w.value.highlights?.length ? w.value.highlights : null,
      location: w.value.location ?? PLACEHOLDERS.location,
      description: w.value.description ?? PLACEHOLDERS.workDescription,
      positions: null,
    })) ?? [];

  const skills = draft.skills
    .filter((s) => s.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((s) => ({
      name: s.value.name ?? PLACEHOLDERS.skillName,
      level: s.value.level ?? PLACEHOLDERS.skillLevel,
      keywords: s.value.keywords?.length ? s.value.keywords : null,
    }));

  const education = draft.education
    .filter((e) => e.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((e) => ({
      institution: e.value.institution ?? PLACEHOLDERS.educationInstitution,
      area: e.value.area ?? PLACEHOLDERS.educationArea,
      study_type: e.value.study_type ?? PLACEHOLDERS.educationStudyType,
      url: e.value.url ?? PLACEHOLDERS.url,
      start_date:
        e.value.start_date instanceof Date
          ? e.value.start_date.toISOString()
          : e.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        e.value.end_date instanceof Date
          ? e.value.end_date.toISOString()
          : e.value.end_date ?? PLACEHOLDERS.endDate,
      score: e.value.score ?? PLACEHOLDERS.educationScore,
      courses: e.value.courses?.length ? e.value.courses : null,
    }));

  const awards = draft.awards
    .filter((a) => a.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((a) => ({
      title: a.value.title ?? PLACEHOLDERS.awardTitle,
      date:
        a.value.date instanceof Date
          ? a.value.date.toISOString()
          : a.value.date ?? PLACEHOLDERS.awardDate,
      awarder: a.value.awarder ?? PLACEHOLDERS.awarder,
      summary: a.value.summary ?? PLACEHOLDERS.awardSummary,
    }));

  const languages = draft.languages
    .filter((l) => l.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((l) => ({
      language: l.value.language ?? PLACEHOLDERS.language,
      fluency: l.value.fluency ?? PLACEHOLDERS.fluency,
    }));

  const projects = draft.projects
    .filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((p) => ({
      name: p.value.name ?? PLACEHOLDERS.projectName,
      description: p.value.description ?? PLACEHOLDERS.projectDescription,
      highlights: p.value.highlights?.length ? p.value.highlights : null,
      keywords: p.value.keywords?.length ? p.value.keywords : null,
      start_date:
        p.value.start_date instanceof Date
          ? p.value.start_date.toISOString()
          : p.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        p.value.end_date instanceof Date
          ? p.value.end_date.toISOString()
          : p.value.end_date ?? PLACEHOLDERS.endDate,
      url: p.value.url ?? PLACEHOLDERS.url,
      roles: p.value.roles?.length ? p.value.roles : null,
      entity: p.value.entity ?? PLACEHOLDERS.projectEntity,
      type: p.value.type ?? PLACEHOLDERS.projectType,
    }));

  const certificates = draft.certificates
    .filter((c) => c.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((c) => ({
      name: c.value.name ?? PLACEHOLDERS.certificateName,
      date:
        c.value.date instanceof Date
          ? c.value.date.toISOString()
          : c.value.date ?? PLACEHOLDERS.certificateDate,
      url: c.value.url ?? PLACEHOLDERS.url,
      issuer: c.value.issuer ?? PLACEHOLDERS.certificateIssuer,
    }));

  const references = draft.references
    .filter((r) => r.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((r) => ({
      name: r.value.name ?? PLACEHOLDERS.referenceName,
      reference: r.value.reference ?? PLACEHOLDERS.referenceText,
      email: r.value.email ?? PLACEHOLDERS.email,
      phone: r.value.phone ?? PLACEHOLDERS.phone,
      position: r.value.position ?? PLACEHOLDERS.workPosition,
      company: r.value.company ?? PLACEHOLDERS.workCompany,
    }));

  const interests = draft.interests
    .filter((i) => i.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((i) => ({
      name: i.value.name ?? PLACEHOLDERS.interestName,
      keywords: i.value.keywords?.length ? i.value.keywords : null,
    }));

  const publications = draft.publications
    .filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((p) => ({
      name: p.value.name ?? PLACEHOLDERS.publicationName,
      publisher: p.value.publisher ?? PLACEHOLDERS.publicationPublisher,
      release_date:
        p.value.release_date instanceof Date
          ? p.value.release_date.toISOString()
          : p.value.release_date ?? PLACEHOLDERS.publicationDate,
      url: p.value.url ?? PLACEHOLDERS.url,
      summary: p.value.summary ?? PLACEHOLDERS.publicationSummary,
    }));

  const volunteer = draft.volunteer
    .filter((v) => v.ai_confidence >= CONFIDENCE_THRESHOLD)
    .map((v) => ({
      organization: v.value.organization ?? PLACEHOLDERS.volunteerOrganization,
      position: v.value.position ?? PLACEHOLDERS.volunteerPosition,
      url: v.value.url ?? PLACEHOLDERS.url,
      start_date:
        v.value.start_date instanceof Date
          ? v.value.start_date.toISOString()
          : v.value.start_date ?? PLACEHOLDERS.startDate,
      end_date:
        v.value.end_date instanceof Date
          ? v.value.end_date.toISOString()
          : v.value.end_date ?? PLACEHOLDERS.endDate,
      summary: v.value.summary ?? PLACEHOLDERS.volunteerSummary,
      highlights: v.value.highlights?.length ? v.value.highlights : null,
    }));

  // Helper functions for type-safe header item creation
  const getStringValue = (
    arr: { value: string; ai_confidence: number }[] | undefined,
    placeholder: string
  ): string => {
    return pickBestByConfidence(arr, placeholder) as string;
  };

  const getStringOrNullValue = (
    arr: { value: string; ai_confidence: number }[] | undefined,
    placeholder: string | null
  ): string | null => {
    return pickBestByConfidence(arr, placeholder) as string | null;
  };

  return resume.parse({
    header: {
      items: [
        {
          name: 'name' as const,
          value: getStringValue(draft.name, PLACEHOLDERS.name),
        },
        {
          name: 'title' as const,
          value: getStringValue(draft.title, PLACEHOLDERS.label),
        },
        {
          name: 'email' as const,
          value: getStringValue(draft.email, PLACEHOLDERS.email),
        },
        {
          name: 'phone' as const,
          value: getStringOrNullValue(draft.phone, PLACEHOLDERS.phone),
        },
        {
          name: 'url' as const,
          value: getStringOrNullValue(draft.url, PLACEHOLDERS.url),
        },
        {
          name: 'location' as const,
          value: location,
        },
        {
          name: 'summary' as const,
          value: getStringOrNullValue(draft.summary, PLACEHOLDERS.summary),
        },
        ...(draft.profiles
          ?.filter((p) => p.ai_confidence >= CONFIDENCE_THRESHOLD)
          .map((p) => ({
            name: 'profile' as const,
            value: {
              url: p.value.url ?? PLACEHOLDERS.url,
              network: p.value.network ?? PLACEHOLDERS.network,
              username: p.value.username ?? PLACEHOLDERS.username,
            },
          })) ?? []),
      ],
    },
    sections: [
      { name: 'work' as const, items: work },
      { name: 'skills' as const, items: skills },
      { name: 'education' as const, items: education },
      { name: 'awards' as const, items: awards },
      { name: 'languages' as const, items: languages },
      { name: 'projects' as const, items: projects },
      { name: 'certificates' as const, items: certificates },
      { name: 'references' as const, items: references },
      { name: 'interests' as const, items: interests },
      { name: 'publications' as const, items: publications },
      { name: 'volunteer' as const, items: volunteer },
    ].filter((section) => section.items.length > 0), // Only include sections with items
    $schema: null,
    meta: {
      last_modified: new Date().toISOString(),
      version: '1.0',
      canonical: null,
      created_at: new Date().toISOString(),
      job_description: null,
    },
  } satisfies Primitive<Resume>);
};

const pickBestByConfidence = <
  T extends { value: V; ai_confidence: number },
  V,
  P extends V
>(
  arr: T[] | undefined,
  placeholder: P,
  minConfidence = CONFIDENCE_THRESHOLD
): V => {
  if (!arr || arr.length === 0) return placeholder;

  const best = arr.reduce((a, b) =>
    a.ai_confidence > b.ai_confidence ? a : b
  );

  return best.ai_confidence >= minConfidence ? best.value : placeholder;
};

// defaults can differ a bit depending on the situation - for parts we could consider critical, e.g. - we shouldn't have a resume without an email or work experience without at least our position - we provide a string fallback which should urge the person to edit it to a proper value. Otherwise for non critical parts we provide null as default
// Only include placeholders for required fields in the resume schema
// Only include placeholders for required fields in the resume schema
// All section fields are optional in the schema, so they don't need placeholders
const PLACEHOLDERS = {
  // Required fields in basics
  name: 'Name not provided',
  label: 'Title not provided', // maps to title in basics
  email: 'Email not provided',

  // Optional fields - no placeholders needed as they're optional in the schema
  phone: null,
  url: 'https://www.example.com',
  summary: null,
  workCompany: 'Unknown company',
  workPosition: 'Position not provided',
  workSummary: null,
  skillName: 'Skill not provided',
  network: 'unknown network',
  username: null,
  skillLevel: null,
  startDate: new Date().toISOString(), // Required field, provide current date as fallback
  endDate: null,
  city: null,
  postalCode: null,
  address: null,
  countryCode: 'US',
  region: null,
  location: null,
  workDescription: null,
  educationInstitution: 'Institution not provided',
  educationArea: 'Area not provided',
  educationStudyType: null,
  educationScore: null,
  awardTitle: 'Award not provided',
  awardDate: new Date().toISOString(), // Required field
  awarder: 'Awarder not provided',
  awardSummary: 'Summary not provided',
  language: 'Language not provided',
  fluency: 'Fluency not provided',
  projectName: 'Project not provided',
  projectDescription: 'Description not provided',
  projectEntity: null,
  projectType: 'Type not provided',
  certificateName: 'Certificate not provided',
  certificateDate: new Date().toISOString(), // Required field
  certificateIssuer: 'Issuer not provided',
  referenceName: 'Reference not provided',
  referenceText: null,
  interestName: 'Interest not provided',
  publicationName: 'Publication not provided',
  publicationPublisher: null,
  publicationDate: new Date().toISOString(), // Required field
  publicationSummary: 'Summary not provided',
  volunteerOrganization: null,
  volunteerPosition: 'Position not provided',
  volunteerSummary: 'Summary not provided',
} as const;
