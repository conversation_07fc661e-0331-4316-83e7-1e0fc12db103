import { describe, expect, it } from 'vitest';

import { yamlToResume } from '../resume.yaml';

describe('resume schema', () => {
  it('should successfully parse valid YAML to Resume object', () => {
    const validYaml = `
      meta:
        job_description: "Software Engineer"
        created_at: "2023-01-01"
        last_modified: "2023-01-01"
        version: "1.0.0"
        canonical: "https://janesmith.dev/resume.json"
      header:
        items:
          - name: "name"
            value: "<PERSON>"
          - name: "title"
            value: "Software Engineer"
          - name: "email"
            value: "<EMAIL>"
          - name: "phone"
            value: null
      sections:
        - name: "work"
          items:
            - name: "Tech Corp"
              position: "Senior Developer"
              start_date: "2020-01-01"
              end_date: "2023-01-01"
              highlights:
                - "Led team of 5 developers"
                - "Implemented CI/CD pipeline"
        - name: "skills"
          items:
            - name: "JavaScript"
              level: "Expert"
              keywords:
                - "React"
                - "Node.js"
                - "TypeScript"
    `;

    const result = yamlToResume(validYaml);

    if (!result.success) {
      console.log('Validation errors:', result.errors);
    }

    expect(result.success).toBe(true);

    if (result.success) {
      expect(result.data).toEqual({
        $schema: null,
        meta: {
          job_description: 'Software Engineer',
          created_at: '2023-01-01',
          last_modified: '2023-01-01',
          version: '1.0.0',
          canonical: 'https://janesmith.dev/resume.json',
        },
        header: {
          items: [
            {
              name: 'name',
              value: 'John Doe',
            },
            {
              name: 'title',
              value: 'Software Engineer',
            },
            {
              name: 'email',
              value: '<EMAIL>',
            },
            {
              name: 'phone',
              value: null,
            },
          ],
        },
        sections: [
          {
            name: 'work',
            items: [
              {
                name: 'Tech Corp',
                location: null,
                description: null,
                position: 'Senior Developer',
                url: null,
                start_date: '2020-01-01',
                end_date: '2023-01-01',
                summary: null,
                highlights: [
                  'Led team of 5 developers',
                  'Implemented CI/CD pipeline',
                ],
                positions: null,
              },
            ],
          },
          {
            name: 'skills',
            items: [
              {
                name: 'JavaScript',
                level: 'Expert',
                keywords: ['React', 'Node.js', 'TypeScript'],
              },
            ],
          },
        ],
      });
    }
  });

  it('should successfully parse a comprehensive resume with all possible fields', () => {
    const comprehensiveYaml = `
      header:
        items:
          - name: "name"
            value: "Jane Smith"
          - name: "title"
            value: "Senior Software Engineer"
          - name: "email"
            value: "<EMAIL>"
          - name: "phone"
            value: "(*************"
          - name: "url"
            value: "https://janesmith.dev"
          - name: "summary"
            value: |
              Experienced software engineer with a focus on **cloud architecture** and {{technology}} systems.
              Over 10 years of industry experience building scalable applications.
          - name: "location"
            value:
              address: 123 Tech Street
              postal_code: "10001"
              city: New York
              country_code: US
              region: NY
          - name: "profile"
            value:
              network: LinkedIn
              username: janesmith
              url: https://linkedin.com/in/janesmith
          - name: "profile"
            value:
              network: GitHub
              username: janesmith
              url: https://github.com/janesmith
          - name: "profile"
            value:
              network: Twitter
              username: janesmith
              url: https://twitter.com/janesmith

      sections:
        - name: "work"
          items:
            - name: "Tech Solutions Inc."
              location: "New York, NY"
              description: "A leading provider of enterprise software solutions."
              position: "Lead Software Architect"
              url: "https://techsolutions.example.com"
              start_date: "2018-01-01"
              end_date: "2023-01-01"
              summary: "Led the architecture and development of the company's flagship product."
              highlights:
                - "Increased system performance by 40%"
                - "Reduced deployment time from days to minutes"
                - "Implemented CI/CD pipeline using GitHub Actions"

        - name: "volunteer"
          items:
            - organization: "Code for Good"
              position: "Mentor"
              url: "https://codeforgood.org"
              start_date: "2019-01-01"
              end_date: "2022-12-31"
              summary: "Mentored underprivileged youth in software development"
              highlights:
                - "Taught web development to 50+ students"
                - "Organized annual hackathons"

        - name: "education"
          items:
            - institution: "University of Technology"
              url: "https://uotech.edu"
              area: "Computer Science"
              study_type: "Bachelor"
              start_date: "2011-09-01"
              end_date: "2015-05-31"
              score: "3.8 GPA"
              courses:
                - "Data Structures and Algorithms"
                - "Operating Systems"
                - "Database Management"

        - name: "awards"
          items:
            - title: "Innovation Award"
              date: "2022-06-15"
              awarder: "Tech Industry Association"
              summary: "Recognized for innovative approach to cloud architecture"

        - name: "certificates"
          items:
            - name: "AWS Solutions Architect"
              date: "2021-05-20"
              url: "https://aws.amazon.com/certification"
              issuer: "Amazon Web Services"

        - name: "publications"
          items:
            - name: "Modern Microservice Architecture"
              publisher: "Tech Publishing Co."
              release_date: "2021-11-15"
              url: "https://example.com/publication"
              summary: "A comprehensive guide to designing microservice architectures"

        - name: "skills"
          items:
            - name: "Programming Languages"
              level: "Expert"
              keywords:
                - "JavaScript"
                - "TypeScript"
                - "Python"
                - "Go"
            - name: "Cloud Technologies"
              level: "Expert"
              keywords:
                - "AWS"
                - "Google Cloud"
                - "Azure"
                - "Kubernetes"

        - name: "languages"
          items:
            - language: "English"
              fluency: "Native"
            - language: "Spanish"
              fluency: "Professional"

        - name: "interests"
          items:
            - name: "Open Source"
              keywords:
                - "Contributing"
                - "Maintaining"
                - "Community Building"

        - name: "references"
          items:
            - name: "Alex Johnson"
              reference: "Jane is an exceptional engineer and leader."
              email: "<EMAIL>"
              phone: "5551234567"
              position: "CTO"
              company: "Tech Solutions Inc."

        - name: "projects"
          items:
            - name: "Cloud Migration Framework"
              description: "An open-source framework for migrating legacy applications to the cloud."
              highlights:
                - "1000+ GitHub stars"
                - "Used by 50+ companies"
              keywords:
                - "Cloud"
                - "Migration"
                - "Open Source"
              start_date: "2020-01-01"
              end_date: "2022-06-30"
              url: "https://github.com/janesmith/cloud-migration"
              roles:
                - "Creator"
                - "Maintainer"
              entity: "Personal Project"
              type: "Open Source"

      meta:
        canonical: "https://janesmith.dev/resume.json"
        version: "1.0.0"
        last_modified: "2023-05-01"
        created_at: "2023-01-01"
        job_description: "Software Engineer"
    `;

    const result = yamlToResume(comprehensiveYaml);

    if (!result.success) {
      console.log('Validation errors:', result.errors);
    }

    expect(result.success).toBe(true);

    if (result.success) {
      const { data } = result;

      // Test header items
      expect(data.header.items).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ name: 'name', value: 'Jane Smith' }),
          expect.objectContaining({
            name: 'title',
            value: 'Senior Software Engineer',
          }),
          expect.objectContaining({ name: 'email', value: '<EMAIL>' }),
          expect.objectContaining({ name: 'phone', value: '(*************' }),
          expect.objectContaining({
            name: 'url',
            value: 'https://janesmith.dev',
          }),
          expect.objectContaining({
            name: 'summary',
            value: expect.stringContaining('Experienced software engineer'),
          }),
          expect.objectContaining({
            name: 'location',
            value: {
              address: '123 Tech Street',
              postal_code: '10001',
              city: 'New York',
              country_code: 'US',
              region: 'NY',
            },
          }),
          expect.objectContaining({
            name: 'profile',
            value: expect.objectContaining({
              network: 'LinkedIn',
              username: 'janesmith',
              url: 'https://linkedin.com/in/janesmith',
            }),
          }),
          expect.objectContaining({
            name: 'profile',
            value: expect.objectContaining({
              network: 'GitHub',
              username: 'janesmith',
              url: 'https://github.com/janesmith',
            }),
          }),
          expect.objectContaining({
            name: 'profile',
            value: expect.objectContaining({
              network: 'Twitter',
              username: 'janesmith',
              url: 'https://twitter.com/janesmith',
            }),
          }),
        ])
      );

      // Test sections
      const getSection = (name: string) =>
        data.sections.find((section) => section.name === name);

      // Test work section
      const workSection = getSection('work');
      expect(workSection).toBeDefined();
      if (workSection) {
        expect(workSection.items[0]).toMatchObject({
          name: 'Tech Solutions Inc.',
          location: 'New York, NY',
          position: 'Lead Software Architect',
          url: 'https://techsolutions.example.com',
          start_date: '2018-01-01',
          end_date: '2023-01-01',
          highlights: expect.arrayContaining([
            'Increased system performance by 40%',
            'Reduced deployment time from days to minutes',
            'Implemented CI/CD pipeline using GitHub Actions',
          ]),
        });
      }

      // Test skills section
      const skillsSection = getSection('skills');
      expect(skillsSection).toBeDefined();
      if (skillsSection) {
        expect(skillsSection.items).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              name: 'Programming Languages',
              level: 'Expert',
              keywords: expect.arrayContaining([
                'JavaScript',
                'TypeScript',
                'Python',
                'Go',
              ]),
            }),
          ])
        );
      }

      // Test projects section
      const projectsSection = getSection('projects');
      expect(projectsSection).toBeDefined();
      if (projectsSection) {
        expect(projectsSection.items[0]).toMatchObject({
          name: 'Cloud Migration Framework',
          description: expect.stringContaining('open-source framework'),
          highlights: expect.arrayContaining(['1000+ GitHub stars']),
          keywords: expect.arrayContaining([
            'Cloud',
            'Migration',
            'Open Source',
          ]),
          start_date: '2020-01-01',
          end_date: '2022-06-30',
          url: 'https://github.com/janesmith/cloud-migration',
          roles: expect.arrayContaining(['Creator', 'Maintainer']),
          entity: 'Personal Project',
          type: 'Open Source',
        });
      }
    }
  });
});
