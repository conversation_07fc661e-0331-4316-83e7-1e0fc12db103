import { type IsoDate } from '@awe/core';
import { describe, expect, it } from 'vitest';

import { Resume, toResume } from '../../entities/resume';
import { resumeToYaml } from '../resume.yaml';

describe('resumeToYaml', () => {
  it('should successfully convert a basic Resume object to YAML', () => {
    const basicResume = toResume.fromPartial({
      meta: {
        job_description: 'Software Engineer',
        created_at: '2023-01-01' as IsoDate,
        last_modified: '2023-01-01' as IsoDate,
        version: '1.0.0',
        canonical: 'https://janesmith.dev/resume.json',
      },
      header: {
        items: [
          {
            name: 'name',
            value: '<PERSON>',
          },
          {
            name: 'email',
            value: '<EMAIL>',
          },
          {
            name: 'summary',
            value: 'A professional software developer',
          },
        ],
      },
      sections: [
        {
          name: 'skills',
          items: [
            {
              name: 'JavaScript',
              level: 'Expert',
              keywords: ['React', 'Node.js', 'TypeScript'],
            },
          ],
        },
      ],
    });

    const result = resumeToYaml(basicResume);

    expect(result).toContain('value: <PERSON>');
    expect(result).toContain('value: <EMAIL>');
    expect(result).toContain('value: A professional software developer');
    expect(result).toContain('name: JavaScript');
    expect(result).toContain('level: Expert');
    expect(result).toContain('React');
    expect(result).toContain('Node.js');
    expect(result).toContain('TypeScript');
  });

  it('should handle empty Resume object', () => {
    expect(() => toResume.fromPartial({})).toThrow();
  });

  it('should successfully convert a comprehensive Resume object to YAML', () => {
    const comprehensiveResume = {
      meta: {
        job_description: 'Senior Software Engineer',
        created_at: '2023-01-01' as IsoDate,
        last_modified: '2023-06-15' as IsoDate,
        version: '1.0.0',
        canonical: 'https://janesmith.dev/resume.json',
      },
      header: {
        items: [
          { name: 'name', value: 'Jane Smith' },
          { name: 'title', value: 'Senior Software Engineer' },
          { name: 'email', value: '<EMAIL>' },
          { name: 'phone', value: '(*************' },
          { name: 'url', value: 'https://janesmith.dev' },
          {
            name: 'summary',
            value:
              'Experienced software engineer with a focus on **cloud architecture** and {{technology}} systems. Over 10 years of industry experience building scalable applications.',
          },
          {
            name: 'location',
            value: {
              address: '123 Tech Street',
              postal_code: '10001',
              city: 'New York',
              country_code: 'US',
              region: 'NY',
            },
          },
          {
            name: 'profile',
            value: {
              network: 'LinkedIn',
              username: 'janesmith',
              url: 'https://linkedin.com/in/janesmith',
            },
          },
          {
            name: 'profile',
            value: {
              network: 'GitHub',
              username: 'janesmith',
              url: 'https://github.com/janesmith',
            },
          },
          {
            name: 'profile',
            value: {
              network: 'Twitter',
              username: 'janesmith',
              url: 'https://twitter.com/janesmith',
            },
          },
        ],
      },
      sections: [
        {
          name: 'work',
          items: [
            {
              name: 'Tech Solutions Inc.',
              location: 'New York, NY',
              description:
                'A leading provider of enterprise software solutions.',
              position: 'Lead Software Architect',
              url: 'https://techsolutions.example.com',
              start_date: '2018-01-01' as IsoDate,
              end_date: '2023-01-01' as IsoDate,
              summary:
                "Led the architecture and development of the company's flagship product.",
              highlights: [
                'Increased system performance by 40%',
                'Reduced deployment time from days to minutes',
                'Implemented CI/CD pipeline using GitHub Actions',
              ],
              positions: null,
            },
          ],
        },
        {
          name: 'volunteer',
          items: [
            {
              organization: 'Code for Good',
              position: 'Mentor',
              url: 'https://codeforgood.org',
              start_date: '2019-01-01' as IsoDate,
              end_date: '2022-12-31' as IsoDate,
              summary: 'Mentored underprivileged youth in software development',
              highlights: [
                'Taught web development to 50+ students',
                'Organized annual hackathons',
              ],
            },
          ],
        },
        {
          name: 'education',
          items: [
            {
              institution: 'University of Technology',
              url: 'https://uotech.edu',
              area: 'Computer Science',
              study_type: 'Bachelor',
              start_date: '2011-09-01' as IsoDate,
              end_date: '2015-05-31' as IsoDate,
              score: '3.8 GPA',
              courses: [
                'Data Structures and Algorithms',
                'Operating Systems',
                'Database Management',
              ],
            },
          ],
        },
        {
          name: 'awards',
          items: [
            {
              title: 'Employee of the Year',
              date: '2022-12-01' as IsoDate,
              awarder: 'Tech Solutions Inc.',
              summary:
                "Recognized for outstanding contributions to the company's success.",
            },
          ],
        },
        {
          name: 'publications',
          items: [
            {
              name: 'Building Scalable Microservices',
              publisher: 'Tech Journal',
              release_date: '2022-06-15' as IsoDate,
              summary:
                'A comprehensive guide to building scalable microservices architecture.',
              url: 'https://techjournal.com/building-scalable-microservices',
            },
          ],
        },
        {
          name: 'skills',
          items: [
            {
              name: 'JavaScript',
              level: 'Expert',
              keywords: ['React', 'Node.js', 'TypeScript'],
            },
            {
              name: 'Cloud',
              level: 'Advanced',
              keywords: ['AWS', 'Docker', 'Kubernetes'],
            },
          ],
        },
        {
          name: 'languages',
          items: [
            {
              language: 'English',
              fluency: 'Native speaker',
            },
            {
              language: 'Spanish',
              fluency: 'Intermediate',
            },
          ],
        },
        {
          name: 'interests',
          items: [
            {
              name: 'Open Source',
              keywords: ['Contributor', 'Maintainer'],
            },
            {
              name: 'Photography',
              keywords: ['Landscape', 'Portrait'],
            },
          ],
        },
        {
          name: 'references',
          items: [
            {
              name: 'John Doe',
              reference:
                'Jane was an outstanding employee who always delivered high-quality work.',
              email: '<EMAIL>',
              phone: '(*************',
              position: 'Software Engineer',
              company: 'Tech Solutions Inc.',
            },
          ],
        },
        {
          name: 'projects',
          items: [
            {
              name: 'Resume Builder',
              description: 'An open-source resume builder application',
              highlights: [
                'Built with React and TypeScript',
                'Supports multiple export formats',
              ],
              keywords: ['React', 'TypeScript', 'Open Source'],
              start_date: '2022-01-01' as IsoDate,
              end_date: '2022-12-31' as IsoDate,
              url: 'https://github.com/username/resume-builder',
              roles: ['Lead Developer', 'UI/UX Designer'],
              entity: 'Personal Project',
              type: 'application',
            },
          ],
        },
      ],
      $schema: null,
    } satisfies Resume;

    const result = resumeToYaml(comprehensiveResume);

    const expectedYaml = `meta:
  job_description: Senior Software Engineer
  created_at: 2023-01-01
  last_modified: 2023-06-15
  version: 1.0.0
  canonical: https://janesmith.dev/resume.json
header:
  items:
    - name: name
      value: Jane Smith
    - name: title
      value: Senior Software Engineer
    - name: email
      value: <EMAIL>
    - name: phone
      value: (*************
    - name: url
      value: https://janesmith.dev
    - name: summary
      value: Experienced software engineer with a focus on **cloud architecture** and
        {{technology}} systems. Over 10 years of industry experience building
        scalable applications.
    - name: location
      value:
        address: 123 Tech Street
        postal_code: "10001"
        city: New York
        country_code: US
        region: NY
    - name: profile
      value:
        network: LinkedIn
        username: janesmith
        url: https://linkedin.com/in/janesmith
    - name: profile
      value:
        network: GitHub
        username: janesmith
        url: https://github.com/janesmith
    - name: profile
      value:
        network: Twitter
        username: janesmith
        url: https://twitter.com/janesmith
sections:
  - name: work
    items:
      - name: Tech Solutions Inc.
        location: New York, NY
        description: A leading provider of enterprise software solutions.
        position: Lead Software Architect
        url: https://techsolutions.example.com
        start_date: 2018-01-01
        end_date: 2023-01-01
        summary: Led the architecture and development of the company's flagship product.
        highlights:
          - Increased system performance by 40%
          - Reduced deployment time from days to minutes
          - Implemented CI/CD pipeline using GitHub Actions
        positions: null
  - name: volunteer
    items:
      - organization: Code for Good
        position: Mentor
        url: https://codeforgood.org
        start_date: 2019-01-01
        end_date: 2022-12-31
        summary: Mentored underprivileged youth in software development
        highlights:
          - Taught web development to 50+ students
          - Organized annual hackathons
  - name: education
    items:
      - institution: University of Technology
        url: https://uotech.edu
        area: Computer Science
        study_type: Bachelor
        start_date: 2011-09-01
        end_date: 2015-05-31
        score: 3.8 GPA
        courses:
          - Data Structures and Algorithms
          - Operating Systems
          - Database Management
  - name: awards
    items:
      - title: Employee of the Year
        date: 2022-12-01
        awarder: Tech Solutions Inc.
        summary: Recognized for outstanding contributions to the company's success.
  - name: publications
    items:
      - name: Building Scalable Microservices
        publisher: Tech Journal
        release_date: 2022-06-15
        summary: A comprehensive guide to building scalable microservices architecture.
        url: https://techjournal.com/building-scalable-microservices
  - name: skills
    items:
      - name: JavaScript
        level: Expert
        keywords:
          - React
          - Node.js
          - TypeScript
      - name: Cloud
        level: Advanced
        keywords:
          - AWS
          - Docker
          - Kubernetes
  - name: languages
    items:
      - language: English
        fluency: Native speaker
      - language: Spanish
        fluency: Intermediate
  - name: interests
    items:
      - name: Open Source
        keywords:
          - Contributor
          - Maintainer
      - name: Photography
        keywords:
          - Landscape
          - Portrait
  - name: references
    items:
      - name: John Doe
        reference: Jane was an outstanding employee who always delivered high-quality
          work.
        email: <EMAIL>
        phone: (*************
        position: Software Engineer
        company: Tech Solutions Inc.
  - name: projects
    items:
      - name: Resume Builder
        description: An open-source resume builder application
        highlights:
          - Built with React and TypeScript
          - Supports multiple export formats
        keywords:
          - React
          - TypeScript
          - Open Source
        start_date: 2022-01-01
        end_date: 2022-12-31
        url: https://github.com/username/resume-builder
        roles:
          - Lead Developer
          - UI/UX Designer
        entity: Personal Project
        type: application
$schema: null`;

    expect(result.trim()).toEqual(expectedYaml.trim());
  });
});
