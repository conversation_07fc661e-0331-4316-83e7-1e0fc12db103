import { Job, job } from '../entities/job';
import {
  YamlValidationResult,
  objectToYaml,
  parseYamlWithValidation,
} from './utils.yaml';

/**
 * Parse YAML string and validate it as a Job
 */
export function yamlToJob(yamlString: string): YamlValidationResult<Job> {
  return parseYamlWithValidation(yamlString, job) as YamlValidationResult<Job>;
}

/**
 * Convert a Job object to YAML string
 */
export function jobToYaml(
  job: Job,
  options?: {
    indent?: number;
  }
): string {
  return objectToYaml(job, 'Job', options);
}
