import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import type { Job } from '../entities/job';
import { DirectMatchAnalyzer } from './direct-match-analyzer';
import type { ConsolidatedSkill } from './types';

describe('DirectMatchAnalyzer', () => {
  let analyzer: DirectMatchAnalyzer;
  let fakeModel: FakeListChatModel;

  beforeEach(() => {
    fakeModel = new FakeListChatModel({
      responses: [],
    });

    analyzer = new DirectMatchAnalyzer(fakeModel);
  });

  describe('findDirectMatches', () => {
    it('should return empty array when no job skills provided', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'javascript',
          keywords: [],
          sourceResumes: [0],
        },
      ];

      const result = await analyzer.findDirectMatches(consolidatedSkills, null);
      expect(result).toEqual([]);
    });

    it('should return empty array when empty job skills array provided', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'javascript',
          keywords: [],
          sourceResumes: [0],
        },
      ];

      const result = await analyzer.findDirectMatches(consolidatedSkills, []);
      expect(result).toEqual([]);
    });

    it('should find exact matches (case-insensitive)', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'javascript',
          keywords: [],
          sourceResumes: [0],
        },
        {
          name: 'python',
          keywords: [],
          sourceResumes: [1],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'JavaScript', level: null, keywords: null },
        { name: 'React', level: null, keywords: null },
      ];

      const result = await analyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        jobSkill: 'JavaScript',
        resumeSkill: 'javascript',
        matchType: 'exact',
      });
    });

    it('should find keyword matches', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'web development',
          keywords: ['html', 'css', 'javascript'],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        {
          name: 'Frontend Development',
          level: null,
          keywords: ['HTML', 'CSS', 'React'],
        },
      ];

      const result = await analyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        jobSkill: 'Frontend Development',
        resumeSkill: 'web development',
        matchType: 'keyword',
      });
    });

    it('should include source resume when option is enabled', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'javascript',
          keywords: [],
          sourceResumes: [2],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'JavaScript', level: null, keywords: null },
      ];

      const result = await analyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills,
        { includeSourceResume: true }
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        jobSkill: 'JavaScript',
        resumeSkill: 'javascript',
        matchType: 'exact',
        sourceResume: 2,
      });
    });

    it('should handle multiple keyword matches correctly', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'frontend',
          keywords: ['react', 'vue'],
          sourceResumes: [0],
        },
        {
          name: 'backend',
          keywords: ['node.js', 'express'],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: null },
        { name: 'Node.js', level: null, keywords: null },
      ];

      const result = await analyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toHaveLength(2);

      const reactMatch = result.find((m) => m.jobSkill === 'React');
      expect(reactMatch).toEqual({
        jobSkill: 'React',
        resumeSkill: 'frontend',
        matchType: 'keyword',
      });

      const nodeMatch = result.find((m) => m.jobSkill === 'Node.js');
      expect(nodeMatch).toEqual({
        jobSkill: 'Node.js',
        resumeSkill: 'backend',
        matchType: 'keyword',
      });
    });

    it('should prioritize exact matches over keyword matches', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'react',
          keywords: [],
          sourceResumes: [0],
        },
        {
          name: 'frontend',
          keywords: ['react'],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: null },
      ];

      const result = await analyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        jobSkill: 'React',
        resumeSkill: 'react',
        matchType: 'exact',
      });
    });

    it('should handle AI synonym detection gracefully when it fails', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'vue',
          keywords: [],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'Vue.js', level: null, keywords: null },
      ];

      // Create a fake model that throws an error
      const failingModel = new FakeListChatModel({
        responses: [],
      });

      const failingAnalyzer = new DirectMatchAnalyzer(failingModel);

      // Should not throw, should handle gracefully
      const result = await failingAnalyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      // Should return empty since no exact or keyword matches
      expect(result).toEqual([]);
    });
  });

  describe('AI synonym detection', () => {
    it('should process synonym matches when AI is available', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'vue',
          keywords: [],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'Vue.js', level: null, keywords: null },
      ];

      // Configure fake model to return synonym match
      const synonymModel = new FakeListChatModel({
        responses: [
          JSON.stringify({
            matches: [
              {
                resumeSkill: 'vue',
                jobSkill: 'Vue.js',
                isSynonym: true,
                reasoning:
                  'Vue and Vue.js refer to the same JavaScript framework',
              },
            ],
          }),
        ],
      });

      const synonymAnalyzer = new DirectMatchAnalyzer(synonymModel);

      const result = await synonymAnalyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        jobSkill: 'Vue.js',
        resumeSkill: 'vue',
        matchType: 'synonym',
      });
    });

    it('should not create matches when AI determines skills are not synonymous', async () => {
      const consolidatedSkills: ConsolidatedSkill[] = [
        {
          name: 'react',
          keywords: [],
          sourceResumes: [0],
        },
      ];

      const jobSkills: Job['skills'] = [
        { name: 'Angular', level: null, keywords: null },
      ];

      // Configure fake model to return no synonym match
      const noSynonymModel = new FakeListChatModel({
        responses: [
          JSON.stringify({
            matches: [
              {
                resumeSkill: 'react',
                jobSkill: 'Angular',
                isSynonym: false,
                reasoning:
                  'React and Angular are different JavaScript frameworks',
              },
            ],
          }),
        ],
      });

      const noSynonymAnalyzer = new DirectMatchAnalyzer(noSynonymModel);

      const result = await noSynonymAnalyzer.findDirectMatches(
        consolidatedSkills,
        jobSkills
      );

      expect(result).toEqual([]);
    });
  });
});
