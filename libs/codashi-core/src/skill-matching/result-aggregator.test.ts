import { describe, expect, it } from 'vitest';

import type { Job } from '../entities/job';
import { ResultAggregator } from './result-aggregator';
import type {
  DirectSkillMatch,
  SkillMatchOptions,
  TransferableSkillMatch,
} from './types';

describe('ResultAggregator', () => {
  const aggregator = new ResultAggregator();

  const mockJobSkills: NonNullable<Job['skills']> = [
    { name: 'JavaScript', level: 'Advanced', keywords: ['JS', 'ECMAScript'] },
    { name: 'React', level: 'Intermediate', keywords: ['ReactJS', 'JSX'] },
    { name: 'Python', level: 'Beginner', keywords: ['Django', 'Flask'] },
    {
      name: 'Docker',
      level: null,
      keywords: ['Containers', 'Containerization'],
    },
    { name: 'AWS', level: 'Intermediate', keywords: ['EC2', 'S3', 'Lambda'] },
  ];

  const mockDirectMatches: DirectSkillMatch[] = [
    {
      jobSkill: 'JavaScript',
      resumeSkill: 'JavaScript',
      matchType: 'exact',
      sourceResume: 0,
    },
    {
      jobSkill: 'React',
      resumeSkill: 'ReactJS',
      matchType: 'synonym',
      sourceResume: 1,
    },
  ];

  const mockTransferableSkills: TransferableSkillMatch[] = [
    {
      jobSkill: 'Python',
      resumeSkill: 'Java',
      confidenceRating: 2,
      reasoning: 'Both are object-oriented programming languages',
      sourceResume: 0,
    },
    {
      jobSkill: 'Docker',
      resumeSkill: 'Kubernetes',
      confidenceRating: 3,
      reasoning: 'Both are container orchestration technologies',
      sourceResume: 1,
    },
    {
      jobSkill: 'AWS',
      resumeSkill: 'Azure',
      confidenceRating: 1,
      reasoning: 'Both are cloud platforms but different providers',
      sourceResume: 0,
    },
  ];

  describe('aggregate', () => {
    it('should create comprehensive analysis with all components', () => {
      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        mockJobSkills
      );

      expect(result).toEqual({
        directMatches: [
          {
            jobSkill: 'JavaScript',
            resumeSkill: 'JavaScript',
            matchType: 'exact',
          },
          {
            jobSkill: 'React',
            resumeSkill: 'ReactJS',
            matchType: 'synonym',
          },
        ],
        transferableSkills: [
          {
            jobSkill: 'Python',
            resumeSkill: 'Java',
            confidenceRating: 2,
            reasoning: 'Both are object-oriented programming languages',
          },
          {
            jobSkill: 'Docker',
            resumeSkill: 'Kubernetes',
            confidenceRating: 3,
            reasoning: 'Both are container orchestration technologies',
          },
          {
            jobSkill: 'AWS',
            resumeSkill: 'Azure',
            confidenceRating: 1,
            reasoning: 'Both are cloud platforms but different providers',
          },
        ],
        missingSkills: [], // All skills are covered
        summary: {
          totalJobSkills: 5,
          directMatchCount: 2,
          transferableMatchCount: 3,
          missingSkillCount: 0,
          coveragePercentage: 100,
        },
      });
    });

    it('should calculate missing skills correctly', () => {
      const partialDirectMatches: DirectSkillMatch[] = [
        {
          jobSkill: 'JavaScript',
          resumeSkill: 'JavaScript',
          matchType: 'exact',
        },
      ];

      const partialTransferableSkills: TransferableSkillMatch[] = [
        {
          jobSkill: 'Python',
          resumeSkill: 'Java',
          confidenceRating: 2,
          reasoning: 'Both are programming languages',
        },
      ];

      const result = aggregator.aggregate(
        partialDirectMatches,
        partialTransferableSkills,
        mockJobSkills
      );

      expect(result.missingSkills).toEqual([
        {
          name: 'React',
          level: 'Intermediate',
          keywords: ['ReactJS', 'JSX'],
        },
        {
          name: 'Docker',
          level: null,
          keywords: ['Containers', 'Containerization'],
        },
        {
          name: 'AWS',
          level: 'Intermediate',
          keywords: ['EC2', 'S3', 'Lambda'],
        },
      ]);

      expect(result.summary).toEqual({
        totalJobSkills: 5,
        directMatchCount: 1,
        transferableMatchCount: 1,
        missingSkillCount: 3,
        coveragePercentage: 40, // 2 out of 5 skills covered
      });
    });

    it('should filter transferable skills by confidence threshold', () => {
      const options: SkillMatchOptions = {
        confidenceThreshold: 2,
      };

      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        mockJobSkills,
        options
      );

      // Should only include transferable skills with confidence >= 2
      expect(result.transferableSkills).toHaveLength(2);
      expect(result.transferableSkills).toEqual([
        {
          jobSkill: 'Python',
          resumeSkill: 'Java',
          confidenceRating: 2,
          reasoning: 'Both are object-oriented programming languages',
        },
        {
          jobSkill: 'Docker',
          resumeSkill: 'Kubernetes',
          confidenceRating: 3,
          reasoning: 'Both are container orchestration technologies',
        },
      ]);

      // AWS should now be missing since its transferable match was filtered out
      expect(result.missingSkills).toEqual([
        {
          name: 'AWS',
          level: 'Intermediate',
          keywords: ['EC2', 'S3', 'Lambda'],
        },
      ]);

      expect(result.summary).toEqual({
        totalJobSkills: 5,
        directMatchCount: 2,
        transferableMatchCount: 2,
        missingSkillCount: 1,
        coveragePercentage: 80, // 4 out of 5 skills covered
      });
    });

    it('should include source resume information when requested', () => {
      const options: SkillMatchOptions = {
        includeSourceResume: true,
      };

      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        mockJobSkills,
        options
      );

      // Should include sourceResume in direct matches
      expect(result.directMatches[0]).toHaveProperty('sourceResume', 0);
      expect(result.directMatches[1]).toHaveProperty('sourceResume', 1);

      // Should include sourceResume in transferable skills
      expect(result.transferableSkills[0]).toHaveProperty('sourceResume', 0);
      expect(result.transferableSkills[1]).toHaveProperty('sourceResume', 1);
      expect(result.transferableSkills[2]).toHaveProperty('sourceResume', 0);
    });

    it('should exclude source resume information by default', () => {
      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        mockJobSkills
      );

      // Should not include sourceResume in direct matches
      expect(result.directMatches[0]).not.toHaveProperty('sourceResume');
      expect(result.directMatches[1]).not.toHaveProperty('sourceResume');

      // Should not include sourceResume in transferable skills
      expect(result.transferableSkills[0]).not.toHaveProperty('sourceResume');
      expect(result.transferableSkills[1]).not.toHaveProperty('sourceResume');
      expect(result.transferableSkills[2]).not.toHaveProperty('sourceResume');
    });

    it('should handle empty job skills gracefully', () => {
      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        []
      );

      expect(result).toEqual({
        directMatches: [],
        transferableSkills: [],
        missingSkills: [],
        summary: {
          totalJobSkills: 0,
          directMatchCount: 0,
          transferableMatchCount: 0,
          missingSkillCount: 0,
          coveragePercentage: 0,
        },
      });
    });

    it('should handle null job skills gracefully', () => {
      const result = aggregator.aggregate(
        mockDirectMatches,
        mockTransferableSkills,
        null
      );

      expect(result).toEqual({
        directMatches: [],
        transferableSkills: [],
        missingSkills: [],
        summary: {
          totalJobSkills: 0,
          directMatchCount: 0,
          transferableMatchCount: 0,
          missingSkillCount: 0,
          coveragePercentage: 0,
        },
      });
    });

    it('should handle empty matches gracefully', () => {
      const result = aggregator.aggregate([], [], mockJobSkills);

      expect(result.directMatches).toEqual([]);
      expect(result.transferableSkills).toEqual([]);
      expect(result.missingSkills).toHaveLength(5); // All job skills are missing
      expect(result.summary).toEqual({
        totalJobSkills: 5,
        directMatchCount: 0,
        transferableMatchCount: 0,
        missingSkillCount: 5,
        coveragePercentage: 0,
      });
    });

    it('should calculate coverage percentage correctly for partial matches', () => {
      const singleDirectMatch: DirectSkillMatch[] = [
        {
          jobSkill: 'JavaScript',
          resumeSkill: 'JavaScript',
          matchType: 'exact',
        },
      ];

      const singleTransferableSkill: TransferableSkillMatch[] = [
        {
          jobSkill: 'Python',
          resumeSkill: 'Java',
          confidenceRating: 2,
          reasoning: 'Both are programming languages',
        },
      ];

      const result = aggregator.aggregate(
        singleDirectMatch,
        singleTransferableSkill,
        mockJobSkills
      );

      // 2 out of 5 skills covered = 40%
      expect(result.summary.coveragePercentage).toBe(40);
    });

    it('should handle case-insensitive skill matching for missing skills calculation', () => {
      const caseVariantMatches: DirectSkillMatch[] = [
        {
          jobSkill: 'javascript', // lowercase in match
          resumeSkill: 'JavaScript',
          matchType: 'exact',
        },
      ];

      const jobSkillsWithCase: NonNullable<Job['skills']> = [
        { name: 'JavaScript', level: 'Advanced', keywords: null }, // uppercase in job
      ];

      const result = aggregator.aggregate(
        caseVariantMatches,
        [],
        jobSkillsWithCase
      );

      // Should recognize the match despite case difference
      expect(result.missingSkills).toHaveLength(0);
      expect(result.summary.coveragePercentage).toBe(100);
    });
  });
});
