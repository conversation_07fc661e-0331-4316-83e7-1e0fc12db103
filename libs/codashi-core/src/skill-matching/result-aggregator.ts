import type { Job } from '../entities/job';
import type {
  DirectSkillMatch,
  MissingSkill,
  SkillMatchAnalysis,
  SkillMatchOptions,
  TransferableSkillMatch,
} from './types';

/**
 * Aggregates and consolidates skill matching results into a comprehensive analysis.
 *
 * This class takes direct matches and transferable skill matches and creates
 * summary statistics, identifies missing skills, and formats the final result
 * according to the specified options.
 */
export class ResultAggregator {
  /**
   * Aggregates skill matching results into a comprehensive analysis.
   *
   * @param directMatches - Array of direct skill matches found
   * @param transferableSkills - Array of transferable skill matches with confidence ratings
   * @param jobSkills - Original job skills for calculating missing skills and coverage
   * @param options - Configuration options for result formatting
   * @returns Comprehensive skill match analysis with summary statistics
   */
  aggregate(
    directMatches: DirectSkillMatch[],
    transferableSkills: TransferableSkillMatch[],
    jobSkills: Job['skills'],
    options: SkillMatchOptions = {}
  ): SkillMatchAnalysis {
    // Handle case where job has no skills
    if (!jobSkills || jobSkills.length === 0) {
      return this.createEmptyAnalysis();
    }

    // Filter transferable skills by confidence threshold if specified
    const filteredTransferableSkills = this.filterByConfidenceThreshold(
      transferableSkills,
      options.confidenceThreshold
    );

    // Calculate missing skills
    const missingSkills = this.calculateMissingSkills(
      directMatches,
      filteredTransferableSkills,
      jobSkills
    );

    // Calculate summary statistics
    const summary = this.calculateSummaryStatistics(
      directMatches,
      filteredTransferableSkills,
      missingSkills,
      jobSkills
    );

    // Apply source resume information if requested
    const finalDirectMatches = this.applySourceResumeOption(
      directMatches,
      options.includeSourceResume
    );

    const finalTransferableSkills = this.applySourceResumeOption(
      filteredTransferableSkills,
      options.includeSourceResume
    );

    return {
      directMatches: finalDirectMatches,
      transferableSkills: finalTransferableSkills,
      missingSkills,
      summary,
    };
  }

  /**
   * Creates an empty analysis for cases where there are no job skills.
   *
   * @returns Empty skill match analysis
   */
  private createEmptyAnalysis(): SkillMatchAnalysis {
    return {
      directMatches: [],
      transferableSkills: [],
      missingSkills: [],
      summary: {
        totalJobSkills: 0,
        directMatchCount: 0,
        transferableMatchCount: 0,
        missingSkillCount: 0,
        coveragePercentage: 0,
      },
    };
  }

  /**
   * Filters transferable skills by minimum confidence threshold.
   *
   * @param transferableSkills - Array of transferable skill matches
   * @param confidenceThreshold - Minimum confidence rating to include (1-3)
   * @returns Filtered array of transferable skills
   */
  private filterByConfidenceThreshold(
    transferableSkills: TransferableSkillMatch[],
    confidenceThreshold?: 1 | 2 | 3
  ): TransferableSkillMatch[] {
    if (!confidenceThreshold) {
      return transferableSkills;
    }

    return transferableSkills.filter(
      (skill) => skill.confidenceRating >= confidenceThreshold
    );
  }

  /**
   * Calculates which job skills are missing (not covered by direct or transferable matches).
   *
   * @param directMatches - Array of direct skill matches
   * @param transferableSkills - Array of transferable skill matches
   * @param jobSkills - Original job skills
   * @returns Array of missing skills
   */
  private calculateMissingSkills(
    directMatches: DirectSkillMatch[],
    transferableSkills: TransferableSkillMatch[],
    jobSkills: NonNullable<Job['skills']>
  ): MissingSkill[] {
    // Create sets of matched job skills for efficient lookup
    const directlyMatchedSkills = new Set(
      directMatches.map((match) => match.jobSkill.toLowerCase().trim())
    );

    const transferablyMatchedSkills = new Set(
      transferableSkills.map((match) => match.jobSkill.toLowerCase().trim())
    );

    // Find job skills that are not covered by either direct or transferable matches
    const missingSkills: MissingSkill[] = [];

    for (const jobSkill of jobSkills) {
      const skillNameLower = jobSkill.name.toLowerCase().trim();

      const isDirectlyMatched = directlyMatchedSkills.has(skillNameLower);
      const isTransferablyMatched =
        transferablyMatchedSkills.has(skillNameLower);

      if (!isDirectlyMatched && !isTransferablyMatched) {
        missingSkills.push({
          name: jobSkill.name,
          level: jobSkill.level,
          keywords: jobSkill.keywords,
          // Could add category classification in future for learning recommendations
        });
      }
    }

    return missingSkills;
  }

  /**
   * Calculates comprehensive summary statistics for the skill match analysis.
   *
   * @param directMatches - Array of direct skill matches
   * @param transferableSkills - Array of transferable skill matches
   * @param missingSkills - Array of missing skills
   * @param jobSkills - Original job skills
   * @returns Summary statistics object
   */
  private calculateSummaryStatistics(
    directMatches: DirectSkillMatch[],
    transferableSkills: TransferableSkillMatch[],
    missingSkills: MissingSkill[],
    jobSkills: NonNullable<Job['skills']>
  ): SkillMatchAnalysis['summary'] {
    const totalJobSkills = jobSkills.length;
    const directMatchCount = directMatches.length;
    const transferableMatchCount = transferableSkills.length;
    const missingSkillCount = missingSkills.length;

    // Calculate coverage percentage based on skills that have some form of match
    // (either direct or transferable)
    const coveredSkillCount = directMatchCount + transferableMatchCount;
    const coveragePercentage =
      totalJobSkills > 0
        ? Math.round((coveredSkillCount / totalJobSkills) * 100)
        : 0;

    return {
      totalJobSkills,
      directMatchCount,
      transferableMatchCount,
      missingSkillCount,
      coveragePercentage,
    };
  }

  /**
   * Applies source resume information option to skill matches.
   *
   * If includeSourceResume is false, removes the sourceResume property from matches.
   * If true, keeps the sourceResume information for introspection.
   *
   * @param matches - Array of skill matches (direct or transferable)
   * @param includeSourceResume - Whether to include source resume information
   * @returns Array of matches with or without source resume information
   */
  private applySourceResumeOption<T extends { sourceResume?: number }>(
    matches: T[],
    includeSourceResume?: boolean
  ): T[] {
    if (includeSourceResume) {
      // Keep source resume information as-is
      return matches;
    }

    // Remove source resume information from each match
    return matches.map((match) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { sourceResume: _, ...matchWithoutSource } = match;
      return matchWithoutSource as T;
    });
  }
}
