import * as fs from 'fs';
import * as path from 'path';
import { describe, expect, it } from 'vitest';
import { extractTextFromPDF } from './text';

describe('PDF Text Extraction', () => {
  it('should extract simple text from a PDF file', async () => {
    const pdfPath = path.resolve(__dirname, './files/sample.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);

    const arrayBuffer = pdfBuffer.buffer.slice(
      pdfBuffer.byteOffset,
      pdfBuffer.byteOffset + pdfBuffer.byteLength
    );

    const samplePdf = new File([arrayBuffer], 'sample.pdf', {
      type: 'application/pdf',
    });

    const result = await extractTextFromPDF(samplePdf);

    expect(result.content).toBe('Dummy PDF file');
    expect(result.fileName).toBe('sample.pdf');
    expect(result.pageCount).toBeGreaterThan(0);
  });

  it('should extract text from text-based pdf resume', async () => {
    const pdfPath = path.resolve(__dirname, './files/resume.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);

    const arrayBuffer = pdfBuffer.buffer.slice(
      pdfBuffer.byteOffset,
      pdfBuffer.byteOffset + pdfBuffer.byteLength
    );

    const samplePdf = new File([arrayBuffer], 'resume.pdf', {
      type: 'application/pdf',
    });

    const result = await extractTextFromPDF(samplePdf);

    expect(result.content.length).greaterThan(4000);
    // phrases at the start, middle, and end of the resume
    expect(result.content).toContain('Zdravko Kirilov');
    expect(result.content).toContain('Software Engineer');
    expect(result.content).toContain('Front end developer');
    expect(result.content).toContain(
      "I've been mentoring aspiring young developers as a part time job"
    );
    expect(result.fileName).toBe('resume.pdf');
    expect(result.pageCount).toBeGreaterThan(0);
  });

  it('returns an empty string for an image-based resume', async () => {
    const pdfPath = path.resolve(__dirname, './files/resume-image.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);

    const arrayBuffer = pdfBuffer.buffer.slice(
      pdfBuffer.byteOffset,
      pdfBuffer.byteOffset + pdfBuffer.byteLength
    );

    const samplePdf = new File([arrayBuffer], 'resume.pdf', {
      type: 'application/pdf',
    });

    const result = await extractTextFromPDF(samplePdf);

    expect(result.content).toEqual(' ');
    expect(result.fileName).toBe('resume.pdf');
    expect(result.pageCount).toBeGreaterThan(0);
  });
});
