import { CustomError } from '@awe/core';

/**
 * Attempts to extract text from a PDF file using extractTextFromPDF.
 * If extraction fails or returns empty, falls back to OCR extraction.
 *
 * OCR scale could be adjusted to lower resolution on mobile devices for performance reasons.
 */
export type ExtractTextResult = {
  content: string;
  fileName: string;
  pageCount: number;
};

export async function extractTextWithFallback(
  file: File,
  ocrScale = 3
): Promise<ExtractTextResult> {
  try {
    const result = await extractTextFromPDF(file);
    if (result.content && result.content.trim().length > 0) {
      return result;
    }
    // If text is empty, fallback to OCR
  } catch (error) {
    // If extraction fails, fallback to OCR
  }
  // Fallback to OCR extraction
  return extractTextFromPDFWithOCR(file, ocrScale);

  // todo: 3rd fallback to some api
}

export async function extractTextFromPDF(
  file: File
): Promise<ExtractTextResult> {
  try {
    const { extractText, getDocumentProxy } = await import('unpdf');

    const buffer = await file.arrayBuffer();
    const pdf = await getDocumentProxy(new Uint8Array(buffer));
    const { text } = await extractText(pdf, { mergePages: true });

    return {
      content: text,
      fileName: file.name,
      pageCount: pdf.numPages,
    };
  } catch (error) {
    throw CustomError.toError(error).addContext({
      service: 'extractTextFromPDF',
      fileName: file.name,
    });
  }
}

export async function extractTextFromPDFWithOCR(
  file: File,
  scale = 3
): Promise<ExtractTextResult> {
  try {
    const { getDocumentProxy, renderPageAsImage } = await import('unpdf');
    const { createWorker } = await import('tesseract.js');

    const buffer = await file.arrayBuffer();
    const pdf = await getDocumentProxy(new Uint8Array(buffer));

    const images = await Promise.all(
      Array.from({ length: pdf.numPages }, async (_, i) => {
        const imageBuffer = await renderPageAsImage(pdf, i + 1, {
          scale,
        });
        // Convert ArrayBuffer to Blob and then to ObjectURL for Tesseract.js
        const blob = new Blob([imageBuffer], { type: 'image/png' });
        return URL.createObjectURL(blob);
      })
    );

    const worker = await createWorker();

    let ocrText = '';

    for (const imageUrl of images) {
      const { data } = await worker.recognize(imageUrl);
      ocrText += data.text + '\n';
      URL.revokeObjectURL(imageUrl);
    }

    await worker.terminate();

    return {
      content: ocrText.trim(),
      fileName: file.name,
      pageCount: pdf.numPages,
    };
  } catch (error) {
    throw CustomError.toError(error).addContext({
      service: 'extractTextFromPDFWithOCR',
      fileName: file.name,
    });
  }
}
