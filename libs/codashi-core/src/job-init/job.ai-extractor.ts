import { BaseChatModel } from '@langchain/core/language_models/chat_models.js';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';

import { type JobDraft, jobDraft, toDraft } from './job.draft.js';

export const extractJobStream = async function* (
  jobText: string,
  model: BaseChatModel,
  batchSize = 10
): AsyncGenerator<JobExtractionStreamEvent> {
  // 1. Identify sections
  const sectionIdentifier = createJobSectionIdentifier(model);
  const { sections } = await sectionIdentifier.invoke({ jobText });

  yield { type: 'sections_identified', sections };

  // 2. Group sections into batches
  const sectionBatches: JobSection[][] = [];
  for (let i = 0; i < sections.length; i += batchSize) {
    sectionBatches.push(sections.slice(i, i + batchSize));
  }

  // 3. Process each batch
  const fullJob: Partial<JobDraft> = {};

  for (const batch of sectionBatches) {
    const batchExtractor = createBatchSectionExtractor(batch, model);
    const batchInput = batch.reduce((acc, section) => {
      acc[section.name] = { content: section.content };
      return acc;
    }, {} as Record<string, { content: string }>);

    const batchResult = await batchExtractor.invoke(batchInput);
    Object.assign(fullJob, batchResult);

    // Yield results for each section in the batch
    for (const section of batch) {
      const sectionData = extractSectionData(batchResult, section.name);
      if (sectionData && Object.keys(sectionData).length > 0) {
        yield {
          type: 'extraction_result',
          section: section.name,
          data: sectionData,
        };
      }
    }
  }

  yield { type: 'final_result', job: toDraft(fullJob) };
};

const jobSectionSchema = z.object({
  name: z
    .enum([
      'basic_info',
      'company_info',
      'job_details',
      'compensation',
      'location',
      'skills',
      'experience',
      'responsibilities',
      'qualifications',
      'benefits',
    ])
    .describe(
      'The type of job information contained in this section. ' +
        'basic_info: Job title, company name, and job type. ' +
        'company_info: Information about the company including size and type. ' +
        'job_details: Detailed description of the job and its requirements. ' +
        'compensation: Salary and other financial benefits. ' +
        'location: Physical work location details including address and region. ' +
        'skills: Technical and professional skills required for the job ' +
        'experience: Required experience level and type. ' +
        'responsibilities: Key duties and tasks of the position. ' +
        'qualifications: Required education, certifications, and other qualifications. ' +
        'benefits: Non-salary compensation and perks.'
    ),
  content: z
    .string()
    .describe(
      'The full text content of this job section that needs to be parsed.'
    ),
});

type JobSection = z.infer<typeof jobSectionSchema>;

const identifiedJobSectionsSchema = z.object({
  sections: z.array(jobSectionSchema),
});

const createJobSectionIdentifier = (model: BaseChatModel) => {
  const parser = StructuredOutputParser.fromZodSchema(
    identifiedJobSectionsSchema
  );

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Your task is to identify and segment the following job posting text into logical sections.

Possible sections are: ${jobSectionSchema.shape.name.options.join(', ')}.

Job Posting Text:
{jobText}

{format_instructions}

Make sure to identify distinct sections like responsibilities, qualifications, company info, etc.
Group related content together - don't split similar responsibilities into separate sections.

Respond ONLY with the structured JSON output.`
  );

  return RunnableSequence.from([
    {
      jobText: (input: { jobText: string }) => input.jobText,
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

const createBatchSectionExtractor = (
  sections: JobSection[],
  model: BaseChatModel
) => {
  const parser = StructuredOutputParser.fromZodSchema(jobDraft);

  const prompt = ChatPromptTemplate.fromTemplate(
    `You are an expert job posting parser. Extract structured information from the following job posting sections.

For responsibilities and qualifications, break them down into individual items/bullet points.

Sections to process:
${sections
  .map(
    (s) => `
**${s.name.toUpperCase()}:**
{${s.name}}`
  )
  .join('\n')}

{format_instructions}

Respond ONLY with the structured JSON output.`
  );

  const inputMapping = sections.reduce((acc, section) => {
    acc[section.name] = (input: Record<string, { content: string }>) =>
      input[section.name]?.content || '';
    return acc;
  }, {} as Record<string, (input: Record<string, { content: string }>) => string>);

  return RunnableSequence.from([
    {
      ...inputMapping,
      format_instructions: () => parser.getFormatInstructions(),
    },
    prompt,
    model,
    parser,
  ]);
};

const extractSectionData = (
  batchResult: Partial<JobDraft>,
  sectionName: string
): Partial<JobDraft> => {
  const sectionData: Partial<JobDraft> = {};

  // Extract only the fields that are relevant to this specific section
  switch (sectionName) {
    case 'basic_info':
      if (batchResult.title !== undefined)
        sectionData.title = batchResult.title;
      if (batchResult.company !== undefined)
        sectionData.company = batchResult.company;
      if (batchResult.type !== undefined) sectionData.type = batchResult.type;
      if (batchResult.date !== undefined) sectionData.date = batchResult.date;
      break;
    case 'company_info':
      if (batchResult.company !== undefined)
        sectionData.company = batchResult.company;
      if (batchResult.company_meta !== undefined)
        sectionData.company_meta = batchResult.company_meta;
      break;
    case 'job_details':
      if (batchResult.description !== undefined)
        sectionData.description = batchResult.description;
      if (batchResult.type !== undefined) sectionData.type = batchResult.type;
      break;
    case 'compensation':
      if (batchResult.salary !== undefined)
        sectionData.salary = batchResult.salary;
      break;
    case 'location':
      if (batchResult.location !== undefined)
        sectionData.location = batchResult.location;
      if (batchResult.remote !== undefined)
        sectionData.remote = batchResult.remote;
      break;
    case 'skills':
      if (batchResult.skills !== undefined)
        sectionData.skills = batchResult.skills;
      break;
    case 'experience':
      if (batchResult.experience !== undefined)
        sectionData.experience = batchResult.experience;
      break;
    case 'responsibilities':
      if (batchResult.responsibilities !== undefined)
        sectionData.responsibilities = batchResult.responsibilities;
      break;
    case 'qualifications':
      if (batchResult.qualifications !== undefined)
        sectionData.qualifications = batchResult.qualifications;
      break;
    case 'benefits':
      if (batchResult.benefits !== undefined)
        sectionData.benefits = batchResult.benefits;
      break;
  }

  return sectionData;
};

export type JobExtractionStreamEvent =
  | { type: 'sections_identified'; sections: JobSection[] }
  | { type: 'extraction_result'; section: string; data: Partial<JobDraft> }
  | { type: 'final_result'; job: JobDraft };
