import { type Job } from '../entities/job';
import { JobDraft } from './job.draft';

const CONFIDENCE_THRESHOLD = 0.65;

export const draftToJob = (draft: JobDraft): Job => {
  const bestLocationEntry =
    draft.location && draft.location.length > 0
      ? draft.location.reduce((a, b) =>
          a.ai_confidence > b.ai_confidence ? a : b
        )
      : undefined;

  const location =
    bestLocationEntry && bestLocationEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? {
          address: bestLocationEntry.value?.address ?? PLACEHOLDERS.address,
          postal_code:
            bestLocationEntry.value?.postal_code ?? PLACEHOLDERS.postalCode,
          city: bestLocationEntry.value?.city ?? PLACEHOLDERS.city,
          country_code:
            bestLocationEntry.value?.country_code ?? PLACEHOLDERS.countryCode,
          region: bestLocationEntry.value?.region ?? PLACEHOLDERS.region,
        }
      : null;

  // Skills: filter by confidence and map to job schema format
  const skills = draft.skills
    .filter((s) => s.ai_confidence >= CONFIDENCE_THRESHOLD)
    .flatMap((s) => s.value || [])
    .map((skill) => ({
      name: skill.name ?? PLACEHOLDERS.skillName,
      level: skill.level ?? PLACEHOLDERS.skillLevel,
      keywords: skill.keywords?.length ? skill.keywords : null,
    }));

  // Meta: extract best meta entry
  const bestMetaEntry =
    draft.meta && draft.meta.length > 0
      ? draft.meta.reduce((a, b) => (a.ai_confidence > b.ai_confidence ? a : b))
      : undefined;

  const meta =
    bestMetaEntry && bestMetaEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? {
          canonical: bestMetaEntry.value.canonical ?? PLACEHOLDERS.canonical,
          version: bestMetaEntry.value.version ?? PLACEHOLDERS.version,
          last_modified:
            bestMetaEntry.value.last_modified ?? PLACEHOLDERS.lastModified,
        }
      : null;

  // Company Meta: extract best company meta entry
  const bestCompanyMetaEntry =
    draft.company_meta && draft.company_meta.length > 0
      ? draft.company_meta.reduce((a, b) =>
          a.ai_confidence > b.ai_confidence ? a : b
        )
      : undefined;

  const company_meta =
    bestCompanyMetaEntry &&
    bestCompanyMetaEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? {
          type: bestCompanyMetaEntry.value.type ?? PLACEHOLDERS.companyType,
          size: bestCompanyMetaEntry.value.size ?? PLACEHOLDERS.companySize,
          tone: bestCompanyMetaEntry.value.tone ?? PLACEHOLDERS.companyTone,
        }
      : {
          type: PLACEHOLDERS.companyType,
          size: PLACEHOLDERS.companySize,
          tone: PLACEHOLDERS.companyTone,
        };

  // Notes: extract best notes entry
  const bestNotesEntry =
    draft.notes && draft.notes.length > 0
      ? draft.notes.reduce((a, b) =>
          a.ai_confidence > b.ai_confidence ? a : b
        )
      : undefined;

  const notes =
    bestNotesEntry && bestNotesEntry.ai_confidence >= CONFIDENCE_THRESHOLD
      ? bestNotesEntry.value
      : null;

  // Helper functions for type-safe value extraction
  const getStringValue = (
    arr:
      | { value: string; ai_confidence: number; human_feedback: unknown }[]
      | undefined,
    placeholder: string
  ): string => {
    return pickBestByConfidence(arr, placeholder) as string;
  };

  const getStringOrNullValue = (
    arr:
      | {
          value: string | null;
          ai_confidence: number;
          human_feedback: unknown;
        }[]
      | undefined,
    placeholder: string | null
  ): string | null => {
    return pickBestByConfidence(arr, placeholder) as string | null;
  };

  const getDateValue = (
    arr: { value: Date | null; ai_confidence: number }[] | undefined
  ) => {
    if (!arr || arr.length === 0) return null;

    const best = arr.reduce((a, b) =>
      a.ai_confidence > b.ai_confidence ? a : b
    );

    if (best.ai_confidence < CONFIDENCE_THRESHOLD) return null;

    return best.value instanceof Date ? best.value.toISOString() : null;
  };

  const getArrayValue = (
    arr:
      | {
          value: string[] | null;
          ai_confidence: number;
          human_feedback: unknown;
        }[]
      | undefined,
    placeholder: string[] | null
  ): string[] | null => {
    return pickBestByConfidence(arr, placeholder) as string[] | null;
  };

  const getRemoteValue = (
    arr:
      | { value: 'Full' | 'Hybrid' | 'None' | null; ai_confidence: number }[]
      | undefined,
    placeholder: 'Full' | 'Hybrid' | 'None' | null
  ): 'Full' | 'Hybrid' | 'None' | null => {
    return pickBestByConfidence(arr, placeholder) as
      | 'Full'
      | 'Hybrid'
      | 'None'
      | null;
  };

  return {
    title: getStringValue(draft.title, PLACEHOLDERS.title),
    company: getStringValue(draft.company, PLACEHOLDERS.company),
    type: getStringValue(draft.type, PLACEHOLDERS.type),
    date: getDateValue(draft.date) as Job['date'], // Cast to handle branded type
    description: getStringValue(draft.description, PLACEHOLDERS.description),
    location,
    remote: getRemoteValue(draft.remote, PLACEHOLDERS.remote),
    salary: getStringOrNullValue(draft.salary, PLACEHOLDERS.salary),
    experience: (() => {
      const exp = getStringOrNullValue(
        draft.experience,
        PLACEHOLDERS.experience
      );
      return exp ? [exp] : null;
    })(),
    responsibilities: getArrayValue(
      draft.responsibilities,
      PLACEHOLDERS.responsibilities
    ),
    qualifications: getArrayValue(
      draft.qualifications,
      PLACEHOLDERS.qualifications
    ),
    skills: skills.length > 0 ? skills : null,
    meta,
    company_meta: {
      ...company_meta,
      internal_company_id: null,
    },
    notes,
    benefits: getArrayValue(draft.benefits, PLACEHOLDERS.benefits),
  };
};

const pickBestByConfidence = <
  T extends { value: V; ai_confidence: number },
  V,
  P extends V
>(
  arr: T[] | undefined,
  placeholder: P,
  minConfidence = CONFIDENCE_THRESHOLD
): V => {
  if (!arr || arr.length === 0) return placeholder;

  const best = arr.reduce((a, b) =>
    a.ai_confidence > b.ai_confidence ? a : b
  );

  return best.ai_confidence >= minConfidence ? best.value : placeholder;
};

// Placeholders for required and critical fields in the job schema
// Only include placeholders for required fields or fields that should have meaningful defaults
const PLACEHOLDERS = {
  // Required fields in job schema
  title: 'Job title not provided',
  company: 'Company name not provided',
  type: 'Job type not provided',
  description: 'Job description not provided',

  // Optional fields with meaningful defaults
  address: null as string | null,
  postalCode: null as string | null,
  city: null as string | null,
  countryCode: 'US' as string,
  region: null as string | null,
  remote: null as 'Full' | 'Hybrid' | 'None' | null,
  salary: null as string | null,
  experience: null as string | null,
  responsibilities: null as string[] | null,
  qualifications: null as string[] | null,
  skillName: 'Skill not provided',
  skillLevel: null as string | null,
  canonical: null as string | null,
  version: null as string | null,
  lastModified: null as string | null,
  companyType: null as string | null,
  companySize: null as string | null,
  companyTone: null as string | null,
  benefits: null as string[] | null,
};
