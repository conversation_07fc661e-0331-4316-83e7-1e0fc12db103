# Interactive Resume Parser Agent

## Overview

An AI-powered conversational agent that transforms unstructured resume text into structured Zod-validated data through an interactive chat interface. The agent can ask clarifying questions to resolve ambiguities and improve parsing accuracy.

## Problem Statement

Traditional single-shot resume parsing often fails to:

- Handle ambiguous or incomplete information
- Understand context-dependent details
- Extract nuanced information that requires clarification
- Adapt to different resume formats and styles
- Provide confidence levels for extracted data

## Solution Architecture

### Core Components

1. **LangGraph-based Agent**: Multi-step reasoning agent that can:

   - Parse initial resume content
   - Identify areas needing clarification
   - Generate contextual questions
   - Incorporate user responses into parsing logic
   - Iteratively improve extraction quality

2. **Structured Output Schema**: Comprehensive Zod schema covering:

   - Personal information (name, location, email, phone)
   - Professional summary
   - Years of experience calculation
   - Technical specializations
   - Skills categorization
   - Work experience with detailed context
   - Education background
   - Certifications and achievements

3. **Conversation Management**: Chat session handling with:
   - Message history tracking
   - Context preservation across interactions
   - Question prioritization logic
   - Confidence scoring for extracted fields

## Technical Implementation

### Technology Stack

- **LangChain.js**: Core agent framework
- **LangGraph**: Multi-agent workflow orchestration
- **Zod**: Schema validation and type safety
- **Mistral 21B**: LLM provider with structured output
- **TypeScript**: Type-safe implementation

### Agent Workflow

```mermaid
graph TD
    A[Resume Text Input] --> B[Initial Parse Attempt]
    B --> C{Confidence Check}
    C -->|High Confidence| D[Return Structured Data]
    C -->|Low Confidence| E[Generate Clarifying Questions]
    E --> F[Present Questions to User]
    F --> G[Collect User Responses]
    G --> H[Update Context & Re-parse]
    H --> C

    I[Question Prioritizer] --> E
    J[Confidence Scorer] --> C
    K[Context Manager] --> H
```

### Data Schema Strategy

The parser will target a **simplified intermediate schema** that aligns with your existing `extendedResume` structure but focuses on the essential fields that can be reliably extracted from raw text. This intermediate result can then be mapped to the full `Resume` type.

```typescript
// Simplified parsing schema aligned with existing Resume structure
export const resumeParsingSchema = z.object({
  // Maps to basics in Resume schema
  basics: z.object({
    name: z.string().describe('Full name as it appears on the resume'),
    label: z.string().optional().describe('Professional title or role'),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    url: z.string().url().optional().describe('Personal website or portfolio'),
    summary: z.string().optional().describe('Professional summary or bio'),
    location: z
      .object({
        city: z.string().optional(),
        region: z.string().optional(), // state/province
        country_code: z.string().optional(),
      })
      .optional(),
    profiles: z
      .array(
        z.object({
          network: z.string().describe('Platform name (LinkedIn, GitHub, etc.)'),
          username: z.string().optional(),
          url: z.string().url().optional(),
        })
      )
      .optional(),
  }),

  // Maps to work array in Resume schema
  work: z.array(
    z.object({
      name: z.string().describe('Company or organization name'),
      position: z.string().describe('Job title or role'),
      location: z.string().optional(),
      start_date: z.string().describe('Start date in ISO format (YYYY-MM-DD)'),
      end_date: z.string().optional().describe('End date in ISO format or null for current'),
      summary: z.string().optional().describe('Role description and responsibilities'),
      highlights: z.array(z.string()).optional().describe('Key accomplishments'),
      url: z.string().url().optional(),
    })
  ),

  // Maps to education array in Resume schema
  education: z
    .array(
      z.object({
        institution: z.string().describe('School or university name'),
        area: z.string().optional().describe('Field of study'),
        study_type: z.string().optional().describe("Degree type (Bachelor's, Master's, etc.)"),
        start_date: z.string().optional().describe('Start date in ISO format'),
        end_date: z.string().optional().describe('Graduation date in ISO format'),
        score: z.string().optional().describe('GPA or grade'),
        courses: z.array(z.string()).optional().describe('Relevant coursework'),
        url: z.string().url().optional(),
      })
    )
    .optional(),

  // Maps to skills array in Resume schema
  skills: z
    .array(
      z.object({
        name: z.string().describe('Skill category or primary skill name'),
        level: z.string().optional().describe('Proficiency level'),
        keywords: z.array(z.string()).optional().describe('Specific technologies, tools, or sub-skills'),
      })
    )
    .optional(),

  // Maps to certificates array in Resume schema
  certificates: z
    .array(
      z.object({
        name: z.string().describe('Certificate name'),
        issuer: z.string().optional().describe('Issuing organization'),
        date: z.string().optional().describe('Issue date in ISO format'),
        url: z.string().url().optional(),
      })
    )
    .optional(),

  // Maps to projects array in Resume schema
  projects: z
    .array(
      z.object({
        name: z.string().describe('Project name'),
        description: z.string().optional().describe('Project description'),
        highlights: z.array(z.string()).optional().describe('Key accomplishments'),
        keywords: z.array(z.string()).optional().describe('Technologies used'),
        start_date: z.string().optional().describe('Start date in ISO format'),
        end_date: z.string().optional().describe('End date in ISO format'),
        url: z.string().url().optional(),
        roles: z.array(z.string()).optional().describe('Roles held in the project'),
        entity: z.string().optional().describe('Associated organization'),
      })
    )
    .optional(),

  // Maps to languages array in Resume schema
  languages: z
    .array(
      z.object({
        language: z.string().describe('Language name'),
        fluency: z.string().optional().describe('Proficiency level'),
      })
    )
    .optional(),

  // Parsing metadata (not part of Resume schema)
  parsingMetadata: z.object({
    confidenceScore: z.number().min(0).max(1).describe('Overall parsing confidence'),
    fieldConfidence: z.record(z.number()).describe('Per-field confidence scores'),
    questionsAsked: z.array(z.string()).describe('Questions asked during parsing'),
    ambiguousFields: z.array(z.string()).describe('Fields that needed clarification'),
    parsingTimestamp: z.string().datetime(),
    extractedYearsOfExperience: z.number().optional().describe('Calculated years of experience'),
  }),
});

export type ResumeParsingResult = z.infer<typeof resumeParsingSchema>;

// Mapping function to convert parsing result to full Resume schema
export function mapParsingResultToResume(parsingResult: ResumeParsingResult): Partial<Resume> {
  return {
    basics: parsingResult.basics,
    work: parsingResult.work,
    education: parsingResult.education,
    skills: parsingResult.skills,
    certificates: parsingResult.certificates,
    projects: parsingResult.projects,
    languages: parsingResult.languages,
    // Additional Resume fields can be populated later or left optional
    volunteer: [],
    awards: [],
    publications: [],
    interests: [],
    references: [],
    meta: {
      last_modified: new Date().toISOString(),
      version: '1.0',
    },
  };
}
```

### Agent States and Transitions

1. **Initial Parse State**

   - Extract obvious information
   - Identify ambiguous sections
   - Calculate initial confidence scores

2. **Question Generation State**

   - Prioritize unclear fields
   - Generate contextual questions
   - Determine question order

3. **User Interaction State**

   - Present questions to user
   - Collect and validate responses
   - Update parsing context

4. **Re-parsing State**

   - Incorporate new information
   - Re-evaluate confidence scores
   - Determine if more questions needed

5. **Completion State**
   - Finalize structured output
   - Generate confidence report
   - Return parsed resume data

## Question Generation Strategy

### Strategic "Unlock" Questions

Having trouble parsing the first one-shot attempt? -
These high-impact questions provide context that dramatically improves parsing of the entire resume:

#### **1. Career Context (Ask First)**

- **"What's your primary technical specialization?"** (Frontend, Backend, Full-stack, DevOps, Data Science, etc.)

  - Unlocks: Skill categorization, technology context, role interpretation
  - Example: "Full-stack" → React/Node.js grouped as primary stack vs Data Scientist's Python focus

- **"How many total years of professional experience do you have?"**
  - Unlocks: Date validation, gap identification, role progression logic, seniority interpretation

#### **2. Industry/Domain Context**

- **"What industry do you primarily work in?"** (Fintech, Healthcare, E-commerce, Gaming, etc.)
  - Unlocks: Technology stack assumptions, compliance requirements, achievement significance
  - Example: Healthcare → "HIPAA compliance" becomes significant skill vs general knowledge

#### **3. Company Context**

- **"Do you typically work at startups, mid-size companies, or large enterprises?"**
  - Unlocks: Role scope interpretation, responsibility levels, team size context
  - Example: "Led team of 3" at startup vs enterprise has different implications

### Question Types by Priority

#### **High Priority: Strategic Context**

1. Technical specialization
2. Years of experience
3. Industry/domain
4. Company size preference

#### **Medium Priority: Specific Clarifications**

1. **Disambiguation**: "ABC Corp - is this ABC Corporation or ABC Consulting?"
2. **Technology Context**: "Python at TechCorp - web development, data science, or automation?"
3. **Date Clarification**: "Gap in 2020-2021 - pandemic, career transition, or education?"
4. **Role Specificity**: "'Engineer' at StartupXYZ - software, DevOps, or other specialty?"

#### **Low Priority: Validation & Details**

1. **Validation**: "I calculated 7 years experience - does this sound accurate?"
2. **Completeness**: "I extracted these skills: [list]. Any important ones missing?"
3. **Preferences**: "You mention React and Angular - which do you use more frequently?"

### Question Sequencing Strategy

1. **Start with 1-2 strategic unlock questions** (specialization + experience)
2. **Use context to inform parsing approach** for remaining content
3. **Ask targeted clarifications** only when context doesn't resolve ambiguity
4. **Validate final results** with user before completion

**Example Flow:**

```
AI: "What's your primary technical specialization?"
User: "Full-stack web development"
AI: [Parses with React/Node.js as primary, Python as secondary]

AI: "How many years of professional experience?"
User: "6 years"
AI: [Interprets leadership as mid-level, validates dates, expects deeper skills]

AI: [Only asks specific questions if context doesn't resolve ambiguities]
```

## Integration Points

### Existing Codebase Integration

- **Extend existing `AiProvider` interface** to support conversational parsing
- **Leverage current Mistral AI** for LLM capabilities
- **Build on existing resume entities** in `libs/codashi-core/src/entities/resume.ts`
- **Integrate with chat session management** from `apps/codarashi-hq/src/entities/shared.ts`
- **Use simplified schema that maps to your `extendedResume`** structure
- **Preserve compatibility with JSON Resume standard** while adding parsing-specific metadata

### API Design

```typescript
export interface InteractiveResumeParser {
  startParsing(resumeText: string): Promise<ParsingSession>;
  answerQuestion(sessionId: string, answer: string): Promise<ParsingSession>;
  getParsingResult(sessionId: string): Promise<ResumeParsingResult>;
  cancelParsing(sessionId: string): Promise<void>;
}

export interface ParsingSession {
  id: string;
  status: 'parsing' | 'waiting_for_answer' | 'completed' | 'error';
  currentQuestion?: string;
  partialResult: Partial<ResumeParsingResult>;
  confidenceScore: number;
  questionsRemaining: number;
}
```

## Implementation Plan

### Phase 1: Core Agent Framework

- [ ] Set up LangGraph agent structure
- [ ] Implement basic parsing workflow
- [ ] Create confidence scoring system
- [ ] Build question generation logic

### Phase 2: Schema and Validation

- [ ] Define comprehensive Zod schema
- [ ] Implement field-level confidence tracking
- [ ] Create validation and error handling
- [ ] Add parsing metadata collection

### Phase 3: Conversation Management

- [ ] Build chat session persistence
- [ ] Implement question prioritization
- [ ] Create user response validation
- [ ] Add conversation history tracking

### Phase 4: Integration and Testing

- [ ] Integrate with existing AI provider
- [ ] Add to codashi-core exports
- [ ] Create comprehensive test suite
- [ ] Performance optimization

### Phase 5: UI Integration

- [ ] Connect to chat interface
- [ ] Add progress indicators
- [ ] Implement real-time updates
- [ ] Create result visualization

## Success Metrics

- **Parsing Accuracy**: >95% for critical fields (name, email, experience)
- **User Satisfaction**: <3 questions needed on average
- **Completion Rate**: >90% of sessions reach completion
- **Performance**: <30 seconds total parsing time
- **Confidence**: >85% average confidence score for completed parses

## Future Enhancements

1. **Multi-language Support**: Parse resumes in different languages
2. **Industry-specific Parsing**: Specialized logic for different fields
3. **Batch Processing**: Handle multiple resumes simultaneously
4. **Learning System**: Improve based on user corrections
5. **Integration APIs**: Connect with ATS systems and job boards
