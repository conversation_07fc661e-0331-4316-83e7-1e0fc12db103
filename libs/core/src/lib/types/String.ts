import { z } from 'zod';

import { Tagged } from './Tagged';

enum Errors {
  Required = 'Required',
  NotAString = 'NotAString',
  TooShort = 'TooShort',
  TooLong = 'TooLong',
}

export type StringOfLength<
  Min extends number,
  Max extends number,
  Id extends string = ''
> = Tagged<
  'StringOfLength',
  {
    min: Min;
    max: Max;
    id: Id;
  } & string
>;

const trimString = (u: unknown) => (typeof u === 'string' ? u.trim() : u);

const parser = <
  Min extends number,
  Max extends number,
  Id extends string = string
>(
  min: Min,
  max: Max,
  _id = '' as Id
) =>
  z
    .preprocess(
      trimString,
      z
        .string({
          required_error: Errors.Required,
          invalid_type_error: Errors.NotAString,
        })
        .min(min, { message: Errors.TooShort })
        .max(max, { message: Errors.TooLong })
    )
    .transform((result) => result as StringOfLength<Min, Max, typeof _id>);

const generate = <
  Min extends number,
  <PERSON> extends number,
  Id extends string = string
>(
  source: string,
  min: Min,
  max: Max,
  id = '' as Id
) => {
  return parser(min, max, id).parse(source);
};

export const StringOfLength = {
  parser,
  generate,
  Errors,
};
