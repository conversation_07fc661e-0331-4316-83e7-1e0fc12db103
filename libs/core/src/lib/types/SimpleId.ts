import { z } from 'zod';
import { sample } from '../misc';

export type SimpleId = string & { readonly __SimpleId: true };

const alphabet = 'abcdefghijklmnopqrstwxyz0123456789';
const length = 8;

const parser = z
  .string()
  .length(length)
  .refine((res) => res.split('').every((char) => alphabet.includes(char)))
  .transform((res) => res as SimpleId);

const generate = <T extends SimpleId>() =>
  new Array(length).fill(1).reduce((total) => {
    total += sample(alphabet.split(''));
    return total;
  }, '') as T;

export const SimpleId = { parser, generate };
