import { describe, expect, it } from 'vitest';

import { isoDateParser } from './IsoDate';

describe('isoDateParser', () => {
  // Valid inputs
  it('should accept valid Date objects', () => {
    expect(isoDateParser.safeParse(new Date(2023, 0, 15)).success).toBe(true);
    expect(isoDateParser.safeParse(new Date(2024, 1, 29)).success).toBe(true); // Leap year
    expect(
      isoDateParser.safeParse(new Date(Date.UTC(1995, 11, 5))).success
    ).toBe(true);
  });

  it('should accept valid date strings in different formats', () => {
    expect(isoDateParser.safeParse('2023').success).toBe(true);
    expect(isoDateParser.safeParse('2024-01').success).toBe(true);
    expect(isoDateParser.safeParse('2023-12-31').success).toBe(true);
    expect(isoDateParser.safeParse('2024-02-29').success).toBe(true); // Valid leap year
  });

  it('should convert Date objects to YYYY-MM-DD format', () => {
    // Use Date.UTC to avoid timezone issues
    const result = isoDateParser.safeParse(new Date(Date.UTC(2023, 0, 15)));
    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data).toBe('2023-01-15');
    }
  });

  // Invalid inputs
  it('should reject invalid Date objects', () => {
    expect(isoDateParser.safeParse(new Date('invalid date')).success).toBe(
      false
    );
  });

  it('should reject Date objects with years outside allowed range', () => {
    // Use Date.UTC for consistent behavior
    expect(isoDateParser.safeParse(new Date(Date.UTC(500, 0, 1))).success).toBe(
      false
    );
    expect(
      isoDateParser.safeParse(new Date(Date.UTC(3000, 0, 1))).success
    ).toBe(false);
  });

  it('should reject invalid date strings', () => {
    expect(isoDateParser.safeParse('3000-01-01').success).toBe(false); // Year out of range
    expect(isoDateParser.safeParse('2023-13-01').success).toBe(false); // Invalid month
    expect(isoDateParser.safeParse('invalid-string').success).toBe(false);
  });

  it('should reject non-date inputs', () => {
    expect(isoDateParser.safeParse(12345).success).toBe(false);
    expect(isoDateParser.safeParse(null).success).toBe(false);
    expect(isoDateParser.safeParse(undefined).success).toBe(false);
  });
});
