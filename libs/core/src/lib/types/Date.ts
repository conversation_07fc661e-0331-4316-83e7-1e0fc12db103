import { z } from 'zod';

import { zodToDomain } from '../misc';
import { CustomError, ParsingError } from './Error';
import * as Numbers from './Number';
import { Tagged } from './Tagged';

export type DateOnly = Tagged<'DateOnly', Date>;

export type DateTime = Tagged<'DateTime', Date>;

const isValid = (date: Date) => {
  return date instanceof Date && !isNaN(date.getTime());
};

const isValidDate = (input: unknown): input is Date => {
  if (input instanceof Date) {
    return isValid(input);
  }
  if (typeof input === 'string') {
    return isValid(new Date(input));
  }
  if (typeof input === 'number') {
    return isValid(new Date(input));
  }
  return false;
};

export enum Errors {
  Required = 'Required',
  InvalidDate = 'InvalidDate',
}

export const dateParser = z
  .unknown({ required_error: Errors.Required })
  .refine(isValidDate, { message: Errors.InvalidDate })
  .transform((date) => new Date(new Date(date).toDateString()) as DateOnly);

export const dateTimeParser = z
  .unknown({ required_error: Errors.Required })
  .refine(isValidDate, { message: Errors.InvalidDate })
  .transform((date) => new Date(date) as DateTime);

export const toValidOrEmpty = (date: unknown): DateOnly | undefined => {
  const parsed = dateParser.safeParse(date);

  return parsed.success ? parsed.data : undefined;
};

const toValidDate = (date: string | Date | null | undefined | unknown) => {
  const parsed = dateParser.safeParse(date);

  if (parsed.success) {
    return parsed.data;
  }

  return new ParsingError(parsed.error);
};

export const isPast = (
  primaryDate: DateTime | DateOnly,
  toBeComparedTo: DateTime | DateOnly
) => {
  return primaryDate.getTime() < toBeComparedTo.getTime();
};

export const isFuture = (...params: Parameters<typeof isPast>) =>
  !isPast(...params);

export const nowDate = () => {
  return new Date(new Date().toDateString()) as DateTime;
};

export const nowDateTime = () => {
  return new Date() as DateTime;
};

export const toDisplayDate = (date: unknown) => {
  const asDate = toValidDate(date);

  if (CustomError.isErrorInstance(asDate)) {
    return '';
  }

  return asDate.toLocaleDateString();
};

export type Minutes = Tagged<'Dates/Minutes', number>;

export const minutesParser = z
  .number()
  .positive()
  .int()
  .transform((minutes) => minutes as Minutes);

export const toMinutes = (source: unknown) =>
  zodToDomain(z.object({ minutes: minutesParser }), source);

export type Timestamp = Numbers.PositiveInteger & { __timestamp: true };

export const timestampParser = Numbers.positiveIntegerParser.transform(
  (data) => data as Timestamp
);

export const toTimestamp = (source: Date): Timestamp =>
  source.getTime() as Timestamp;

export const addMinutes = <T extends DateTime>(
  date: T,
  minutes: Minutes
): T => {
  return new Date(date.getTime() + minutes * 60000) as T;
};

export const nowTimestamp = () => {
  return new Date().getTime() as Timestamp;
};
