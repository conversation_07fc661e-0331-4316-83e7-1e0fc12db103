import { z } from 'zod';

import { zodToDomainOrThrow } from '../misc';
import { Tagged } from './Tagged';

export type PositiveInteger = Tagged<'PositiveInteger', number>;

export const positiveIntegerParser = z
  .number()
  .int()
  .positive()
  .transform((x) => x as PositiveInteger);

export const toPositiveInteger = (x: number): PositiveInteger =>
  zodToDomainOrThrow(z.object({ value: positiveIntegerParser }), { value: x })
    .value;
