import { z } from 'zod';

const yearRegexPart = '[1-2][0-9]{3}'; // Matches YYYY from 1000-2999
const monthRegexPart = '(0[1-9]|1[0-2])'; // Matches MM from 01-12
const dayRegexPart = '(0[1-9]|[12][0-9]|3[01])'; // Matches DD from 01-31

const strictDateRegex = new RegExp(
  `^(${yearRegexPart}-${monthRegexPart}-${dayRegexPart}|${yearRegexPart}-${monthRegexPart}|${yearRegexPart})$`
);

function isValidDateValues(dateString: string): boolean {
  const parts = dateString.split('-');

  if (parts.length === 3) {
    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10);
    const day = parseInt(parts[2], 10);
    const date = new Date(Date.UTC(year, month - 1, day));

    return (
      date.getUTCFullYear() === year &&
      date.getUTCMonth() === month - 1 &&
      date.getUTCDate() === day
    );
  }

  return true;
}

const comprehensiveDateStringSchema = z
  .string()
  .regex(strictDateRegex, {
    message:
      'Invalid date string format. Expected YYYY, YYYY-MM, or YYYY-MM-DD ' +
      '(e.g., 2023, 2023-04, 2023-04-20).\n' +
      'Month must be 01-12, day 01-31, year 1000-2999.',
  })
  .refine(isValidDateValues, {
    message:
      'Invalid date values. For example, day is out of range for the given month and year\n' +
      '(e.g., 2023-02-30 is invalid).',
  });

const parseDate = (date: Date) => {
  if (isNaN(date.getTime())) {
    return 'Invalid Date Object';
  }

  // Get UTC date components to avoid timezone issues
  const year = date.getUTCFullYear();

  // Explicitly check year range for Date objects
  if (year < 1000 || year > 2999) {
    return 'Year out of range';
  }

  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Month is 0-indexed
  const day = String(date.getUTCDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

export const isoDateParser = z
  .preprocess((arg) => {
    if (arg instanceof Date) {
      return parseDate(arg);
    }

    if (typeof arg === 'string') {
      return parseDate(new Date(arg));
    }

    return arg;
  }, comprehensiveDateStringSchema)
  .brand('iso_date');

export type IsoDate = z.infer<typeof isoDateParser>;
