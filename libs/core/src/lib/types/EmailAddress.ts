import { z } from 'zod';

import { Tagged } from './Tagged';

export type EmailAddress = Tagged<'EmailAddress', string>;

enum Errors {
  Required = 'Required',
  Invalid = 'Invalid',
}

const parser = z
  .string({
    invalid_type_error: Errors.Invalid,
    required_error: Errors.Required,
  })
  .email({ message: Errors.Invalid })
  .transform((x) => x as EmailAddress);

const parse = parser.parse;

export const EmailAddress = {
  parse,
  parser,
  Errors,
}
