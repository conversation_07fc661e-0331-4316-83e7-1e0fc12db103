import { z } from 'zod';

export type UUID = string & { readonly __UUID4: true };

enum Errors {
  NotAString = 'NotAString',
  Invalid = 'InvalidUUID',
}

const parser = <T extends UUID>() =>
  z
    .string({
      required_error: Errors.NotAString,
      invalid_type_error: Errors.NotAString,
    })
    .uuid({ message: Errors.Invalid })
    .transform((x) => x as T);

const generate = <T extends UUID>() => crypto.randomUUID() as T;

export const UUID = {
  parser,
  generate,
  Errors,
};
