import { z } from 'zod';
import { nanoid } from 'nanoid';

export type NanoId = string & { readonly NanoId: true };

const SYMBOLS_COUNT = 21;

enum Errors {
  Required = 'Required',
  Invalid = 'InvalidNanoId',
}

const parser = <T extends NanoId, P extends string>(prefix = '' as P) =>
  z
    .string({
      required_error: Errors.Required,
      invalid_type_error: Errors.Invalid,
    })
    .refine(
      (x) => {
        const hasPrefix = x.startsWith(`${prefix}_`);
        const hasLength = x.length === SYMBOLS_COUNT + prefix.length + 1;

        return hasPrefix && hasLength;
      },
      { message: Errors.Invalid }
    )
    .transform((x) => x as T & { __prefix: P });

const generate = <T extends NanoId, P extends string>(prefix = '' as P) => {
  const id = nanoid(SYMBOLS_COUNT);
  return prefix
    ? (`${prefix}_${id}` as T & { __prefix: P })
    : (id as T & { __prefix: P });
};

export const isIdOfType = <T extends string>(
  prefix: T,
  value: string
): value is NanoId & { __prefix: typeof prefix } => {
  return parser(prefix).safeParse(value).success;
};

export const NanoId = {
  parser,
  generate,
  Errors,
};
