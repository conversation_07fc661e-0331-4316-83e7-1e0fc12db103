import { ZodError, z } from 'zod';

import { CustomEnum } from './Enum';
import { GenericRecord } from './GenericRecord';

export enum ErrorTypes {
  ParsingError = 'ParsingError',
  UnexpectedError = 'UnexpectedError',
  DomainError = 'DomainError',
}

/* Those could be translatable keys for the FE*/
export enum ErrorMessages {
  InvalidFields = 'InvalidFields',
  UnexpectedError = 'UnexpectedError',
  EntityAlreadyExists = 'EntityAlreadyExists',
  EntityNotFound = 'EntityNotFound',
  InvalidParam = 'InvalidParameter',
  Unauthorized = 'Unauthorized',
  Forbidden = 'Forbidden',
  MultipleEntitiesFound = 'MultipleEntitiesFound',
  AuthCodeExpired = 'AuthCodeExpired',
  InvalidAction = 'InvalidAction',
}

const contextParser = z.union([z.string(), z.record(z.any())]);

const parsingErrors = z.record(
  z.union([z.array(z.string()), z.literal(undefined)])
);

type ErrorContext = z.infer<typeof contextParser>;

abstract class ErrorWithContext extends Error {
  readonly context: ErrorContext[] = [];

  constructor(context?: ErrorContext | ErrorContext[]) {
    super();

    if (context) {
      if (Array.isArray(context)) {
        this.addContext(...context);
      } else {
        this.addContext(context);
      }
    }
  }

  addContext(...contexts: ErrorContext[]) {
    contexts.forEach((ctx) => this.context.push(ctx));
    return this;
  }
}

const zodParsingError = z.object({
  type: z.literal(ErrorTypes.ParsingError),
  message: CustomEnum.parser(ErrorMessages),

  errors: parsingErrors.default({}),

  context: z.array(contextParser).default([]),
});

type ZodParsingError = z.infer<typeof zodParsingError>;

export class ParsingError<
    T extends GenericRecord<string[] | undefined> = GenericRecord<string[]>
  >
  extends ErrorWithContext
  implements ZodParsingError
{
  readonly type = ErrorTypes.ParsingError;
  override readonly message: ErrorMessages = ErrorMessages.InvalidFields;

  readonly errors = {} as T;

  hasError(key: keyof T): boolean {
    return key in this.errors;
  }

  override toString(): string {
    return JSON.stringify(this.errors);
  }

  constructor(
    errors: ZodError | GenericRecord<string[] | undefined>,
    context?: ErrorContext | ErrorContext[]
  ) {
    super(context);

    if (errors instanceof ZodError) {
      this.errors = errors.flatten().fieldErrors as T;
    } else {
      this.errors = errors as T;
    }
  }
}

const zodDomainError = z.object({
  type: z.literal(ErrorTypes.DomainError),
  message: CustomEnum.parser(ErrorMessages),

  context: z.array(contextParser).default([]),
});

type ZodDomainError = z.infer<typeof zodDomainError>;

export class DomainError<T extends ErrorMessages = ErrorMessages>
  extends ErrorWithContext
  implements ZodDomainError
{
  readonly type = ErrorTypes.DomainError;

  readonly errors = {};

  constructor(
    public override message: T,
    context?: ErrorContext | ErrorContext[]
  ) {
    super(context);
  }
}

const zodUnexpectedError = z.object({
  type: z.literal(ErrorTypes.UnexpectedError),
  message: CustomEnum.parser(ErrorMessages),

  originalError: z.unknown().optional(),

  context: z.array(contextParser).default([]),
});

type ZodUnexpectedError = z.infer<typeof zodUnexpectedError>;

export class UnexpectedError
  extends ErrorWithContext
  implements ZodUnexpectedError
{
  readonly type = ErrorTypes.UnexpectedError;
  override readonly message = ErrorMessages.UnexpectedError;

  constructor(
    public originalError?: unknown,
    context?: ErrorContext | ErrorContext[]
  ) {
    super(context);
  }
}

const errorInstanceParser = z
  .discriminatedUnion('type', [
    zodDomainError,
    zodUnexpectedError,
    zodParsingError,
  ])
  .transform((data) => {
    switch (data.type) {
      case ErrorTypes.DomainError:
        return new DomainError(data.message, data.context);
      case ErrorTypes.ParsingError:
        return new ParsingError(data.errors, data.context);
      case ErrorTypes.UnexpectedError:
        return new UnexpectedError(data.originalError, data.context);
    }
  });

export type CustomError = z.infer<typeof errorInstanceParser>;

export const CustomError = {
  toError: (data: unknown) => {
    if (CustomError.isErrorInstance(data)) {
      return data;
    }

    const parsed = errorInstanceParser.safeParse(data);

    if (parsed.success) {
      return parsed.data;
    }

    return new UnexpectedError(data);
  },

  isErrorInstance: (data: unknown): data is CustomError => {
    return (
      data instanceof DomainError ||
      data instanceof ParsingError ||
      data instanceof UnexpectedError
    );
  },

  isParsingError: (data: unknown): data is ParsingError => {
    return data instanceof ParsingError;
  },

  isUnexpectedError: (data: unknown): data is UnexpectedError => {
    return data instanceof UnexpectedError;
  },

  isDomainError: <T extends ErrorMessages>(
    data: unknown,
    type?: T
  ): data is DomainError<T> => {
    const parsed = CustomError.toError(data);

    return parsed instanceof DomainError && (!type || parsed.message === type);
  },
};
