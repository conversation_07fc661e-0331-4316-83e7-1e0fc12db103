import { z } from 'zod';

export type Url<T extends string = string> = string & {
  readonly __URL: true;
  readonly __id: T;
};

export enum Errors {
  Required = 'Required',
  NotAString = 'NotAString',
  NotAUrl = 'NotAUrl',
}

export const parser = <T extends string = string>(_id = '' as T) =>
  z
    .string({
      required_error: Errors.Required,
      invalid_type_error: Errors.NotAString,
    })
    .url(Errors.NotAUrl)
    .transform((data) => data as Url<typeof _id>);

export const parse = parser().parse;
