import { z } from 'zod';
import { GenericRecord, is<PERSON><PERSON><PERSON>, StrictOmit } from '../types';

export const omit = <Field extends keyof T, T extends GenericRecord>(
  fieldName: Field,
  source: T
): StrictOmit<T, Field> => {
  return Object.entries(source).reduce((acc, [key, value]) => {
    if (key === fieldName) {
      return acc;
    }
    acc[key] = value;
    return acc;
  }, {} as GenericRecord) as StrictOmit<T, Field>;
};

export const pick = <Field extends keyof T, T extends GenericRecord>(
  fieldNames: Array<Field>,
  source: T
): Pick<T, Field> => {
  return Object.entries(source).reduce((acc, [key, value]) => {
    if (fieldNames.includes(key as Field)) {
      acc[key] = value;
    }
    return acc;
  }, {} as GenericRecord) as Pick<T, Field>;
};

export const capitalize = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const startCase = (str: string) => {
  return str
    .split(' ')
    .filter((word) => word)
    .map((word) => capitalize(word))
    .join(' ');
};

export const isNil = (value: unknown) => {
  return value === null || value === undefined;
};

export const truncate = (str: string, length: number) => {
  return str.length > length ? str.slice(0, length) + '...' : str;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const get = (path: string | string[], obj: unknown): any => {
  if (!isRecord(obj)) {
    return undefined;
  }

  const parts = Array.isArray(path) ? path : path.split('.');
  if (parts.length == 1) {
    return obj[parts[0]];
  }

  if (typeof obj[parts[0]] !== 'object') {
    return undefined;
  }

  return get(parts.slice(1).join('.'), obj[parts[0]] as GenericRecord);
};

export const sample = <T>(arr: T[]): T | undefined => {
  return arr[Math.floor(Math.random() * arr.length)];
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const set = (obj: unknown, path: string, value: unknown): any => {
  const parts = path.split('.');

  if (parts.length === 0 || !isRecord(obj)) {
    throw new Error('Invalid path or object');
  }

  return parts.reduce((acc, key, index) => {
    const isLast = index === parts.length - 1;

    if (isLast) {
      acc[key] = value;
      return acc;
    } else {
      acc[key] = isRecord(acc[key]) ? acc[key] : {};

      return set(acc[key], parts.slice(1).join('.'), value);
    }
  }, obj);
};

export const must = <T>(value: T): NonNullable<T> => {
  if (value === null || value === undefined) {
    throw new Error('Value is null or undefined');
  }

  return value as NonNullable<T>;
};

export const moveArrayItem = <T>(arr: T[], from: number, to: number): T[] => {
  const cloned = arr.slice();
  const item = cloned[from];
  cloned.splice(from, 1);
  cloned.splice(to, 0, item);

  return cloned;
};

export const safeParseJson = <T>(str: unknown, schema?: z.ZodAny): T | null => {
  try {
    const asJson = JSON.parse(String(str));

    return schema ? schema.parse(asJson) : asJson;
  } catch {
    return null;
  }
};

export function objectKeys<T extends Record<string, unknown>>(
  obj: T
): Array<keyof T> {
  return Object.keys(obj) as Array<keyof T>;
}
