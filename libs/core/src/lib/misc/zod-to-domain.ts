import { z } from 'zod';

import { GenericRecord, ParsingError } from '../types';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type UnknownZodType<T> = z.ZodType<T, any, unknown>;

export const zodToDomain = <T extends GenericRecord>(
  schema: UnknownZodType<T>,
  data: unknown
) => {
  const result = schema.safeParse(data);

  if (result.success) {
    return result.data;
  }

  return new ParsingError<ToError<T>>(result.error);
};

export const zodToDomainOrThrow = <T extends GenericRecord>(
  schema: UnknownZodType<T>,
  data: unknown
) => {
  const result = schema.safeParse(data);

  if (result.success) {
    return result.data;
  }

  console.log(result.error);

  throw new ParsingError<ToError<T>>(result.error);
};

export type ToError<T extends Record<string, unknown>> = Record<
  keyof T,
  string[]
>;
