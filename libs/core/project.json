{"name": "core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/core/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/libs/core"}}}, "tags": []}