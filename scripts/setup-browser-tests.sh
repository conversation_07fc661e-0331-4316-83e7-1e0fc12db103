#!/bin/bash

# Setup script for browser tests in CI/Docker environments

set -e

echo "🚀 Setting up browser tests for CI..."

# Check if we're in CI
if [ "$CI" = "true" ]; then
    echo "📦 CI environment detected"
    
    # Install system dependencies for Chromium (Ubuntu/Debian)
    if command -v apt-get &> /dev/null; then
        echo "🔧 Installing system dependencies..."
        sudo apt-get update
        sudo apt-get install -y \
            libnss3 \
            libatk-bridge2.0-0 \
            libdrm2 \
            libxkbcommon0 \
            libxcomposite1 \
            libxdamage1 \
            libxrandr2 \
            libgbm1 \
            libxss1 \
            libasound2
    fi
fi

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
if [ "$CI" = "true" ]; then
    # In CI, install with system dependencies
    npx playwright install --with-deps chromium
else
    # Local development
    npx playwright install chromium
fi

echo "✅ Browser test setup complete!"

# Verify installation
echo "🔍 Verifying Chromium installation..."
npx playwright --version

echo "🎯 Ready to run browser tests!"
echo "   Local: npx nx test:browser codashi"
echo "   CI:    npx nx test:browser:ci codashi"
